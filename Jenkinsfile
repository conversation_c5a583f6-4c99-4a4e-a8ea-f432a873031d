pipeline {
    agent { label 'Second_Slave' }
    environment {
        NEXUS_COMMON_CREDS = credentials('0981d455-e100-4f93-9faf-151ac7e29d8a')
        NEXUS_URL = 'http://*************:8081'
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '10'))
	        office365ConnectorWebhooks([[
                name: '<PERSON>',
                notifyBackToNormal: true,
                notifyFailure: true,
                notifySuccess: true,
                notifyUnstable: true,
                url: "https://healsoftwareai.webhook.office.com/webhookb2/78345e71-2972-44c4-a270-fbae82662bf1@55dca2af-e23a-4402-b9a6-8833b28a02dc/JenkinsCI/7958868126734afeb78edb01dafdcc05/6fed72e3-b7dd-422f-9075-e6d96468feb0"
            ]]
        )
    }
    parameters {
        gitParameter branch: '', branchFilter: 'origin/(.*)', defaultValue: 'develop', name: 'API_BRANCH', quickFilterEnabled: true, type: 'PT_BRANCH_TAG', useRepository: '.*appsone-controlcenter.git'
        gitParameter branch: '', branchFilter: 'origin/(.*)', defaultValue: 'develop', name: 'UI_BRANCH', quickFilterEnabled: true, type: 'PT_BRANCH_TAG', useRepository: '.*appsone-ui-controlcenter.git'
    }
    tools {
         nodejs 'NodeJs12'
          }
    stages {
        stage('Checkout') {
            parallel {
                stage('appsone-controlcenter checkout') {
                    steps {
                        script {
                            currentBuild.displayName = "#${BUILD_NUMBER}->${params.API_BRANCH}->${params.UI_BRANCH}"
                            currentBuild.description = "API branch: ${params.API_BRANCH} and UI branch: ${params.UI_BRANCH} is used for this build"
                        }
                        dir('heal-controlcenter') {
                            git branch: "${params.API_BRANCH}", url: 'https://<EMAIL>/appsone/appsone-controlcenter.git', credentialsId: "fd197b00-fd06-4632-a018-36134111e086"
                        }
                    }
                }
                stage('appsone-ui-controlcenter checkout') {
                    steps {
                        dir('heal-ui-controlcenter') {
                            git branch: "${params.UI_BRANCH}", url: 'https://<EMAIL>/appsone/appsone-ui-controlcenter.git', credentialsId: "fd197b00-fd06-4632-a018-36134111e086"
                        }
                    }
                }
            }
        }
        stage('Build UI') {
            steps {
                dir('heal-ui-controlcenter') {
                    sh 'npm install'
                    sh 'npm run test'
                    sh 'npm run build'
                }
            }
        }
        stage('Build jar & Sonarqube analysis') {
            steps {
                dir('heal-controlcenter') {
                    withSonarQubeEnv('sonarqube_71') {
			             sh 'mvn clean deploy -U sonar:sonar'
                    }
                }
            }
        }
        stage('Copy ui to cc') {
            steps {
                sh '''
                    mkdir -p build
                    mv heal-controlcenter/target/heal-controlcenter*.tar.gz build/heal-controlcenter.tar.gz
                    cd build/
                    tar -xvf heal-controlcenter.tar.gz
                    rm -f heal-controlcenter.tar.gz
                    mkdir -p heal-controlcenter/ui/public/heal-controlcenter/
                    cp -r ../heal-ui-controlcenter/dist/* heal-controlcenter/ui/public/heal-controlcenter/
                    tar -czf heal-controlcenter.tar.gz heal-controlcenter
                    mv heal-controlcenter.tar.gz ../heal-controlcenter/
                    rm -rf heal-controlcenter/
                '''
            }
        }
        stage('Archive Build') {
            steps {
                dir('heal-controlcenter') {
                    archiveArtifacts artifacts: 'heal-controlcenter.tar.gz', fingerprint: true
                }
            }
        }
        stage('Docker build') {
            steps {
                dir('heal-controlcenter') {
                    sh "tar -xvf heal-controlcenter.tar.gz"
                    script {
                        pom = readMavenPom file: 'pom.xml'
                        version = pom.version
                    }
                    echo "Building project in version: ${version}"
                    sh "docker build -t heal-controlcenter:${version} ."
                }
            }
        }
        stage('Publish Docker Image') {
            steps {
                sh "docker save heal-controlcenter:${version} > heal-controlcenter_${version}.tar"
                sh "curl -v -u ${NEXUS_COMMON_CREDS} --upload-file heal-controlcenter_${version}.tar ${NEXUS_URL}/nexus/repository/tls_docker_images/heal-controlcenter_${version}.tar"
                sh "echo heal-controlcenter_${version} > /tmp/heal-controlcenter_version"
            }
            post {
                always {
                    sh "docker rmi -f heal-controlcenter:${version}"
                }
            }
        }
        stage('Cleanup') {
            steps {
                cleanWs()
            }
        }
    }
}