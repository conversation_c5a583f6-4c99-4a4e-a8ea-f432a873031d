import com.appnomic.appsone.controlcenter.businesslogic.ActionsTest;
import com.appnomic.appsone.controlcenter.manager.MySQLConnectionManager;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.junit.runners.Suite;

@RunWith(Suite.class)

@Suite.SuiteClasses({
        ActionsTest.class,
        /*CategoryTest.class,
        ImportConnectionTest.class,
        ImportFileServiceTest.class,*/
        /*InstanceHealthDataTest.class,
        MaintenanceWindowBLTest.class,
        AgentDaoTest.class,
        ApplicationNotificationDaoTest.class,
        CategoryDetailsDaoTest.class,
        CommandAuditDaoTest.class,
        ComponentAgentDaoTest.class,
        ComponentInstanceDaoTest.class,
        ImportServicesDaoTest.class,
        KPIDataDaoTest.class,
        NotificationSettingsDaoTest.class,
        ActionsDataServiceTest.class,
        CategoryDetailsDataServiceTest.class,
        CommandDataServiceTest.class,
        ImportServicesDataServiceTest.class,
        KPIDataServiceTest.class,
        NotificationPreferencesDataServiceTest.class,
        NotificationSettingsDataServiceTest.class,
        AgentStatusServiceTest.class,
        ApplicationNotificationServiceTest.class,
        NotificationSettingsServiceTest.class,
        ApplicationNotificationTest.class,
        NotificationSettingsTest.class*/
})

public class TestSuite {

    @BeforeClass
    public static void setUpClass() {
        System.out.println("Master setup");
        MySQLConnectionManager.getInstance().setHaveToRunTestCases(true);
        MySQLConnectionManager.getInstance().getHandle();

        try {
            Thread.sleep(30000);
        } catch (InterruptedException e) {
            System.out.println(e);
        }
    }
}
