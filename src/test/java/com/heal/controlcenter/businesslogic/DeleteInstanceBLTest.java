package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ComponentInstanceBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.entity.ComponentInstanceDao;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.DeleteInstancePojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DeleteInstanceBLTest {

    @InjectMocks
    DeleteInstanceBL deleteInstanceBL;

    @Mock
    ComponentInstanceDao componentInstanceDao;
    @Mock
    InstanceRepo instanceRepo;
    @Mock
    ServerValidationUtils serverValidationUtils;
    @Mock
    ClientValidationUtils clientValidationUtils;

    private DeleteInstancePojo deleteRequest;
    private Account account;
    private ComponentInstanceBean componentInstance;
    private BasicUserDetails userDetails;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        deleteRequest = new DeleteInstancePojo();
        deleteRequest.setInstances(List.of("instance-123"));

        account = new Account();
        account.setId(1);
        account.setIdentifier("test-account");
        account.setName("Test Account");

        componentInstance = ComponentInstanceBean.builder()
                .id(101)
                .identifier("instance-123")
                .name("Test Instance")
                .accountId(1)
                .status(1)
                .build();

        userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("user-123");
    }

    @Test
    void testClientValidationSuccess() throws Exception {
        String accountIdentifier = "test-account";

        UtilityBean<DeleteInstancePojo> result = deleteInstanceBL.clientValidation(deleteRequest, accountIdentifier, userDetails);

        assertNotNull(result);
        assertEquals(deleteRequest, result.getPojoObject());
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals(false, result.getMetadata().get(Constants.HARD_DELETE));
    }

    @Test
    void testClientValidationWithNullRequest() {
        String accountIdentifier = "test-account";

        assertThrows(ClientException.class, () -> {
            deleteInstanceBL.clientValidation(null, accountIdentifier, userDetails);
        });
    }

    @Test
    void testClientValidationWithEmptyInstanceIdentifiers() {
        String accountIdentifier = "test-account";
        deleteRequest.setInstances(new ArrayList<>());

        assertThrows(ClientException.class, () -> {
            deleteInstanceBL.clientValidation(deleteRequest, accountIdentifier, userDetails);
        });
    }

    @Test
    void testServerValidationSuccess() throws Exception {
        String userId = "user-1";
        String accountIdentifier = "test-account";

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, userId);
        metadata.put(Constants.HARD_DELETE, false);

        UtilityBean<DeleteInstancePojo> inputBean = UtilityBean.<DeleteInstancePojo>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .pojoObject(deleteRequest)
                .metadata(metadata)
                .build();

        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(account);
        when(componentInstanceDao.getComponentInstancesByIdentifiers(deleteRequest.getInstances(), account.getId()))
                .thenReturn(List.of(componentInstance));

        UtilityBean<DeleteInstancePojo> result = deleteInstanceBL.serverValidation(inputBean);

        assertNotNull(result);
        assertEquals(account, result.getMetadata().get(Constants.ACCOUNT));

        verify(serverValidationUtils).accountValidation(accountIdentifier);
        verify(componentInstanceDao).getComponentInstancesByIdentifiers(deleteRequest.getInstances(), account.getId());
    }

    @Test
    void testServerValidationWithNonExistentInstance() throws Exception {
        String userId = "user-1";
        String accountIdentifier = "test-account";

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, userId);
        metadata.put(Constants.HARD_DELETE, false);

        UtilityBean<DeleteInstancePojo> inputBean = UtilityBean.<DeleteInstancePojo>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .pojoObject(deleteRequest)
                .metadata(metadata)
                .build();

        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(account);
        when(componentInstanceDao.getComponentInstancesByIdentifiers(deleteRequest.getInstances(), account.getId()))
                .thenReturn(new ArrayList<>());

        assertThrows(ServerException.class, () -> {
            deleteInstanceBL.serverValidation(inputBean);
        });

        verify(serverValidationUtils).accountValidation(accountIdentifier);
        verify(componentInstanceDao).getComponentInstancesByIdentifiers(deleteRequest.getInstances(), account.getId());
    }

    @Test
    void testProcessSoftDeleteSuccess() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.HARD_DELETE, false);
        metadata.put(Constants.ACCOUNT, account);

//        UtilityBean<DeleteInstancePojo> inputBean = UtilityBean.<List<ComponentInstanceBean>>builder()
//                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, account.getIdentifier()))
//                .pojoObject(List.of(componentInstance))
//                .metadata(metadata)
//                .build();

        when(componentInstanceDao.softDeleteComponentInstances(List.of(componentInstance.getIdentifier()), account.getId()))
                .thenReturn(1);
        doNothing().when(instanceRepo).removeInstance(account.getIdentifier(), componentInstance.getIdentifier());

//        String result = deleteInstanceBL.process(inputBean);

//        assertNotNull(result);
//        assertTrue(result.contains("soft deleted"));
//        assertTrue(result.contains("1 component instance"));
        verify(instanceRepo).removeInstance(account.getIdentifier(), componentInstance.getIdentifier());
    }

    @Test
    void testProcessHardDeleteSuccess() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.HARD_DELETE, true);
        metadata.put(Constants.ACCOUNT, account);

        UtilityBean<List<ComponentInstanceBean>> inputBean = UtilityBean.<List<ComponentInstanceBean>>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, account.getIdentifier()))
                .pojoObject(List.of(componentInstance))
                .metadata(metadata)
                .build();

        when(componentInstanceDao.hardDeleteComponentInstances(List.of(componentInstance.getIdentifier()), account.getId()))
                .thenReturn(1);
        doNothing().when(instanceRepo).removeInstance(account.getIdentifier(), componentInstance.getIdentifier());

//        String result = deleteInstanceBL.process(inputBean);

//        assertNotNull(result);
//        assertTrue(result.contains("hard deleted"));
//        assertTrue(result.contains("1 component instance"));
        verify(instanceRepo).removeInstance(account.getIdentifier(), componentInstance.getIdentifier());
    }

    @Test
    void testProcessWithDaoException() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.HARD_DELETE, false);
        metadata.put(Constants.ACCOUNT, account);

        UtilityBean<List<ComponentInstanceBean>> inputBean = UtilityBean.<List<ComponentInstanceBean>>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, account.getIdentifier()))
                .pojoObject(List.of(componentInstance))
                .metadata(metadata)
                .build();

        when(componentInstanceDao.softDeleteComponentInstances(List.of(componentInstance.getIdentifier()), account.getId()))
                .thenThrow(new HealControlCenterException("Database error"));

//        DataProcessingException exception = assertThrows(DataProcessingException.class, () -> {
//            deleteInstanceBL.process(inputBean);
//        });

//        assertTrue(exception.getMessage().contains("Failed to delete component instances"));
//        verify(componentInstanceDao).softDeleteComponentInstances(List.of(componentInstance.getIdentifier()), account.getId());
    }

    @Test
    void testProcessWithZeroDeletedInstances() throws Exception {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.HARD_DELETE, false);
        metadata.put(Constants.ACCOUNT, account);

        UtilityBean<List<ComponentInstanceBean>> inputBean = UtilityBean.<List<ComponentInstanceBean>>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, account.getIdentifier()))
                .pojoObject(List.of(componentInstance))
                .metadata(metadata)
                .build();

        when(componentInstanceDao.softDeleteComponentInstances(List.of(componentInstance.getIdentifier()), account.getId()))
                .thenReturn(0);

//        DataProcessingException exception = assertThrows(DataProcessingException.class, () -> {
//            deleteInstanceBL.process(inputBean);
//        });

//        assertTrue(exception.getMessage().contains("No component instances were deleted"));
        verify(componentInstanceDao).softDeleteComponentInstances(List.of(componentInstance.getIdentifier()), account.getId());
    }
}
