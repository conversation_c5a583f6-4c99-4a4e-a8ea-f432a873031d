package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ProducerKpiValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ProducersDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ProducerKpiMappingPojo;
import com.heal.controlcenter.pojo.ProducerMappedKpiDetailsPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.pojo.BasicUserDetails;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class GetProducerMappedKpisBLTest {

    @Mock
    private ProducersDao producersDao;
    @Mock
    private ClientValidationUtils clientValidationUtils;
    @Mock
    private ServerValidationUtils serverValidationUtils;

    @InjectMocks
    private GetProducerMappedKpisBL getProducerMappedKpisBL;

    private BasicUserDetails userDetails;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("user1");
    }

    @Test
    void clientValidation_success() throws Exception {
        String accountIdentifier = "acc1";
        String producerId = "123";
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);

        // Fix argument order to match actual method signature
        UtilityBean<Void> result = getProducerMappedKpisBL.clientValidation(accountIdentifier, producerId, userDetails);

        assertNotNull(result);
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals(producerId, result.getRequestParams().get("producerId"));
    }

    @Test
    void clientValidation_invalidProducerId() throws ClientException {
        String accountIdentifier = "acc1";
        String producerId = "abc";
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);

        // Fix argument order to match actual method signature
        assertThrows(ClientException.class, () ->
                getProducerMappedKpisBL.clientValidation(accountIdentifier, producerId, userDetails));
    }

    @Test
    void serverValidation_success() throws Exception {
        String accountIdentifier = "acc1";
        String producerId = "123";
        Account account = new Account();
        account.setId(1);
        when(serverValidationUtils.accountValidation(accountIdentifier)).thenReturn(account);

        UtilityBean<Void> clientBean = UtilityBean.<Void>builder()
                .requestParams(new HashMap<String, String>() {{
                    put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
                    put("producerId", producerId);
                }})
                .metadata(new HashMap<>())
                .build();

        UtilityBean<ProducerKpiValidationBean> result = getProducerMappedKpisBL.serverValidation(clientBean);

        assertNotNull(result);
        assertEquals(account, result.getPojoObject().getAccount());
        assertEquals(Integer.valueOf(producerId), result.getPojoObject().getProducerId());
    }

    @Test
    void serverValidation_invalidProducerId() {
        String accountIdentifier = "acc1";
        String producerId = "abc";
        UtilityBean<Void> clientBean = UtilityBean.<Void>builder()
                .requestParams(new HashMap<String, String>() {{
                    put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
                    put("producerId", producerId);
                }})
                .metadata(new HashMap<>())
                .build();

        assertThrows(ServerException.class, () ->
                getProducerMappedKpisBL.serverValidation(clientBean));
    }

    @Test
    void process_success() throws Exception {
        Account account = new Account();
        account.setId(1);
        ProducerKpiValidationBean validationBean = ProducerKpiValidationBean.builder()
                .account(account)
                .producerId(123)
                .build();
        UtilityBean<ProducerKpiValidationBean> utilityBean = UtilityBean.<ProducerKpiValidationBean>builder()
                .pojoObject(validationBean)
                .pageable(PageRequest.of(0, 10))
                .build();

        ProducerMappedKpiDetailsPojo mappedKpi = new ProducerMappedKpiDetailsPojo();
        mappedKpi.setProducerId(123);
        mappedKpi.setKpiName("KPI1");
        ProducerKpiMappingPojo mappingPojo = ProducerKpiMappingPojo.builder()
                .producerId(123)
                .producerName("Producer1")
                .mappedKpis(Collections.singletonList(mappedKpi))
                .build();

        when(producersDao.getProducerMappedKpis(anyInt(), anyInt(), any(Pageable.class)))
                .thenReturn(new PageImpl<>(Collections.singletonList(mappedKpi), PageRequest.of(0, 10), 1L));
        when(producersDao.getProducerNameById(anyInt())).thenReturn("Producer1");

        Page<ProducerKpiMappingPojo> result = getProducerMappedKpisBL.process(utilityBean);

        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("Producer1", result.getContent().get(0).getProducerName());
        assertEquals(1, result.getContent().get(0).getMappedKpis().size());
        assertEquals("KPI1", result.getContent().get(0).getMappedKpis().get(0).getKpiName());
    }

    @Test
    void process_emptyResult() throws Exception {
        Account account = new Account();
        account.setId(1);
        ProducerKpiValidationBean validationBean = ProducerKpiValidationBean.builder()
                .account(account)
                .producerId(123)
                .build();
        UtilityBean<ProducerKpiValidationBean> utilityBean = UtilityBean.<ProducerKpiValidationBean>builder()
                .pojoObject(validationBean)
                .pageable(PageRequest.of(0, 10))
                .build();

        ProducerKpiMappingPojo mappingPojo = ProducerKpiMappingPojo.builder()
                .producerId(123)
                .producerName("Producer1")
                .mappedKpis(Collections.emptyList())
                .build();

        when(producersDao.getProducerMappedKpis(anyInt(), anyInt(), any(Pageable.class)))
                .thenReturn(new PageImpl<>(Collections.emptyList(), PageRequest.of(0, 10), 0L));
        when(producersDao.getProducerNameById(anyInt())).thenReturn("Producer1");

        Page<ProducerKpiMappingPojo> result = getProducerMappedKpisBL.process(utilityBean);

        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
    }

    @Test
    void process_exception() throws Exception {
        Account account = new Account();
        account.setId(1);
        ProducerKpiValidationBean validationBean = ProducerKpiValidationBean.builder()
                .account(account)
                .producerId(123)
                .build();
        UtilityBean<ProducerKpiValidationBean> utilityBean = UtilityBean.<ProducerKpiValidationBean>builder()
                .pojoObject(validationBean)
                .pageable(PageRequest.of(0, 10))
                .build();

        when(producersDao.getProducerMappedKpis(anyInt(), anyInt(), any(Pageable.class)))
                .thenThrow(new RuntimeException("DB error"));

        assertThrows(DataProcessingException.class, () ->
                getProducerMappedKpisBL.process(utilityBean));
    }
}
