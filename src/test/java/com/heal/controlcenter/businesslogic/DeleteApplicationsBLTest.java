package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.pojo.DeleteApplicationsPojo;
import com.heal.controlcenter.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DeleteApplicationsBLTest {

    @InjectMocks
    DeleteApplicationsBL deleteApplicationsBL;

    @Mock
    ControllerDao controllerDao;
    @Mock
    CommonUtils commonUtils;
    @Mock
    ApplicationRepo applicationRepo;
    @Mock
    UserValidationUtil userValidationUtil;
    @Mock
    ServerValidationUtils serverValidationUtils;
    @Mock
    ClientValidationUtils clientValidationUtils;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testClientValidationSuccess() throws Exception {
        DeleteApplicationsPojo pojo = new DeleteApplicationsPojo();
        pojo.setHardDelete(true);
        pojo.setApplicationIdentifiers(List.of("app-1"));

        String accountIdentifier = "test-account";

        UtilityBean<DeleteApplicationsPojo> result = deleteApplicationsBL.clientValidation(pojo, accountIdentifier);

        assertNotNull(result);
        assertEquals(pojo, result.getPojoObject());
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals(true, result.getMetadata().get(Constants.HARD_DELETE));
    }

    @Test
    @Disabled
    void testServerValidationWithValidAppAndHardDelete() throws Exception {
        String userId = "user-1";
        String accountIdentifier = "test-account";

        DeleteApplicationsPojo pojo = new DeleteApplicationsPojo();
        pojo.setApplicationIdentifiers(List.of("app-1"));
        pojo.setHardDelete(true);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.USER_ID_KEY, userId);
        metadata.put(Constants.HARD_DELETE, true);

        UtilityBean<DeleteApplicationsPojo> inputBean = UtilityBean.<DeleteApplicationsPojo>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .pojoObject(pojo)
                .metadata(metadata)
                .build();

        Account mockAccount = new Account();
        mockAccount.setId(1);

        ControllerBean mockApp = new ControllerBean();
        mockApp.setId(1);
        mockApp.setIdentifier("app-1");
        mockApp.setStatus(1);

        when(serverValidationUtils.accountValidation(any(String.class))).thenReturn(mockAccount);
        when(userValidationUtil.getAccessibleApplicationsForUser(any(String.class), any(String.class))).thenReturn(List.of(mockApp));
        when(controllerDao.getServicesMappedToApplications(any(Integer.class), any(List.class))).thenReturn(List.of());

        // Just verify the method can be called without exception for now
        assertDoesNotThrow(() -> deleteApplicationsBL.serverValidation(inputBean));
    }

    @Test
    void testProcessSoftDelete() throws Exception {
        // Test that the method exists and can be called
        String accountIdentifier = "test-account";

        ControllerBean app = new ControllerBean();
        app.setId(1);
        app.setIdentifier("app-1");
        app.setStatus(1);

        UtilityBean<List<ControllerBean>> input = UtilityBean.<List<ControllerBean>>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .pojoObject(List.of(app))
                .metadata(Map.of(Constants.HARD_DELETE, false))
                .build();

        // Just verify the method can be called without exception
        assertDoesNotThrow(() -> deleteApplicationsBL.process(input));
    }

    @Test
    void testProcessHardDeleteFailure() throws Exception {
        // Test that the method exists and can be called
        String accountIdentifier = "test-account";
        ControllerBean app = new ControllerBean();
        app.setId(1);
        app.setIdentifier("app-1");
        app.setStatus(0);

        UtilityBean<List<ControllerBean>> input = UtilityBean.<List<ControllerBean>>builder()
                .requestParams(Map.of(Constants.ACCOUNT_IDENTIFIER, accountIdentifier))
                .pojoObject(List.of(app))
                .metadata(Map.of(Constants.HARD_DELETE, true))
                .build();

        // Just verify the method can be called without exception
        assertDoesNotThrow(() -> deleteApplicationsBL.process(input));
    }
}
