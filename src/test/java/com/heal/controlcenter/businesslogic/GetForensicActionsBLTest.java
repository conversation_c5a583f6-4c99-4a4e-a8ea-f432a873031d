package com.heal.controlcenter.businesslogic;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.*;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.beans.ForensicActionArgumentsBean;
import com.heal.controlcenter.beans.ForensicActionBean;
import com.heal.controlcenter.beans.UserAccessBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ActionScriptDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ForensicActionsPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
class GetForensicActionsBLTest {

    @InjectMocks
    GetForensicActionsBL getForensicActionsBL;

    @Mock
    ClientValidationUtils clientValidationUtils;

    @Mock
    ActionScriptDao actionScriptDao;

    @Mock
    MasterDataRepo masterDataRepo;

    @Mock
    ServerValidationUtils serverValidationUtils;

    @Mock
    UserDao userDao;

    @Test
    @DisplayName("Should throw exception for missing type in client validation")
    void clientValidation_missingType_throwsException() {
        String[] params = {"account-1", "searchTerm"};
        ClientException exception = assertThrows(ClientException.class, () ->
                getForensicActionsBL.clientValidation(null, params));
        assertTrue(exception.getMessage().contains("type"));
    }

    @Test
    @DisplayName("Should throw exception for invalid type in client validation")
    void clientValidation_invalidType_throwsException() {
        String[] params = {"account-1", "searchTerm", "cmd", "30", null, null, null, "invalid"};
        ClientException exception = assertThrows(ClientException.class, () ->
                getForensicActionsBL.clientValidation(null, params));
        assertTrue(exception.getMessage().contains("must be either"));
    }

    @Test
    @DisplayName("Should validate server input and succeed")
    void serverValidation_validInput_success() throws ServerException {
        UtilityBean<Object> utilityBean = UtilityBean.builder()
                .requestParams(Map.of("accountIdentifier", "acc-123", "type", "standard", "userId", "user1"))
                .metadata(Map.of("userId", "user1"))
                .build();

        Account account = new Account();
        account.setId(101);
        Mockito.when(serverValidationUtils.accountValidation("acc-123")).thenReturn(account);
        Mockito.when(userDao.fetchUserAccessDetailsUsingIdentifier(any())).thenReturn(new UserAccessBean());

        UtilityBean<Integer> result = getForensicActionsBL.serverValidation(utilityBean);
        assertEquals(101, result.getPojoObject());
    }

    @Test
    @DisplayName("Should process valid request and return data")
    void process_validRequest_returnsData() throws DataProcessingException, HealControlCenterException {
        UtilityBean<Integer> utilityBean = buildUtilityBean();
        int subTypeId = 999;

        // mock view type
        ViewTypes vt = ViewTypes.builder()
                .typeName(Constants.STANDARD_TYPE)
                .subTypeName("standard")
                .subTypeId(subTypeId)
                .build();
        Mockito.when(masterDataRepo.getViewTypes()).thenReturn(List.of(vt));

        // mock forensic actions
        ForensicActionBean actionBean = new ForensicActionBean();
        actionBean.setActionId(1);
        actionBean.setActionName("Test Action");
        actionBean.setActionType("custom");
        actionBean.setCommandId(101);
        actionBean.setCommandName("cmd");
        actionBean.setCommandIdentifier("CMD_01");
        actionBean.setCommandTimeoutInSeconds(30);
        actionBean.setSupCtrlTimeoutInSeconds(20);
        actionBean.setSupCtrlRetryCount(2);
        actionBean.setStatus(1);
        actionBean.setLastModifiedBy("admin");
        actionBean.setLastModifiedOn(String.valueOf(new Date()));
        actionBean.setCategoryId(201);
        actionBean.setCategoryName("Category A");
        actionBean.setCategoryIdentifier("cat_a");
        actionBean.setIsCustomCategory(1);

        Mockito.when(actionScriptDao.getForensicActions(anyInt(), any(), any(), any(), any(), any(), any(), any(),any()))
                .thenReturn(List.of(actionBean));
        Mockito.when(actionScriptDao.getForensicActionsCount(anyInt(), any(), any(), any(), any(), any(), any(), eq(subTypeId)))
                .thenReturn(1);

        // mock parameters
        ForensicActionArgumentsBean paramBean = new ForensicActionArgumentsBean();
        paramBean.setCommandId(101);
        paramBean.setId(1);
        paramBean.setType("input");
        paramBean.setArgument_key("arg1");
        paramBean.setValue("val");
        paramBean.setDefaultValue("def");
        paramBean.setValueType("string");

        Mockito.when(actionScriptDao.getForensicsParameters()).thenReturn(List.of(paramBean));

        Page<ForensicActionsPojo> result = getForensicActionsBL.process(utilityBean);

        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("Test Action", result.getContent().get(0).getName());
        assertEquals("cmd", result.getContent().get(0).getCommandName());
    }

    @Test
    @DisplayName("Should throw DataProcessingException for invalid type in process")
    void process_invalidType_throwsDataProcessingException() {
        UtilityBean<Integer> utilityBean = UtilityBean.<Integer>builder()
                .pojoObject(1)
                .requestParams(Map.of("type", "unknown"))
                .pageable(PageRequest.of(0, 10))
                .build();

        Mockito.when(masterDataRepo.getViewTypes()).thenReturn(List.of());

        DataProcessingException exception = assertThrows(DataProcessingException.class, () ->
                getForensicActionsBL.process(utilityBean));
        assertTrue(exception.getMessage().contains("Configuration error"));
    }

    private UtilityBean<Integer> buildUtilityBean() {
        Map<String, String> requestParams = new HashMap<>();
        requestParams.put("searchTerm", "search");
        requestParams.put("commandName", "command");
        requestParams.put("commandTimeoutInSeconds", "10");
        requestParams.put("categoryList", "cat1,cat2");
        requestParams.put("status", "ACTIVE");
        requestParams.put("lastModifiedBy", "admin");
        requestParams.put("type", "standard");

        Pageable pageable = PageRequest.of(0, 10);

        return UtilityBean.<Integer>builder()
                .pojoObject(1)
                .requestParams(requestParams)
                .pageable(pageable)
                .build();
    }
}
