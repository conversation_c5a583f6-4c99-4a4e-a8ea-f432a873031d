package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.ProducerKpiValidationBean;
import com.heal.controlcenter.beans.ProducerValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetProducersBL;
import com.heal.controlcenter.businesslogic.GetProducerMappedKpisBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.GetProducerPojo;
import com.heal.controlcenter.pojo.ProducerKpiMappingPojo;
import com.heal.controlcenter.pojo.ProducerMappedKpiDetailsPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

class ProducersControllerTest {

    @Mock
    private GetProducersBL getProducersBL;
    @Mock
    private GetProducerMappedKpisBL getProducerMappedKpisBL;
    @Mock
    private HealthMetrics healthMetrics;
    @Mock
    private JsonFileParser headersParser;

    @InjectMocks
    private ProducersController controller;

    private BasicUserDetails userDetails;
    private Pageable pageable;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        userDetails = new BasicUserDetails();
        userDetails.setUserIdentifier("user1");
        pageable = PageRequest.of(0, 10);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());
    }

    @Test
    void getProducers_Success() throws Exception {
        UtilityBean<String> clientBean = UtilityBean.<String>builder().metadata(new HashMap<>()).build();
        UtilityBean<ProducerValidationBean> serverBean = UtilityBean.<ProducerValidationBean>builder().build();
        Page<GetProducerPojo> page = new PageImpl<>(Collections.emptyList(), pageable, 0);

        when(getProducersBL.clientValidation(any(), any(), any(), any(), any(), any())).thenReturn(clientBean);
        when(getProducersBL.serverValidation(clientBean)).thenReturn(serverBean);
        when(getProducersBL.process(serverBean)).thenReturn(page);

        ResponseEntity<ResponsePojo<Page<GetProducerPojo>>> response = controller.getProducers(
                "acc", null, null, null, 1, pageable, userDetails);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Producers fetching successful.", response.getBody().getMessage());
        assertEquals(page, response.getBody().getData());
        assertEquals("user1", clientBean.getMetadata().get(Constants.USER_ID_KEY));
    }

    @Test
    void getProducers_ClientException() throws Exception {
        when(getProducersBL.clientValidation(any(), any(), any(), any(), any(), any()))
                .thenThrow(new ClientException("Client error"));

        ResponseEntity<ResponsePojo<Page<GetProducerPojo>>> response = controller.getProducers(
                "acc", null, null, null, 1, pageable, userDetails);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getMessage().contains("Client error"));
    }

    @Test
    void getProducers_ServerException() throws Exception {
        UtilityBean<String> clientBean = UtilityBean.<String>builder().metadata(new HashMap<>()).build();
        when(getProducersBL.clientValidation(any(), any(), any(), any(), any(), any())).thenReturn(clientBean);
        when(getProducersBL.serverValidation(clientBean)).thenThrow(new ServerException("Server error"));

        ResponseEntity<ResponsePojo<Page<GetProducerPojo>>> response = controller.getProducers(
                "acc", null, null, null, 1, pageable, userDetails);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getMessage().contains("Server error"));
    }

    @Test
    void getProducers_DataProcessingException() throws Exception {
        UtilityBean<String> clientBean = UtilityBean.<String>builder().metadata(new HashMap<>()).build();
        UtilityBean<ProducerValidationBean> serverBean = UtilityBean.<ProducerValidationBean>builder().build();
        when(getProducersBL.clientValidation(any(), any(), any(), any(), any(), any())).thenReturn(clientBean);
        when(getProducersBL.serverValidation(clientBean)).thenReturn(serverBean);
        when(getProducersBL.process(serverBean)).thenThrow(new DataProcessingException("Data processing error"));

        ResponseEntity<ResponsePojo<Page<GetProducerPojo>>> response = controller.getProducers(
                "acc", null, null, null, 1, pageable, userDetails);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getMessage().contains("Data processing error"));
    }

    @Test
    void getProducerMappedKpis_Success() throws Exception {
        String accountIdentifier = "acc1";
        Integer producerId = 123;

        UtilityBean<Void> clientBean = UtilityBean.<Void>builder().metadata(new HashMap<>()).build();
        UtilityBean<ProducerKpiValidationBean> serverBean = UtilityBean.<ProducerKpiValidationBean>builder().build();

        ProducerMappedKpiDetailsPojo mappedKpi = new ProducerMappedKpiDetailsPojo();
        mappedKpi.setProducerId(producerId);
        mappedKpi.setKpiName("KPI1");
        ProducerKpiMappingPojo mappingPojo = ProducerKpiMappingPojo.builder()
                .producerId(producerId)
                .producerName("Producer1")
                .mappedKpis(Collections.singletonList(mappedKpi))
                .build();
        Page<ProducerKpiMappingPojo> page = new PageImpl<>(Collections.singletonList(mappingPojo), pageable, 1);

        when(getProducerMappedKpisBL.clientValidation(accountIdentifier, String.valueOf(producerId), userDetails)).thenReturn(clientBean);
        when(getProducerMappedKpisBL.serverValidation(clientBean)).thenReturn(serverBean);
        when(getProducerMappedKpisBL.process(serverBean)).thenReturn(page);

        ResponseEntity<ResponsePojo<Page<ProducerKpiMappingPojo>>> response = controller.getProducerMappedKpis(
                accountIdentifier, producerId, pageable, userDetails);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Producer KPI mapping fetched successfully.", response.getBody().getMessage());
        assertEquals(1, response.getBody().getData().getTotalElements());
    }

    @Test
    void getProducerMappedKpis_ClientException() throws Exception {
        String accountIdentifier = "acc1";
        Integer producerId = 123;

        // Throw ClientException when clientValidation is called
        doThrow(new ClientException("ClientException : Client error"))
                .when(getProducerMappedKpisBL)
                .clientValidation(accountIdentifier, String.valueOf(producerId), userDetails);

        ResponseEntity<ResponsePojo<Page<ProducerKpiMappingPojo>>> response = controller.getProducerMappedKpis(
                accountIdentifier, producerId, pageable, userDetails);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getMessage().contains("ClientException : Client error"));
    }

    @Test
    void getProducerMappedKpis_ServerException() throws Exception {
        String accountIdentifier = "acc1";
        Integer producerId = 123;

        UtilityBean<Void> clientBean = UtilityBean.<Void>builder().metadata(new HashMap<>()).build();

        // Return clientBean for clientValidation
        when(getProducerMappedKpisBL.clientValidation(accountIdentifier, String.valueOf(producerId), userDetails)).thenReturn(clientBean);
        // Throw ServerException when serverValidation is called
        doThrow(new ServerException("ServerException : Server error"))
                .when(getProducerMappedKpisBL)
                .serverValidation(clientBean);

        ResponseEntity<ResponsePojo<Page<ProducerKpiMappingPojo>>> response = controller.getProducerMappedKpis(
                accountIdentifier, producerId, pageable, userDetails);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getMessage().contains("ServerException : Server error"));
    }

    @Test
    void getProducerMappedKpis_DataProcessingException() throws Exception {
        String accountIdentifier = "acc1";
        Integer producerId = 123;

        UtilityBean<Void> clientBean = UtilityBean.<Void>builder().metadata(new HashMap<>()).build();
        UtilityBean<ProducerKpiValidationBean> serverBean = UtilityBean.<ProducerKpiValidationBean>builder().build();

        // Return clientBean and serverBean for clientValidation and serverValidation
        when(getProducerMappedKpisBL.clientValidation(accountIdentifier, String.valueOf(producerId), userDetails)).thenReturn(clientBean);
        when(getProducerMappedKpisBL.serverValidation(clientBean)).thenReturn(serverBean);
        // Throw DataProcessingException when process is called
        doThrow(new DataProcessingException("DataProcessingException : Processing error"))
                .when(getProducerMappedKpisBL)
                .process(serverBean);

        ResponseEntity<ResponsePojo<Page<ProducerKpiMappingPojo>>> response = controller.getProducerMappedKpis(
                accountIdentifier, producerId, pageable, userDetails);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getMessage().contains("DataProcessingException : Processing error"));
    }
}
