-- master tables
DROP SCHEMA IF EXISTS `appsone` ;

-- -----------------------------------------------------
-- Schema appsone
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `appsone` ;
USE `appsone` ;

DROP VIEW if exists view_types;
DROP TABLE IF EXISTS `appsone`.`account` ;
DROP TABLE IF EXISTS `appsone`.`mst_type` ;
DROP TABLE IF EXISTS `appsone`.`tag_details` ;
DROP TABLE IF EXISTS `appsone`.`tag_mapping` ;
DROP TABLE IF EXISTS `appsone`.`controller` ;
DROP TABLE IF EXISTS `appsone`.`mst_timezone` ;
DROP TABLE IF EXISTS `appsone`.`service_configurations` ;

-- -----------------------------------------------------
-- Table `appsone`.`account`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`account` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL,
  `private_key` TEXT NULL,
  `public_key` TEXT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_type`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_type_account1_idx` ON `appsone`.`mst_type` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`mst_sub_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_sub_type` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_sub_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_sub_type_mst_type`
    FOREIGN KEY (`mst_type_id`)
    REFERENCES `appsone`.`mst_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_sub_type_mst_type_idx` ON `appsone`.`mst_sub_type` (`mst_type_id` ASC);

CREATE INDEX `mst_sub_type_account1_idx` ON `appsone`.`mst_sub_type` (`account_id` ASC);

-- All types and subtypes(view_types)

CREATE VIEW view_types AS
select t.type, t.id typeid, st.name, st.id subtypeid
from mst_type t, mst_sub_type st
where t.id = st.mst_type_id;

-- -----------------------------------------------------
-- Table `appsone`.`smtp_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`smtp_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`smtp_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `address` VARCHAR(256) NOT NULL,
  `port` INT NOT NULL,
  `username` VARCHAR(256),
  `password` VARCHAR(256),
  `security_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `from_recipient` VARCHAR(256) NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `status` TINYINT(1) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_smtp_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_smtp_details_2`
    FOREIGN KEY (`security_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`sms_details`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`sms_details` ;

CREATE TABLE IF NOT EXISTS `appsone`.`sms_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `address` VARCHAR(128) NOT NULL,
  `port` INT NOT NULL,
  `country_code` VARCHAR(32) NULL,
  `protocol_id` INT NOT NULL,
  `http_method` VARCHAR(16) NULL,
  `http_relative_url` VARCHAR(512) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `post_data` MEDIUMTEXT NULL,
  `post_data_flag` TINYINT(1) NULL,
  `status` TINYINT(1) Not NULL,
  `is_multi_request` TINYINT(4) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_sms_details_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_sms_details_2`
    FOREIGN KEY (`protocol_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`sms_parameters`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`sms_parameters` ;

CREATE TABLE IF NOT EXISTS `appsone`.`sms_parameters` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `parameter_name` VARCHAR(128) NOT NULL,
  `parameter_value` VARCHAR(128) NOT NULL,
  `sms_details_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `parameter_type_id` INT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `default_value` VARCHAR(128) NULL,
  `is_placeholder` TINYINT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_sms_parameters_1`
    FOREIGN KEY (`sms_details_id`)
    REFERENCES `appsone`.`sms_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_sms_parameters_1_idx` ON `appsone`.`sms_parameters` (`sms_details_id` ASC);
-- -----------------------------------------------------
-- Table `appsone`.`rules`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`rules` ;

CREATE TABLE IF NOT EXISTS `appsone`.`rules` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `is_enabled` TINYINT NOT NULL,
  `order` INT NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `rule_type_id` INT NOT NULL,
  `is_default` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_rules_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_rules_2`
    FOREIGN KEY (`rule_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_rules_1_idx` ON `appsone`.`rules` (`account_id` ASC);

CREATE INDEX `fk_rules_2_idx` ON `appsone`.`rules` (`rule_type_id` ASC);

