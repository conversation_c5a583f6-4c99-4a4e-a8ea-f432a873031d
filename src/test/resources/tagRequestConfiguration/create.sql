-- -----------------------------------------------------
-- Schema appsone
-- -----------------------------------------------------
DROP SCHEMA IF EXISTS `appsone` ;
CREATE SCHEMA IF NOT EXISTS `appsone` ;
USE `appsone` ;

-- -----------------------------------------------------
-- DROP TABLES
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`account`;
DROP TABLE IF EXISTS `appsone`.`mst_type`;
DROP TABLE IF EXISTS `appsone`.`mst_sub_type`;
DROP TABLE IF EXISTS `appsone`.`rules`;
DROP  TABLE IF EXISTS `appsone`.`transaction_groups`;
DROP  TABLE IF EXISTS `appsone`.`transaction_group_mapping`;
DROP TABLE IF EXISTS `appsone`.`transaction`;
DROP TABLE IF EXISTS `appsone`.`mst_timezone` ;
DROP TABLE IF EXISTS `appsone`.`tag_details` ;
DROP TABLE IF EXISTS `appsone`.`tag_mapping` ;

-- -----------------------------------------------------
-- Table `appsone`.`account`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`account` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL,
  `private_key` TEXT NULL,
  `public_key` TEXT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_type`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_type_account1_idx` ON `appsone`.`mst_type` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`mst_sub_type`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_sub_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_sub_type_mst_type`
    FOREIGN KEY (`mst_type_id`)
    REFERENCES `appsone`.`mst_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_sub_type_mst_type_idx` ON `appsone`.`mst_sub_type` (`mst_type_id` ASC);

CREATE INDEX `mst_sub_type_account1_idx` ON `appsone`.`mst_sub_type` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`rules`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`rules` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `account_id` INT NOT NULL,
  `is_enabled` TINYINT NOT NULL,
  `order` INT NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(64) NOT NULL,
  `rule_type_id` INT NOT NULL,
  `is_default` TINYINT NOT NULL DEFAULT 0,
  `max_tags` int(11) NOT NULL DEFAULT '5',
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_rules_1`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_rules_2`
    FOREIGN KEY (`rule_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_rules_1_idx` ON `appsone`.`rules` (`account_id` ASC);
CREATE INDEX `fk_rules_2_idx` ON `appsone`.`rules` (`rule_type_id` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`transaction_groups`
-- -----------------------------------------------------
CREATE TABLE `appsone`.`transaction_groups` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
    ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`transaction_group_mapping`
-- -----------------------------------------------------
CREATE TABLE `appsone`.`transaction_group_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `txn_group_id` INT NOT NULL,
  `object_id` INT NOT NULL,
  `object_ref_table` VARCHAR(128) NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_group_mapping_1`
    FOREIGN KEY (`txn_group_id`)
    REFERENCES `appsone`.`transaction_groups` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;

CREATE INDEX `fk_transaction_group_mapping_1_idx` ON `appsone`.`transaction_group_mapping` (`txn_group_id` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`transaction`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`transaction` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `audit_enabled` TINYINT(1) NOT NULL DEFAULT 0,
  `is_autoconfigured` TINYINT(1) NOT NULL DEFAULT 0,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `transaction_type_id` INT NOT NULL,
  `pattern_hashcode` VARCHAR(256) NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `rule_id` INT NULL,
  `is_business_txn` TINYINT NOT NULL DEFAULT 0,
  `max_tags` int(11) NOT NULL DEFAULT 5,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_transaction_mst_sub_type1`
    FOREIGN KEY (`transaction_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_transaction_mst_sub_type1_idx` ON `appsone`.`transaction` (`transaction_type_id` ASC);
CREATE UNIQUE INDEX `identifier_UNIQUE` ON `appsone`.`transaction` (`identifier` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`mst_timezone`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`mst_timezone` (
  `id` int NOT NULL AUTO_INCREMENT,
  `time_zone_id` varchar(128) NOT NULL,
  `timeoffset` int NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `account_id` int NOT NULL,
  `offset_name` varchar(64) NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0',
  `abbreviation` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mst_timezone_account1_idx` (`account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- -----------------------------------------------------
-- Table `appsone`.`tag_details`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`tag_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL,
  `tag_type_id` int NOT NULL,
  `is_predefined` tinyint NOT NULL,
  `ref_table` varchar(64) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  `ref_where_column_name` varchar(128) DEFAULT NULL,
  `ref_select_column_name` varchar(126) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_tag_details_tag_type_id_idx` (`tag_type_id`),
  KEY `fk_tag_details_account_id_idx` (`account_id`),
  CONSTRAINT `fk_tag_details_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`),
  CONSTRAINT `fk_tag_details_tag_type_id` FOREIGN KEY (`tag_type_id`) REFERENCES `mst_sub_type` (`id`)
) ENGINE=InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`tag_mapping`
-- -----------------------------------------------------

CREATE TABLE `appsone`.`tag_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `tag_id` int NOT NULL,
  `object_id` int NOT NULL,
  `object_ref_table` varchar(256) NOT NULL,
  `tag_key` varchar(256) NOT NULL,
  `tag_value` varchar(256) DEFAULT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `account_id` int NOT NULL,
  `user_details_id` varchar(256) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_tag_mapping_account_id_idx` (`account_id`),
  KEY `fk_tag_mapping_tag_id_idx` (`tag_id`),
  CONSTRAINT `fk_tag_mapping_account_id` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`),
  CONSTRAINT `fk_tag_mapping_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `tag_details` (`id`)
) ENGINE=InnoDB;