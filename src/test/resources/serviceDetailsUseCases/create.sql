-- master tables
DROP SCHEMA IF EXISTS `appsone` ;

-- -----------------------------------------------------
-- Schema appsone
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `appsone` ;
USE `appsone` ;

DROP VIEW if exists view_types;
DROP TABLE IF EXISTS `appsone`.`account` ;
DROP TABLE IF EXISTS `appsone`.`mst_type` ;
DROP TABLE IF EXISTS `appsone`.`tag_details` ;
DROP TABLE IF EXISTS `appsone`.`tag_mapping` ;
DROP TABLE IF EXISTS `appsone`.`controller` ;
DROP TABLE IF EXISTS `appsone`.`mst_timezone` ;
DROP TABLE IF EXISTS `appsone`.`service_configurations` ;
DROP TABLE IF EXISTS `appsone`.`mst_component_type` ;
DROP TABLE IF EXISTS `appsone`.`mst_component` ;
DROP TABLE IF EXISTS `appsone`.`comp_instance` ;
DROP TABLE IF EXISTS `appsone`.`mst_component_mapping` ;
DROP TABLE IF EXISTS `appsone`.`component_cluster_mapping` ;
DROP TABLE IF EXISTS `appsone`.`mst_common_version` ;
DROP TABLE IF EXISTS `appsone`.`mst_component_version` ;
DROP TABLE IF EXISTS `appsone`.`connection_details` ;

-- -----------------------------------------------------
-- Table `appsone`.`account`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`account` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL,
  `private_key` TEXT NULL,
  `public_key` TEXT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_type`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_type_account1_idx` ON `appsone`.`mst_type` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`mst_sub_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_sub_type` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_sub_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_sub_type_mst_type`
    FOREIGN KEY (`mst_type_id`)
    REFERENCES `appsone`.`mst_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_sub_type_mst_type_idx` ON `appsone`.`mst_sub_type` (`mst_type_id` ASC);

CREATE INDEX `mst_sub_type_account1_idx` ON `appsone`.`mst_sub_type` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`tag_details`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`tag_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `tag_type_id` INT NOT NULL,
  `is_predefined` TINYINT NOT NULL,
  `ref_table` VARCHAR(64) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `ref_where_column_name` VARCHAR(128) NULL,
  `ref_select_column_name` VARCHAR(126) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tag_details_tag_type_id`
    FOREIGN KEY (`tag_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tag_details_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`tag_mapping`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`tag_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `tag_id` INT NOT NULL,
  `object_id` INT NOT NULL,
  `object_ref_table` VARCHAR(256) NOT NULL,
  `tag_key` VARCHAR(256) NOT NULL,
  `tag_value` VARCHAR(256) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tag_mapping_tag_id`
    FOREIGN KEY (`tag_id`)
    REFERENCES `appsone`.`tag_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tag_mapping_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`controller`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`controller` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `controller_type_id` INT NOT NULL,
  `monitor_enabled` TINYINT NOT NULL DEFAULT 1,
  `status` TINYINT(4) NOT NULL DEFAULT 1,
  `plugin_supr_interval` INT NOT NULL DEFAULT '0',
  `plugin_whitelist_status` TINYINT NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_application_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_timezone`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_timezone` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `time_zone_id` VARCHAR(128) NOT NULL,
  `timeoffset` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_timezone_account1_idx` ON `appsone`.`mst_timezone` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`mst_component_type`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_component_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `description` VARCHAR(256) NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 0,
  `status` TINYINT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;
-- -----------------------------------------------------
-- Table `appsone`.`mst_component`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_component` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `is_custom` TINYINT(1) NULL DEFAULT 0,
  `status` TINYINT NULL,
  `created_time` DATETIME NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL DEFAULT 0,
  `description` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;
-- -----------------------------------------------------
-- Table `appsone`.`comp_instance`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`comp_instance` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `host_id` INT NULL,
  `is_DR` TINYINT NULL,
  `is_cluster` TINYINT NULL,
  `mst_component_version_id` INT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `mst_component_type_id` INT NOT NULL,
  `discovery` TINYINT NULL,
  `host_address` VARCHAR(256) NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `mst_common_version_id` INT NOT NULL,
  `parent_instance_id` INT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_comp_instance_mst_component1`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_comp_instance_mst_component_type1`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_component_mapping`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_component_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_component_type_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_component_mapping_mst_component_type1`
    FOREIGN KEY (`mst_component_type_id`)
    REFERENCES `appsone`.`mst_component_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_mapping_mst_component1`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;
-- -----------------------------------------------------
-- Table `appsone`.`component_cluster_mapping`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`component_cluster_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `comp_instance_id` INT NOT NULL,
  `cluster_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_component_cluster_mapping_comp_instance1`
    FOREIGN KEY (`comp_instance_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_component_cluster_mapping_comp_instance2`
    FOREIGN KEY (`cluster_id`)
    REFERENCES `appsone`.`comp_instance` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_common_version`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_common_version` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_component_id` INT NOT NULL,
  `is_custom` TINYINT(1) NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_common_version_mst_component`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_component_version`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_component_version` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `is_custom` TINYINT(1) NOT NULL,
  `status` TINYINT NOT NULL,
  `version_from` SMALLINT NULL,
  `version_to` SMALLINT NULL,
  `mst_common_version_id` INT NOT NULL,
  `mst_component_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_component_version_mst_common_version1`
    FOREIGN KEY (`mst_common_version_id`)
    REFERENCES `appsone`.`mst_common_version` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_component_version_mst_component1`
    FOREIGN KEY (`mst_component_id`)
    REFERENCES `appsone`.`mst_component` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`connection_details`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`connection_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `source_id` INT NULL,
  `source_ref_object` VARCHAR(64) NULL,
  `destination_id` INT NULL,
  `destination_ref_object` VARCHAR(64) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `is_discovery` TINYINT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

-- ---------------views------------
-- All types and subtypes(view_types)
DROP VIEW if exists view_types;
CREATE VIEW view_types AS
select t.type, t.id typeid, st.name, st.id subtypeid
from mst_type t, mst_sub_type st
where t.id = st.mst_type_id;

-- All component, component type, component version(view_components)
DROP VIEW if exists view_components;
CREATE VIEW view_components AS
select ct.id component_type_id,ct.name component_type_name, ct.status component_type_status, c.id component_id, c.name component_name,
c.is_custom, c.status component_status, cv.id common_version_id, cv.name common_version_name, v.id component_version_id,
v.name component_version_name, v.is_custom is_custom_version, v.status is_version_status, v.version_from, v.version_to
from mst_component_type ct, mst_component c, mst_component_mapping cm,
mst_common_version cv, mst_component_version v
where ct.id = cm.mst_component_type_id and cm.mst_component_id = c.id and c.id = v.mst_component_id
and v.mst_common_version_id=cv.id;

DROP VIEW if exists view_component_instance;
CREATE VIEW view_component_instance AS
select c.id, c.name, c.status, c.identifier,
c.host_id,
(select name from comp_instance where id=c.host_id) host_name,
c.is_cluster,
(case when c.is_cluster = 0 then (select cluster_id from component_cluster_mapping where comp_instance_id=c.id and account_id=c.account_id) else null end) as cluster_id,
(case when c.is_cluster = 0 then (select ci.name from component_cluster_mapping ccm, comp_instance ci where ccm.comp_instance_id=c.id and ccm.cluster_id= ci.id and ccm.account_id=ci.account_id) else null end) as cluster_name,
(case when c.is_cluster = 0 then (select ci.identifier from component_cluster_mapping ccm, comp_instance ci where ccm.comp_instance_id=c.id and ccm.cluster_id= ci.id and ccm.account_id=ci.account_id) else null end) as cluster_identifier,
c.account_id,
c.user_details_id, c.discovery, c.host_address, c.mst_component_id, vc.component_name,
c.mst_component_type_id, vc.component_type_name, c.mst_component_version_id, vc.component_version_name,
vc.common_version_id, vc.common_version_name, c.created_time, c.updated_time, c.parent_instance_id
from comp_instance c, view_components vc
where c.mst_component_id=vc.component_id and c.mst_component_type_id=vc.component_type_id
and c.mst_component_version_id = vc.component_version_id;

DROP TABLE IF EXISTS `mst_roles` ;
CREATE TABLE `mst_roles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(32) NOT NULL,
  `description` VARCHAR(256) NULL,
  `status` TINYINT NOT NULL,
  `ui_visible` TINYINT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `name_UNIQUE` (`name` ASC));

DROP TABLE IF EXISTS `mst_big_features` ;
CREATE TABLE `mst_big_features` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `description` VARCHAR(256) NULL,
  `status` TINYINT NOT NULL,
  `ui_visible` TINYINT NOT NULL DEFAULT 1,
  `dashboard_name` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `identifier_UNIQUE` (`identifier` ASC));

DROP TABLE IF EXISTS `mst_role_feature_mapping` ;
CREATE TABLE `mst_role_feature_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_role_id` INT NOT NULL,
  `mst_big_feature_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_mst_role_feature_mapping_1_idx` (`mst_big_feature_id` ASC),
  INDEX `fk_mst_role_feature_mapping_2_idx` (`mst_role_id` ASC),
  CONSTRAINT `fk_mst_role_feature_mapping_1`
    FOREIGN KEY (`mst_big_feature_id`)
    REFERENCES `mst_big_features` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_role_feature_mapping_2`
    FOREIGN KEY (`mst_role_id`)
    REFERENCES `mst_roles` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

DROP TABLE IF EXISTS `mst_access_profiles` ;
CREATE TABLE `mst_access_profiles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `mst_role_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  `is_custom` TINYINT NOT NULL,
  `status` TINYINT NOT NULL,
  `ui_visible` TINYINT NOT NULL DEFAULT 1,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_mst_access_profiles_1_idx` (`mst_role_id` ASC),
  CONSTRAINT `fk_mst_access_profiles_1`
    FOREIGN KEY (`mst_role_id`)
    REFERENCES `mst_roles` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

DROP TABLE IF EXISTS `mst_access_profile_mapping` ;
CREATE TABLE `mst_access_profile_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `mst_access_profile_id` INT NOT NULL,
  `mst_big_feature_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_mst_access_profile_mapping_1_idx` (`mst_big_feature_id` ASC),
  INDEX `fk_mst_access_profile_mapping_2_idx` (`mst_access_profile_id` ASC),
  CONSTRAINT `fk_mst_access_profile_mapping_1`
    FOREIGN KEY (`mst_big_feature_id`)
    REFERENCES `mst_big_features` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_mst_access_profile_mapping_2`
    FOREIGN KEY (`mst_access_profile_id`)
    REFERENCES `mst_access_profiles` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

DROP TABLE IF EXISTS `user_attributes` ;
CREATE TABLE `user_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `user_identifier` VARCHAR(256) NOT NULL,
  `contact_number` VARCHAR(64) NULL,
  `email_address` VARCHAR(64) NULL,
  `username` VARCHAR(256) NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` tinyint NOT NULL,
  `is_timezone_mychoice` tinyint NOT NULL DEFAULT 0,
  `mst_access_profile_id` INT NOT NULL,
  `mst_role_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_user_attributes_1_idx` (`mst_access_profile_id` ASC),
  CONSTRAINT `fk_user_attributes_1`
    FOREIGN KEY (`mst_access_profile_id`)
    REFERENCES `mst_access_profiles` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);