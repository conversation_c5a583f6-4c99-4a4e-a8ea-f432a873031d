-- master tables
DROP SCHEMA IF EXISTS `appsone` ;

-- -----------------------------------------------------
-- Schema appsone
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `appsone` ;
USE `appsone` ;

DROP VIEW if exists view_types;
DROP TABLE IF EXISTS `appsone`.`account` ;
DROP TABLE IF EXISTS `appsone`.`mst_type` ;
DROP TABLE IF EXISTS `appsone`.`tag_details` ;
DROP TABLE IF EXISTS `appsone`.`tag_mapping` ;
DROP TABLE IF EXISTS `appsone`.`controller` ;
DROP TABLE IF EXISTS `appsone`.`mst_timezone` ;
DROP TABLE IF EXISTS `appsone`.`service_configurations` ;

-- -----------------------------------------------------
-- Table `appsone`.`account`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`account` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL,
  `private_key` TEXT NULL,
  `public_key` TEXT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_type`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_type_account1_idx` ON `appsone`.`mst_type` (`account_id` ASC);

-- -----------------------------------------------------
-- Table `appsone`.`mst_sub_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_sub_type` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_sub_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_sub_type_mst_type`
    FOREIGN KEY (`mst_type_id`)
    REFERENCES `appsone`.`mst_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_sub_type_mst_type_idx` ON `appsone`.`mst_sub_type` (`mst_type_id` ASC);

CREATE INDEX `mst_sub_type_account1_idx` ON `appsone`.`mst_sub_type` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`tag_details`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`tag_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `tag_type_id` INT NOT NULL,
  `is_predefined` TINYINT NOT NULL,
  `ref_table` VARCHAR(64) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `ref_where_column_name` VARCHAR(128) NULL,
  `ref_select_column_name` VARCHAR(126) NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tag_details_tag_type_id`
    FOREIGN KEY (`tag_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tag_details_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`tag_mapping`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`tag_mapping` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `tag_id` INT NOT NULL,
  `object_id` INT NOT NULL,
  `object_ref_table` VARCHAR(256) NOT NULL,
  `tag_key` VARCHAR(256) NOT NULL,
  `tag_value` VARCHAR(256) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_tag_mapping_tag_id`
    FOREIGN KEY (`tag_id`)
    REFERENCES `appsone`.`tag_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_tag_mapping_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`controller`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`controller` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  `account_id` INT NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `controller_type_id` INT NOT NULL,
  `monitor_enabled` TINYINT NOT NULL DEFAULT 1,
  `status` TINYINT(4) NOT NULL DEFAULT 1,
  `plugin_supr_interval` INT NOT NULL DEFAULT '0',
  `plugin_whitelist_status` TINYINT NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_application_account_id`
    FOREIGN KEY (`account_id`)
    REFERENCES `appsone`.`account` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_timezone`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS `appsone`.`mst_timezone` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `time_zone_id` VARCHAR(128) NOT NULL,
  `timeoffset` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_timezone_account1_idx` ON `appsone`.`mst_timezone` (`account_id` ASC);

CREATE TABLE `appsone`.`user_notification_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `applicable_user_id` varchar(128) NOT NULL,
  `application_id` int(11) NOT NULL,
  `notification_type_id` int(11) NOT NULL,
  `signal_type_id` int(11) NOT NULL,
  `signal_severity_id` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `status` tinyint(4) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_user_notification_mapping_1` FOREIGN KEY (`application_id`) REFERENCES `controller` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_user_notification_mapping_2` FOREIGN KEY (`notification_type_id`) REFERENCES `mst_sub_type` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_user_notification_mapping_3` FOREIGN KEY (`account_id`) REFERENCES `account` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_user_notification_mapping_4` FOREIGN KEY (`signal_type_id`) REFERENCES `mst_sub_type` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_user_notification_mapping_5` FOREIGN KEY (`signal_severity_id`) REFERENCES `mst_sub_type` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB;

CREATE TABLE `appsone`.`user_notifications_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `applicable_user_id` varchar(128) NOT NULL,
  `is_sms_enabled` tinyint(4) NOT NULL DEFAULT '1',
  `is_email_enabled` tinyint(4) NOT NULL DEFAULT '1',
  `account_id` int(11) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(128) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;

CREATE TABLE `appsone`.`notification_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `notification_type_id` int(11) NOT NULL,
  `no_of_minutes` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `created_time` datetime NOT NULL,
  `updated_time` datetime NOT NULL,
  `user_details_id` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_notification_settings_1` (`notification_type_id`),
  KEY `fk_notification_settings_1_idx` (`id`),
  CONSTRAINT `fk_notification_settings_1` FOREIGN KEY (`notification_type_id`) REFERENCES `mst_sub_type` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB;