-- master tables
DROP SCHEMA IF EXISTS `appsone` ;

-- -----------------------------------------------------
-- Schema appsone
-- -----------------------------------------------------
CREATE SCHEMA IF NOT EXISTS `appsone` ;
USE `appsone` ;

-- -----------------------------------------------------
-- Table `appsone`.`a1_installation_attributes`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`a1_installation_attributes` ;

CREATE TABLE `a1_installation_attributes` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(256) NOT NULL,
  `value` VARCHAR(256) NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

-- -----------------------------------------------------
-- Table `appsone`.`mst_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_type` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

CREATE INDEX `mst_type_account1_idx` ON `appsone`.`mst_type` (`account_id` ASC);


-- -----------------------------------------------------
-- Table `appsone`.`mst_sub_type`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`mst_sub_type` ;

CREATE TABLE IF NOT EXISTS `appsone`.`mst_sub_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NOT NULL,
  `mst_type_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `account_id` INT NOT NULL,
  `description` VARCHAR(256) NOT NULL,
  `is_custom` TINYINT NOT NULL DEFAULT 0,
  `status` TINYINT NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_mst_sub_type_mst_type`
    FOREIGN KEY (`mst_type_id`)
    REFERENCES `appsone`.`mst_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
ENGINE = InnoDB;

CREATE INDEX `fk_mst_sub_type_mst_type_idx` ON `appsone`.`mst_sub_type` (`mst_type_id` ASC);

CREATE INDEX `mst_sub_type_account1_idx` ON `appsone`.`mst_sub_type` (`account_id` ASC);

DROP VIEW if exists view_types;
CREATE VIEW view_types AS
select t.type, t.id typeid, st.name, st.id subtypeid
from mst_type t, mst_sub_type st
where t.id = st.mst_type_id;
-- -----------------------------------------------------
-- Table `appsone`.`account`
-- -----------------------------------------------------
DROP TABLE IF EXISTS `appsone`.`account` ;

CREATE TABLE IF NOT EXISTS `appsone`.`account` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(128) NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `status` TINYINT NOT NULL,
  `private_key` TEXT NULL,
  `public_key` TEXT NULL,
  `user_details_id` VARCHAR(256) NOT NULL,
  `identifier` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`))
ENGINE = InnoDB;

DROP  TABLE IF EXISTS `appsone`.`notification_settings`;
CREATE TABLE `appsone`.`notification_settings` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `notification_type_id` INT NOT NULL,
  `no_of_minutes` INT NOT NULL,
  `account_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `user_details_id` VARCHAR(128) NOT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT `fk_notification_settings_1`
    FOREIGN KEY (`notification_type_id`)
    REFERENCES `appsone`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION)
    ENGINE = InnoDB;
CREATE INDEX `fk_notification_settings_1_idx` ON `appsone`.`notification_settings` (`notification_type_id` ASC);

