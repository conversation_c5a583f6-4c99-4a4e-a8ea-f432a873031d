package com.heal.controlcenter.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class ValidationUtils {

    public static void validateField(String value, String fieldName, int minLength, int maxLength, String regex,
                                     String emptyMessage, String lengthMessage, String patternMessage, Map<String, String> errors) {
        if (value == null || value.trim().isEmpty()) {
            log.error(emptyMessage);
            errors.put(fieldName, emptyMessage);
        } else {
            String trimmedValue = value.trim();
            if (trimmedValue.length() < minLength || trimmedValue.length() > maxLength) {
                String errorMessage = lengthMessage.replace("{min}", String.valueOf(minLength)).replace("{max}", String.valueOf(maxLength));
                log.error(errorMessage);
                errors.put(fieldName, errorMessage);
            }
            if (regex != null && !trimmedValue.matches(regex)) {
                log.error(patternMessage);
                errors.put(fieldName, patternMessage);
            }
        }
    }
}
