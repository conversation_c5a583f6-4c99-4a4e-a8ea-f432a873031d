package com.heal.controlcenter.util;

import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

@Slf4j
public class DateTimeUtil {

    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static Date getDateInGMT(long time) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_TIME_PATTERN);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String dateTime = simpleDateFormat.format(time);
        return simpleDateFormat.parse(dateTime);
    }

    public static String getTimeInGMT(long time) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_TIME_PATTERN);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        return simpleDateFormat.format(time);
    }

    public static Long getGMTToEpochTime(String time) {
        DateFormat simpleDateFormat;
        try {
            simpleDateFormat = new SimpleDateFormat(DATE_TIME_PATTERN);
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
            Date date = simpleDateFormat.parse(time.trim());
            return date.getTime();
        }catch (Exception e)    {
            return 0L;
        }
    }

    public static Timestamp getCurrentTimestampInGMT() {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DATE_TIME_PATTERN);
            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
            SimpleDateFormat localDateFormat = new SimpleDateFormat(DATE_TIME_PATTERN);
            return new Timestamp(localDateFormat.parse( simpleDateFormat.format(new Date())).getTime());
        } catch (ParseException e) {
            log.error("Error in getting current time stamp in GMT");
        }
        return null;
    }

    public static String date2GMTConversion(Date date, String format) {
        DateFormat gmtFormat = new SimpleDateFormat(format);
        TimeZone gmtTime = TimeZone.getTimeZone("GMT");
        gmtFormat.setTimeZone(gmtTime);
        return gmtFormat.format(date);
    }

    public static Date getDateFromString(String date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
