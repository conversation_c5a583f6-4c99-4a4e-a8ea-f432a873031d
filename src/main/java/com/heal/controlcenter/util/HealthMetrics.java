package com.heal.controlcenter.util;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jmx.export.annotation.ManagedAttribute;
import org.springframework.jmx.export.annotation.ManagedOperation;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Component
@Slf4j
@ManagedResource(objectName = "HealControlCenter:name=ApplicationInfo")
public class HealthMetrics {

    private final ThreadPoolTaskExecutor threadPoolExecutorCacheInitialization;
    private final ThreadPoolTaskExecutor threadPoolExecutorCacheRefreshScheduler;
    private final ThreadPoolTaskExecutor threadPoolExecutorAPIs;

    private final Map<Integer, Integer> statusCodes = new HashMap<>();

    // Atomic counters for overall metrics to ensure thread safety.
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong slowRequests = new AtomicLong(0);
    private final AtomicLong totalResponseTime = new AtomicLong(0);
    private final AtomicLong maxResponseTime = new AtomicLong(0);
    private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);

    // A concurrent map to store metrics for each API endpoint. The key is the API name.
    private final Map<String, ApiMetrics> apiMetricsMap = new ConcurrentHashMap<>();

    // Threshold in milliseconds to classify a request as "slow".
    private static final int SLOW_REQUEST_THRESHOLD = 3000;

    @Autowired
    public HealthMetrics(@Qualifier("ThreadPoolTaskExecutorCacheInitialization") ThreadPoolTaskExecutor threadPoolExecutorCacheInitialization,
                         @Qualifier("ThreadPoolTaskExecutorCacheRefreshScheduler") ThreadPoolTaskExecutor threadPoolExecutorCacheRefreshScheduler,
                         @Qualifier("ThreadPoolTaskExecutorAPIs") ThreadPoolTaskExecutor threadPoolExecutorAPIs) {
        this.threadPoolExecutorCacheInitialization = threadPoolExecutorCacheInitialization;
        this.threadPoolExecutorCacheRefreshScheduler = threadPoolExecutorCacheRefreshScheduler;
        this.threadPoolExecutorAPIs = threadPoolExecutorAPIs;
    }

    public void updateHealControlCenterErrors() {
        log.info("Updating Heal controlcenter errors");
    }

    public void updateUnauthorizedRequests() {
        log.info("Updating Unauthorized Requests");
    }

    public void updateResponseTime(String apiName, long responseTime) {
        // Increment total requests and add to total response time.
        totalRequests.incrementAndGet();
        totalResponseTime.addAndGet(responseTime);

        // Check if the request was slow.
        if (responseTime > SLOW_REQUEST_THRESHOLD) {
            slowRequests.incrementAndGet();
        }

        // Update min and max response times.
        updateResponseTimeStats(responseTime);

        // Update the metrics for the specific API endpoint.
        apiMetricsMap.computeIfAbsent(apiName, k -> new ApiMetrics()).update(responseTime);
    }

    private void updateResponseTimeStats(long responseTime) {
        maxResponseTime.updateAndGet(currentMax -> Math.max(currentMax, responseTime));
        minResponseTime.updateAndGet(currentMin -> Math.min(currentMin, responseTime));
    }

    public void addStatusCodes(Integer statusCode, Integer counter) {
        statusCodes.put(statusCode, statusCodes.getOrDefault(statusCode, 0) + counter);
    }

    @ManagedAttribute
    public Map<Integer, Integer> getStatusCodes() {
        return statusCodes;
    }

    @ManagedAttribute
    public long getTotalRequests() {
        return totalRequests.get();
    }

    @ManagedAttribute
    public long getSlowRequests() {
        return slowRequests.get();
    }

    @ManagedAttribute
    public long getMinResponseTime() {
        long min = minResponseTime.get();
        return min == Long.MAX_VALUE ? 0 : min;
    }

    @ManagedAttribute
    public long getMaxResponseTime() {
        return maxResponseTime.get();
    }

    @ManagedAttribute
    public double getAverageResponseTime() {
        long total = totalRequests.get();
        long totalTime = totalResponseTime.get();
        if (total == 0) {
            return 0;
        }
        double avgResponseTime = (double) totalTime / total;
        return Math.round(avgResponseTime * 100.0) / 100.0;
    }

    @ManagedAttribute
    public Map<String, Long> getApiMetrics() {
        Map<String, Long> apiMetricsCounts = new HashMap<>();
        for (Map.Entry<String, ApiMetrics> entry : apiMetricsMap.entrySet()) {
            apiMetricsCounts.put(entry.getKey(), entry.getValue().getCount());
        }
        return apiMetricsCounts;
    }

    @ManagedAttribute
    public int getCacheInitializationWorkerPoolSize() {
        return threadPoolExecutorCacheInitialization.getPoolSize();
    }

    @ManagedAttribute
    public int getCacheInitializationWorkerActiveSize() {
        return threadPoolExecutorCacheInitialization.getActiveCount();
    }

    @ManagedAttribute
    public int getCacheInitializationWorkerQueueSize() {
        return threadPoolExecutorCacheInitialization.getThreadPoolExecutor().getQueue().size();
    }

    @ManagedAttribute
    public int getCacheRefreshSchedulerWorkerPoolSize() {
        return threadPoolExecutorCacheRefreshScheduler.getPoolSize();
    }

    @ManagedAttribute
    public int getCacheRefreshSchedulerWorkerActiveSize() {
        return threadPoolExecutorCacheRefreshScheduler.getActiveCount();
    }

    @ManagedAttribute
    public int getCacheRefreshSchedulerWorkerQueueSize() {
        return threadPoolExecutorCacheRefreshScheduler.getThreadPoolExecutor().getQueue().size();
    }

    @ManagedAttribute
    public int getAPIWorkerPoolSize() {
        return threadPoolExecutorAPIs.getPoolSize();
    }

    @ManagedAttribute
    public int getAPIWorkerActiveSize() {
        return threadPoolExecutorAPIs.getActiveCount();
    }

    @ManagedAttribute
    public int getAPIWorkerQueueSize() {
        return threadPoolExecutorAPIs.getThreadPoolExecutor().getQueue().size();
    }

    @ManagedOperation
    public HealthMetricsSnapshot getMetrics() {
        long total = totalRequests.get();
        long totalTime = totalResponseTime.get();
        // Calculate average response time, avoiding division by zero.
        double avgResponseTime = (total > 0) ? (double) totalTime / total : 0;
        // Round the average response time to two decimal places.
        double roundedAvgResponseTime = Math.round(avgResponseTime * 100.0) / 100.0;

        return new HealthMetricsSnapshot(
                total,
                slowRequests.get(),
                // If no requests have been recorded, minResponseTime should be 0.
                minResponseTime.get() == Long.MAX_VALUE ? 0 : minResponseTime.get(),
                maxResponseTime.get(),
                roundedAvgResponseTime,
                apiMetricsMap
        );
    }

    @Getter
    public static class HealthMetricsSnapshot {
        // Getters for all metric fields.
        private final long totalRequests;
        private final long slowRequests;
        private final long minResponseTime;
        private final long maxResponseTime;
        private final double avgResponseTime;
        private final Map<String, ApiMetrics> apiMetrics;

        public HealthMetricsSnapshot(long totalRequests, long slowRequests, long minResponseTime,
                                     long maxResponseTime, double avgResponseTime, Map<String, ApiMetrics> apiMetrics) {
            this.totalRequests = totalRequests;
            this.slowRequests = slowRequests;
            this.minResponseTime = minResponseTime;
            this.maxResponseTime = maxResponseTime;
            this.avgResponseTime = avgResponseTime;
            this.apiMetrics = apiMetrics;
        }

    }

    public static class ApiMetrics {
        private final AtomicLong count = new AtomicLong(0);

        public void update(long responseTime) {
            count.incrementAndGet();
        }

        public long getCount() {
            return count.get();
        }

        @Override
        public String toString() {
            return "count=" + count;
        }
    }
}
