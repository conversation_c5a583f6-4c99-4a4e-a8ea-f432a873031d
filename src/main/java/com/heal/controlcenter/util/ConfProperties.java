package com.heal.controlcenter.util;

import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Properties;

public class ConfProperties {

    private static final Logger log = LoggerFactory.getLogger(ConfProperties.class);


    private static final  Properties properties = new Properties();
    private static volatile Map<String, Object> keycloakSSOConfigurations = null;
    private static volatile Map<String, String> headerConfigurations = null;

    static {
        load();
        loadKeycloakSSOConfig();
        loadHeaderConfiguration();
    }

    private static void loadHeaderConfiguration() {
        try {
            InputStream keycloakConfStream = ConfProperties.class.getClassLoader().getResourceAsStream(Constants.HEADER_PROPERTIES_FILE_NAME);
            if(headerConfigurations == null)
                headerConfigurations = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(keycloakConfStream, new TypeReference<Map<String,String>>(){});
        } catch (Exception e)   {
            log.info("Reading/Loading {} failed", Constants.HEADER_PROPERTIES_FILE_NAME, e);
        }
    }

    private static void load() {
        try {
            InputStream inputStream = ConfProperties.class.getClassLoader().getResourceAsStream(Constants.CONF_PROPERTIES_FILE_NAME);
            if (inputStream != null) {
                properties.load(inputStream);

                if (properties.isEmpty()) {
                    log.info("{} file doesn't exist in class path", Constants.CONF_PROPERTIES_FILE_NAME);
                } else {
                    log.info("{} file loaded successfully", Constants.CONF_PROPERTIES_FILE_NAME);
                }
            }
        }catch (IOException e){
            log.info("Reading/Loading {} failed", Constants.CONF_PROPERTIES_FILE_NAME, e);
        }
    }

    private static void loadKeycloakSSOConfig() {
        try {
            InputStream keycloakConfStream = ConfProperties.class.getClassLoader().getResourceAsStream(Constants.KEYCLOAK_SSO_CONF_FILE_NAME);
            if(keycloakSSOConfigurations == null)
                keycloakSSOConfigurations = CommonUtils.getObjectMapperWithHtmlEncoder().readValue(keycloakConfStream, new TypeReference<Map<String,Object>>(){});
        } catch (Exception e)   {
            log.info("Reading/Loading {} failed", Constants.KEYCLOAK_SSO_CONF_FILE_NAME, e);
        }
    }

    public static Map<String,Object> getKeycloakSSOConfigurations() {
        return keycloakSSOConfigurations;
    }

    public static Map<String, String> getHeaderConfigurations() {
        return headerConfigurations;
    }

    public static String getString(String key){
        return properties.getProperty(key);
    }

    public static String getString(String key, String defaultValue){
        String value = getString(key);
        if ((value == null) || (value.trim().isEmpty())){
            value = defaultValue;
        }
        return value;

    }

    private static Integer getInt(String key){
        String value = properties.getProperty(key);
        if(value == null || value.trim().equals(""))
            return null;
        return Integer.parseInt(properties.getProperty(key));
    }

    public static Integer getInt(String key, String defaultValue){
        Integer value = getInt(key);
        if (value == null){
            value = Integer.parseInt(defaultValue);
        }
        return value;
    }

    public static boolean getBoolean(String key, boolean defaultVal) {
        String value = getString(key);
        if (value == null || value.equals("")) {
            return defaultVal;
        }
        if (value.equalsIgnoreCase("true")) {
            return true;
        } else if (value.equalsIgnoreCase("false")) {
            return false;
        } else return defaultVal;
    }

    public static Long getLong(String key, String defaultValue){
        Long value = getLong(key);
        if (value == null){
            value = Long.parseLong(defaultValue);
        }
        return value;
    }

    private static Long getLong(String key){
        String value = properties.getProperty(key);
        if(value == null || value.trim().isEmpty())
            return null;
        return Long.parseLong(properties.getProperty(key));
    }

}
