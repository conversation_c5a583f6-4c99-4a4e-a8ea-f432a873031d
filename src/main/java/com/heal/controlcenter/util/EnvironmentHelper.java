package com.heal.controlcenter.util;

import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.exception.DataProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

public class EnvironmentHelper {
    private static final Logger logger = LoggerFactory.getLogger(EnvironmentHelper.class);
    public MasterDataRepo masterDataRepo;

    public EnvironmentHelper() {
        this.masterDataRepo = new MasterDataRepo();
    }


    public List<ViewTypes> getEnvTypeDetails() {
        List<ViewTypes> typeDetails = masterDataRepo.getTypeDetailsByTypeName(Constants.ENVIRONMENT_TYPE_NAME);

        if (typeDetails == null || typeDetails.isEmpty()) {
            logger.error("Obtained empty results when queried for type details for type {} from Redis", Constants.ENVIRONMENT_TYPE_NAME);
            return Collections.emptyList();
        }

        return typeDetails;
    }

    public String getOrDefaultEnvironmentName(int envId) {
        List<ViewTypes> typeDetailsByTypeName = getEnvTypeDetails();

        if (typeDetailsByTypeName.isEmpty()) {
            logger.warn("Obtained empty results from Redis when queried for Type details for type 'ENVIRONMENT'. Returning default environment 'NONE'");
            return "NONE";
        }

        return typeDetailsByTypeName.stream()
                .filter(f -> f.getSubTypeId() == envId)
                .findAny()
                .map(ViewTypes::getSubTypeName)
                .orElse("NONE");
    }

    public int getEnvironmentId(String env) throws DataProcessingException {
        List<ViewTypes> typeDetailsByTypeName = masterDataRepo.getTypeDetailsByTypeName(Constants.ENVIRONMENT_TYPE_NAME);

        if (typeDetailsByTypeName.isEmpty()) {
            String errMsg = "Obtained empty results from Redis when queried for Type details for type 'Environment'. Returning default environment 'NONE'";
            logger.error(errMsg);
            throw new DataProcessingException(errMsg);
        }

        return typeDetailsByTypeName.stream()
                .filter(f -> f.getSubTypeName().equalsIgnoreCase(env))
                .findAny()
                .map(ViewTypes::getSubTypeId)
                .orElseThrow(() -> {
                    String errorMessage = String.format("The environment detail provided in the input %s is not among the supported subtypes for type %s. Please verify that the subtype exists in both the Data Source and Redis.", env, Constants.ENVIRONMENT_TYPE_NAME);
                    logger.error(errorMessage);
                    return new DataProcessingException(errorMessage);
                });
    }

}
