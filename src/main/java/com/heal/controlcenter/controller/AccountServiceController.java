package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.AccountServiceValidationBean;
import com.heal.controlcenter.beans.ServiceBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.PostAccountServicesBL;
import com.heal.controlcenter.businesslogic.DeleteAccountServicesBL;
import com.heal.controlcenter.businesslogic.GetAccountServicesBL;
import com.heal.controlcenter.businesslogic.PutAccountServicesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import com.heal.configuration.pojos.IdPojo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Account Services", description = "Endpoints for managing services within accounts.")
public class AccountServiceController {

    private final GetAccountServicesBL getAccountServicesBL;
    private final JsonFileParser headersParser;
    private final PostAccountServicesBL postAccountServicesBL;
    private final DeleteAccountServicesBL deleteAccountServiceBL;
    private final PutAccountServicesBL putAccountServicesBL;

    public AccountServiceController(GetAccountServicesBL getAccountServicesBL, JsonFileParser headersParser,
                                    PostAccountServicesBL postAccountServicesBL, DeleteAccountServicesBL deleteAccountServiceBL,
                                    PutAccountServicesBL putAccountServicesBL) {
        this.getAccountServicesBL = getAccountServicesBL;
        this.headersParser = headersParser;
        this.postAccountServicesBL = postAccountServicesBL;
        this.deleteAccountServiceBL = deleteAccountServiceBL;
        this.putAccountServicesBL = putAccountServicesBL;
    }

    /**
     * Adds or updates one or more services for the specified account.
     * <p>
     * Validates the request, performs business logic for service creation or update, and returns the result.
     * </p>
     *
     * @param accountIdentifier Identifier of the account for which services are being added/updated.
     * @param body              List of ServicePojo objects representing the services to add or update.
     * @return ResponseEntity containing a ResponsePojo with the list of IdPojo for the processed services.
     */
    @Operation(
            summary = "Add or update a service for an account",
            description = "Validates and adds or updates one or more services for the specified account, including database persistence, linking, and Redis updates.",
            security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "List of service details to add or update for the account.",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ServicePojo.class)
                    )
            )
    )
    @SuccessResponse(description = "Service(s) added/updated successfully.")
    @AopCustomAnnotation
    @PostMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> addServices(
            @PathVariable String accountIdentifier,
            @RequestBody List<ServicePojo> body, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked: addOrUpdateService");
        UtilityBean<List<ServicePojo>> utilityBean = postAccountServicesBL.clientValidation(body, accountIdentifier, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<List<ServiceBean>> updatedUtilityBean = postAccountServicesBL.serverValidation(utilityBean);
        List<IdPojo> result = postAccountServicesBL.process(updatedUtilityBean);
        ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Service(s) added/updated successfully", result, HttpStatus.OK);
        return ResponseEntity.ok(responseBean);
    }

    /**
     * Retrieves a paginated list of services for a given account, with optional search and sorting.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param pageable Pagination and sorting information.
     * @param searchTerm An optional term to filter services by name or identifier.
     * @return A {@link ResponseEntity} containing a paginated list of {@link ServiceListPage} objects.
     */
    @Operation(
            summary = "Retrieve Services for Account"
    )
    @SuccessResponse(schema = Page.class, description = "Services retrieved successfully")
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<Page<ServiceListPage>>> getAccountServices(
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=name,asc`"
            )
            Pageable pageable,
            @Parameter(
                    description = "Search term to filter services by name or identifier.",
                    example = "TestService")
            @RequestParam(required = false) String searchTerm, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        UtilityBean<Object> utilityBean = getAccountServicesBL.clientValidation(accountIdentifier, searchTerm, userDetails);
        utilityBean.setPageable(pageable);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<AccountServiceValidationBean> validatedBean = getAccountServicesBL.serverValidation(utilityBean);
        Page<ServiceListPage> services = getAccountServicesBL.process(validatedBean);
        ResponsePojo<Page<ServiceListPage>> responsePojo = new ResponsePojo<>("success",
                services, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    /**
     * Updates a service for the specified account.
     * Validates the request, performs server-side validation, and processes the update.
     *
     * @param accountIdentifier Identifier of the account
     * @param body List of ServicePojo objects containing service details to update
     * @param userDetails Authenticated user details
     * @return ResponseEntity containing the result of the update operation
     */
    @Operation(
            summary = "Update a service for an account",
            description = "Validates and updates a service for the specified account.",
            security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Service details to update for the account.",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ServicePojo.class)
                    )
            )
    )
    @SuccessResponse(description = "Service updated successfully.")
    @AopCustomAnnotation
    @PutMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> updateServices(
            @PathVariable String accountIdentifier,
            @RequestBody List<ServicePojo> body,
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked: updateService");
        UtilityBean<List<ServicePojo>> utilityBean = putAccountServicesBL.clientValidation(body, accountIdentifier, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<List<ServiceBean>> updatedUtilityBean = putAccountServicesBL.serverValidation(utilityBean);
        List<IdPojo> result = putAccountServicesBL.process(updatedUtilityBean);
        ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Service updated successfully", result, HttpStatus.OK);
        return ResponseEntity.ok(responseBean);
    }

    /**
     * Deletes one or more services for the specified account.
     * Supports both hard and soft deletion based on the 'isHardDelete' flag in the request body.
     *
     * @param accountIdentifier Identifier of the account for which services are being deleted.
     * @param deleteRequestPojo Object containing a list of service identifiers and the deletion type (hard/soft).
     * @param userDetails       Authenticated user details.
     * @return ResponseEntity indicating the success or failure of the deletion operation.
     */
    @Operation(
            summary = "Delete a service for an account",
            description = "Deletes one or more services for the specified account, supporting hard and soft deletion.",
            security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "List of service identifiers and deletion type (hard/soft).",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ServiceDeleteRequestPojo.class)
                    )
            )
    )
    @SuccessResponse(description = "Service(s) deleted successfully.")
    @AopCustomAnnotation
    @DeleteMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<String>> deleteServices(
            @PathVariable String accountIdentifier,
            @RequestBody ServiceDeleteRequestPojo deleteRequestPojo,
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked: deleteService");
        log.info("Received deleteRequestPojo: {}", deleteRequestPojo);
        UtilityBean<ServiceDeleteRequestPojo> clientValidatedBean = deleteAccountServiceBL.clientValidation(deleteRequestPojo, accountIdentifier, userDetails);
        clientValidatedBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<List<ServiceBean>> serverValidatedBean = deleteAccountServiceBL.serverValidation(clientValidatedBean);
        String message = deleteAccountServiceBL.process(serverValidatedBean);
        ResponsePojo<String> responseBean = new ResponsePojo<>(message, null, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}