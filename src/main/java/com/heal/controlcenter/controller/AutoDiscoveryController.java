package com.heal.controlcenter.controller;

import com.appnomic.appsone.common.beans.discovery.Component;
import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAutoDiscoveryIgnoredEntitiesBL;
import com.heal.controlcenter.businesslogic.GetAutoDiscoveryKnownComponentsBL;
import com.heal.controlcenter.businesslogic.PostAutoDiscoveryMapToServiceBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AutoDiscoveryIgnoredEntitiesPojo;
import com.heal.controlcenter.pojo.AutoDiscoveryMapEntityPojo;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Auto Discovery", description = "Endpoints for managing auto-discovery within accounts.")
public class AutoDiscoveryController {

    private final PostAutoDiscoveryMapToServiceBL postAutoDiscoveryMapToServiceBL;
    private final GetAutoDiscoveryIgnoredEntitiesBL getAutoDiscoveryIgnoredEntitiesBL;
    private final GetAutoDiscoveryKnownComponentsBL getAutoDiscoveryKnownComponentsBL;
    private final JsonFileParser headersParser;

    public AutoDiscoveryController(PostAutoDiscoveryMapToServiceBL postAutoDiscoveryMapToServiceBL,
                                   GetAutoDiscoveryIgnoredEntitiesBL getAutoDiscoveryIgnoredEntitiesBL,
                                   GetAutoDiscoveryKnownComponentsBL getAutoDiscoveryKnownComponentsBL,
                                   JsonFileParser headersParser) {
        this.postAutoDiscoveryMapToServiceBL = postAutoDiscoveryMapToServiceBL;
        this.getAutoDiscoveryIgnoredEntitiesBL = getAutoDiscoveryIgnoredEntitiesBL;
        this.getAutoDiscoveryKnownComponentsBL = getAutoDiscoveryKnownComponentsBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves auto-discovery ignored entities for the specified account."
    )
    @SuccessResponse(schema = AutoDiscoveryIgnoredEntitiesPojo.class, description = "Ignored entities fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/ignored-entities", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<AutoDiscoveryIgnoredEntitiesPojo>>> getIgnoredEntities(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getIgnoredEntities");
        Object bean = new Object();

        UtilityBean<Object> utilityBean = getAutoDiscoveryIgnoredEntitiesBL.clientValidation(accountIdentifier, userDetails);
        getAutoDiscoveryIgnoredEntitiesBL.serverValidation(utilityBean);
        List<AutoDiscoveryIgnoredEntitiesPojo> data = getAutoDiscoveryIgnoredEntitiesBL.process(bean);

        ResponsePojo<List<AutoDiscoveryIgnoredEntitiesPojo>> responseBean = new ResponsePojo<>("Ignored entities fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Maps auto-discovery entities to services for the specified account.",
            description = "Maps discovered entities to services or unmaps them. Returns different status codes based on the operation result."
    )
    @SuccessResponse(description = "Mapping successful or service(s) unmapped successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/map-to-service", method = RequestMethod.POST)
    public ResponseEntity<ResponsePojo<String>> mapEntity(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "Entity mapping details for auto-discovery service mapping",
                    required = true
            )
            @Validated @RequestBody AutoDiscoveryMapEntityPojo body,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : mapEntity");

        UtilityBean<AutoDiscoveryMapEntityPojo> utilityBean = postAutoDiscoveryMapToServiceBL.clientValidation(body, accountIdentifier, userDetails);
        AutoDiscoveryMapEntityPojo mapEntityBean = postAutoDiscoveryMapToServiceBL.serverValidation(utilityBean);
        List<String> invalidJsonObjects = postAutoDiscoveryMapToServiceBL.process(mapEntityBean);

        ResponsePojo<String> responseBean = new ResponsePojo<>();
        if (invalidJsonObjects.contains("UNMAPPING")) {
            invalidJsonObjects.remove("UNMAPPING");
            if (invalidJsonObjects.size() == 0) {
                responseBean.setData(null);
                responseBean.setMessage("Service(s) unmapped successfully.");
                responseBean.setResponseStatus(HttpStatus.OK);

            } else if (invalidJsonObjects.size() < Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Some invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.MULTI_STATUS);

            } else if (invalidJsonObjects.size() == Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.BAD_REQUEST);
            }
        } else if (invalidJsonObjects.contains("WRONG-EDIT")) {
            invalidJsonObjects.remove("WRONG-EDIT");
            responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
            responseBean.setMessage("Cannot edit component instance mappings here!");
            responseBean.setResponseStatus(HttpStatus.BAD_REQUEST);
        } else if (invalidJsonObjects.contains("PASS")) {
            invalidJsonObjects.remove("PASS");
            if (invalidJsonObjects.size() == 0) {
                responseBean.setData(null);
                responseBean.setMessage("Mapping successful and related connections are discovered.");
                responseBean.setResponseStatus(HttpStatus.OK);
            }
        }
        if (invalidJsonObjects.stream().noneMatch(i -> i.equals("UNMAPPING") || i.equals("WRONG-EDIT"))) {
            if (invalidJsonObjects.size() > 0 && invalidJsonObjects.size() < Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Some invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.MULTI_STATUS);
            } else if (invalidJsonObjects.size() == Arrays.asList(mapEntityBean.getServiceMappingIdentifiers()).size()) {
                responseBean.setData("Invalid Json Objects: " + invalidJsonObjects);
                responseBean.setMessage("Invalid entities found.");
                responseBean.setResponseStatus(HttpStatus.BAD_REQUEST);
            }
        }

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Retrieves auto-discovery known component information.",
            description = "Returns information about all known components for auto-discovery. This is a public endpoint that does not require authentication."
    )
    @SuccessResponse(schema = Component.class, description = "Components info fetched successfully.")
    @RequestMapping(value = "/auto-discovery-components", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<Component>>> getComponentAttributeInfo()
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getComponentAttributeInfo");

        List<Component> data = getAutoDiscoveryKnownComponentsBL.process(new Object());

        ResponsePojo<List<Component>> responsePojo = new ResponsePojo<>("Components info fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
