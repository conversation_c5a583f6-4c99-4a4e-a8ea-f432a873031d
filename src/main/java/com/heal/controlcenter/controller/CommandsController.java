package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAgentCommandsBL;
import com.heal.controlcenter.businesslogic.PostAgentCommandsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentCommandsPojo;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Agent Commands", description = "Endpoints for managing agent commands within accounts and services.")
public class CommandsController {

    private final GetAgentCommandsBL getAgentCommandsBL;
    private final PostAgentCommandsBL postAgentCommandsBL;
    private final JsonFileParser headersParser;

    public CommandsController(GetAgentCommandsBL getAgentCommandsBL, PostAgentCommandsBL postAgentCommandsBL, JsonFileParser headersParser) {
        this.getAgentCommandsBL = getAgentCommandsBL;
        this.postAgentCommandsBL = postAgentCommandsBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves agent commands for the specified account, service, and agent type."
    )
    @SuccessResponse(schema = AgentCommandsPojo.class, description = "Agent commands fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/services/{serviceId}/agent-commands", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<AgentCommandsPojo>> getAgentCommands(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    name = "serviceId",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceId") String serviceId,
            @Parameter(
                    name = "agentType",
                    description = "Type of agent for which to retrieve commands",
                    required = true,
                    example = "JAVA_AGENT"
            )
            @RequestParam("agentType") String agentType,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getAgentCommands");

        UtilityBean<List<String>> utilityBean = getAgentCommandsBL.clientValidation(accountIdentifier, serviceId, agentType, userDetails);
        getAgentCommandsBL.serverValidation(utilityBean);
        AgentCommandsPojo data = getAgentCommandsBL.process(utilityBean);

        ResponsePojo<AgentCommandsPojo> responseBean = new ResponsePojo<>("Agent commands fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Adds agent commands for the specified account, service, and agent type."
    )
    @SuccessResponse(description = "Agent commands added successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/services/{serviceId}/agent-commands", method = RequestMethod.POST)
    public ResponseEntity<ResponsePojo<Object>> addAgentCommands(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    name = "serviceId",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceId") String serviceId,
            @Parameter(
                    name = "agentType",
                    description = "Type of agent for which to add commands",
                    required = true,
                    example = "JAVA_AGENT"
            )
            @RequestParam("agentType") String agentType,
            @Parameter(
                    description = "Agent commands configuration to add",
                    required = true
            )
            @RequestBody AgentCommandsPojo agentCommandsPojo,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : addAgentCommands");

        UtilityBean<AgentCommandsPojo> utilityBean = postAgentCommandsBL.clientValidation(agentCommandsPojo, accountIdentifier, serviceId, agentType, userDetails);
        postAgentCommandsBL.serverValidation(utilityBean);
        postAgentCommandsBL.process(utilityBean);

        ResponsePojo<Object> responseBean = new ResponsePojo<>("Agent commands added successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
