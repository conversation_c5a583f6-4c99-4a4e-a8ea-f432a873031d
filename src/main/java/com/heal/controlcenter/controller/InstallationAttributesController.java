package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.InstallationAttributeBean;
import com.heal.controlcenter.businesslogic.InstallationAttributeBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Installation Attributes", description = "Endpoints for managing installation attributes within the system.")
public class InstallationAttributesController {

    private final InstallationAttributeBL installationAttributeBL;
    private final JsonFileParser headersParser;

    public InstallationAttributesController(InstallationAttributeBL installationAttributeBL, JsonFileParser headersParser) {
        this.installationAttributeBL = installationAttributeBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves installation attributes for the system.",
            description = "Returns installation attributes and configuration details. Authorization is optional for this endpoint."
    )
    @SuccessResponse(schema = InstallationAttributeBean.class, description = "Installation attributes fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/installation-attributes", method = RequestMethod.GET)
    public ResponseEntity<Object> getInstallationAttributes(BasicUserDetails userDetails) throws ClientException, DataProcessingException, ServerException {
        installationAttributeBL.clientValidation(userDetails);
        List<InstallationAttributeBean> listOfInstallationAttributes = installationAttributeBL.process("Installation attributes");
        ResponsePojo<List<InstallationAttributeBean>> responsePojo = new ResponsePojo<>("Installation details fetching successfully", listOfInstallationAttributes, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
