package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.businesslogic.GetViewTypesByNameBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ViewTypeResponse;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Controller for fetching ViewTypes by typeName from Redis.
 */
@Slf4j
@RestController
@ErrorResponse
@Tag(name = "View Types", description = "Endpoints for managing view types within accounts and services.")
public class ViewTypesController {

    private final GetViewTypesByNameBL getViewTypesByNameBL;
    private final JsonFileParser headersParser;

    public ViewTypesController(GetViewTypesByNameBL getViewTypesByNameBL, JsonFileParser headersParser) {
        this.getViewTypesByNameBL = getViewTypesByNameBL;
        this.headersParser = headersParser;
    }

    /**
     * Fetches all ViewTypes objects from Redis where typeName matches the provided argument.
     * Uses standard response structure and AOP logging.
     *
     * @param typeName    The typeName to filter viewtypes (case-insensitive).
     * @param userDetails Basic user details for context (if needed for future expansion)
     * @return ResponseEntity containing a list of ViewTypeResponse with the given typeName.
     */
    @Operation(
            summary = "Get viewtypes by typeName",
            description = "Fetches all ViewTypeResponse objects from Redis where typeName matches the provided argument."
    )
    @SuccessResponse(schema = ViewTypeResponse.class, description = " viewtypes fetched successfully.")
    @AopCustomAnnotation
    @GetMapping("/view-types")
    public ResponseEntity<ResponsePojo<List<ViewTypeResponse>>> getViewTypesByTypeName(
            @Parameter(description = "Type name to filter viewtypes", required = true, example = "Environment")
            @RequestParam String typeName,
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.info("Received request to fetch viewtypes for typeName: {}", typeName);
        var utilityBean = getViewTypesByNameBL.clientValidation(typeName, userDetails);
        utilityBean.getMetadata().put("userId", userDetails.getUserIdentifier());
        var validatedBean = getViewTypesByNameBL.serverValidation(utilityBean);
        List<ViewTypeResponse> result = getViewTypesByNameBL.process(validatedBean);

        ResponsePojo<List<ViewTypeResponse>> responseBean = new ResponsePojo<>(
                String.format("ViewTypes fetched successfully for typeName '%s'.", typeName),
                result,
                HttpStatus.OK
        );
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
