package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetServicePageAttributesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServicePageAttributePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Service Page Attributes", description = "Endpoints for managing service page attributes within accounts and services.")
public class ServicePageAttributesController {

    private final GetServicePageAttributesBL getServicePageAttributesBL;
    private final JsonFileParser headersParser;

    public ServicePageAttributesController(GetServicePageAttributesBL getServicePageAttributesBL, JsonFileParser headersParser) {
        this.getServicePageAttributesBL = getServicePageAttributesBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves service page attributes for the specified account and service.",
            description = "Returns detailed attributes and configuration for service pages, including display properties and metadata."
    )
    @SuccessResponse(schema = ServicePageAttributePojo.class, description = "Service page attributes fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/services/{serviceId}/attributes", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<ServicePageAttributePojo>>> getServiceAttributes(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    name = "serviceId",
                    description = "Unique identifier of the service",
                    required = true,
                    example = "service-123"
            )
            @PathVariable("serviceId") String serviceId,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getServiceAttributes");

        UtilityBean<Integer> attributesUtilityBean = getServicePageAttributesBL.clientValidation(accountIdentifier, serviceId, userDetails);
        Integer srvcId = getServicePageAttributesBL.serverValidation(attributesUtilityBean);
        List<ServicePageAttributePojo> data = getServicePageAttributesBL.process(srvcId);

        ResponsePojo<List<ServicePageAttributePojo>> responsePojo = new ResponsePojo<>("Service page attributes fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
