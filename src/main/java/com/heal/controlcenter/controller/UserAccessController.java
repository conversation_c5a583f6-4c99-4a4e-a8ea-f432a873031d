package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.businesslogic.UserAccessibleActionBL;
import com.heal.controlcenter.businesslogic.UserDetailsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@ErrorResponse
@Tag(name = "User Access", description = "Endpoints for managing user access and permissions.")
public class UserAccessController {

    private final UserAccessibleActionBL userAccessibleActionBL;
    private final UserDetailsBL userDetailsBL;
    private final JsonFileParser headersParser;

    public UserAccessController(UserAccessibleActionBL userAccessibleActionBL, UserDetailsBL userDetailsBL, JsonFileParser headersParser) {
        this.userAccessibleActionBL = userAccessibleActionBL;
        this.userDetailsBL = userDetailsBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves user access information and permissions."
    )
    @SuccessResponse(schema = UserAccessibleActions.class, description = "User access information fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/users/access-info", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<UserAccessibleActions>> getUserAccessInformation(BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {

        UtilityBean<String> userId = userAccessibleActionBL.clientValidation(userDetails);
        UserAttributesBean userAttributesBean = userAccessibleActionBL.serverValidation(userId);
        UserAccessibleActions userActionsDetails = userAccessibleActionBL.process(userAttributesBean);

        ResponsePojo<UserAccessibleActions> response = new ResponsePojo<>("User access information fetched successfully", userActionsDetails, HttpStatus.OK);

        return new ResponseEntity<>(response, headersParser.loadHeaderConfiguration(), response.getResponseStatus());
    }

    @Operation(
            summary = "Retrieves all users in the system."
    )
    @SuccessResponse(schema = UserDetailsBean.class, description = "Users fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/users", method = RequestMethod.GET)
    public ResponseEntity<Object> getUsers(BasicUserDetails userDetails) throws ClientException, DataProcessingException, ServerException {

        userDetailsBL.clientValidation(userDetails);
        List<UserDetailsBean> userDetailsBeanList = userDetailsBL.process("Users detail");

        ResponsePojo<List<UserDetailsBean>> responsePojo = new ResponsePojo<>("Users detail fetch successfully", userDetailsBeanList, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
