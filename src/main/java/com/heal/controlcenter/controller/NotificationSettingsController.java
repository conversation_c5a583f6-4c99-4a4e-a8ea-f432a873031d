package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetNotificationSettingsBL;
import com.heal.controlcenter.businesslogic.PutNotificationSettingsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Notification Settings", description = "Endpoints for managing notification settings within accounts.")
public class NotificationSettingsController {

    private final GetNotificationSettingsBL getNotificationSettingsBL;
    private final PutNotificationSettingsBL putNotificationSettingsBL;
    private final JsonFileParser headersParser;

    public NotificationSettingsController(GetNotificationSettingsBL getNotificationSettingsBL,
                                          PutNotificationSettingsBL putNotificationSettingsBL, JsonFileParser headersParser) {
        this.getNotificationSettingsBL = getNotificationSettingsBL;
        this.putNotificationSettingsBL = putNotificationSettingsBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves notification settings for the specified account."
    )
    @SuccessResponse(schema = NotificationSettings.class, description = "Notification settings fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/notification-settings", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<NotificationSettings>>> getNotificationSettings(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getNotificationSettings");

        UtilityBean<Object> utilityBean = getNotificationSettingsBL.clientValidation(accountIdentifier, userDetails);
        UserAccountPojo user = getNotificationSettingsBL.serverValidation(utilityBean);
        List<NotificationSettings> data = getNotificationSettingsBL.process(user);

        ResponsePojo<List<NotificationSettings>> responsePojo = new ResponsePojo<>("Notification settings fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Updates notification settings for the specified account."
    )
    @SuccessResponse(description = "Notification settings updated successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/notification-settings", method = RequestMethod.PUT)
    public ResponseEntity<ResponsePojo<Object>> putNotificationSettings(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "List of notification settings to update",
                    required = true
            )
            @Validated @RequestBody List<NotificationSettingsPojo> body,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : putNotificationSettings");

        UtilityBean<List<NotificationSettingsPojo>> utilityBean = putNotificationSettingsBL.clientValidation(body, accountIdentifier, userDetails);
        putNotificationSettingsBL.serverValidation(utilityBean);
        putNotificationSettingsBL.process(utilityBean);

        ResponsePojo<Object> responsePojo = new ResponsePojo<>("Notification settings updated successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
