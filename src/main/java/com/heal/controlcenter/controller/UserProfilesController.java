package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.UserProfileBean;
import com.heal.controlcenter.businesslogic.UserProfilesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> Suman - 19-10-2021
 */

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "User Profiles", description = "Endpoints for managing user profiles within the system.")
public class UserProfilesController {

    private final UserProfilesBL userProfilesBL;
    private final JsonFileParser headersParser;

    public UserProfilesController(UserProfilesBL userProfilesBL, JsonFileParser headersParser) {
        this.userProfilesBL = userProfilesBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves all available user profiles in the system."
    )
    @SuccessResponse(schema = UserProfileBean.class, description = "User profiles fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/user-profiles", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<UserProfileBean>>> getUserProfiles(BasicUserDetails userDetails)
            throws ClientException, DataProcessingException, ServerException {

        userProfilesBL.clientValidation(userDetails);
        List<UserProfileBean> listOfUserProfiles = userProfilesBL.process("User profiles");

        ResponsePojo<List<UserProfileBean>> responsePojo = new ResponsePojo<>("User profiles fetched successfully", listOfUserProfiles, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
