package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetEmailConfigurationsBL;
import com.heal.controlcenter.businesslogic.PostEmailConfigurationsBL;
import com.heal.controlcenter.businesslogic.PutEmailConfigurationsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Email Configurations", description = "Endpoints for managing email configurations within accounts.")
public class EmailConfigurationsController {

    private final GetEmailConfigurationsBL getEmailConfigurationsBL;
    private final PutEmailConfigurationsBL putEmailConfigurationsBL;
    private final PostEmailConfigurationsBL postEmailConfigurationsBL;
    private final JsonFileParser headersParser;

    public EmailConfigurationsController(GetEmailConfigurationsBL getEmailConfigurationsBL,
                                         PutEmailConfigurationsBL putEmailConfigurationsBL,
                                         PostEmailConfigurationsBL postEmailConfigurationsBL,
                                         JsonFileParser headersParser) {
        this.getEmailConfigurationsBL = getEmailConfigurationsBL;
        this.putEmailConfigurationsBL = putEmailConfigurationsBL;
        this.postEmailConfigurationsBL = postEmailConfigurationsBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Fetches Email configurations of the user."
    )
    @SuccessResponse(schema = SMTPDetailsPojo.class, description = "Email configurations fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/email-configurations", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<SMTPDetailsPojo>> getEmailConfigurations(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getEmailConfigurations");

        UtilityBean<Object> emailUtilityBean = getEmailConfigurationsBL.clientValidation(accountIdentifier, userDetails);
        Integer accountId = getEmailConfigurationsBL.serverValidation(emailUtilityBean);
        SMTPDetailsPojo data = getEmailConfigurationsBL.process(accountId);

        ResponsePojo<SMTPDetailsPojo> responsePojo = new ResponsePojo<>("Email configurations fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Updates Email configurations for the specified account."
    )
    @SuccessResponse(description = "Email configurations updated successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/email-configurations", method = RequestMethod.PUT)
    public ResponseEntity<ResponsePojo<Object>> putEmailConfigurations(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "SMTP configuration details to update",
                    required = true
            )
            @Validated @RequestBody SMTPDetailsPojo body,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : putEmailConfigurations");

        UtilityBean<SMTPDetailsPojo> smtpUtilityBean = putEmailConfigurationsBL.clientValidation(body, accountIdentifier, userDetails);
        putEmailConfigurationsBL.serverValidation(smtpUtilityBean);
        putEmailConfigurationsBL.process(smtpUtilityBean);

        ResponsePojo<Object> responsePojo = new ResponsePojo<>("Email configurations updated successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Adds Email configurations for the specified account."
    )
    @SuccessResponse(description = "Email configurations added successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/email-configurations", method = RequestMethod.POST)
    public ResponseEntity<ResponsePojo<Object>> addEmailConfiguration(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "SMTP configuration details to add",
                    required = true
            )
            @Validated @RequestBody SMTPDetailsPojo body,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : addEmailConfiguration");

        UtilityBean<SMTPDetailsPojo> smtpDetailsUtilityBean = postEmailConfigurationsBL.clientValidation(body, accountIdentifier, userDetails);
        postEmailConfigurationsBL.serverValidation(smtpDetailsUtilityBean);
        postEmailConfigurationsBL.process(smtpDetailsUtilityBean);

        ResponsePojo<Object> responsePojo = new ResponsePojo<>("Email configurations added successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
