package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.PostConnectionBL;
import com.heal.controlcenter.businesslogic.DeleteConnectionsBL;
import com.heal.controlcenter.businesslogic.GetConnectionsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestHeader;
import com.heal.controlcenter.swagger.ErrorResponse;
import org.springframework.web.bind.annotation.RestController;
import com.heal.controlcenter.swagger.SuccessResponse;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Connections", description = "Endpoints for managing service-to-service connections within accounts.")
public class ConnectionsController {

    private final GetConnectionsBL getConnectionsBL;
    private final JsonFileParser headersParser;
    private final PostConnectionBL postConnectionBL;
    private final DeleteConnectionsBL deleteConnectionsBL;

    public ConnectionsController(GetConnectionsBL getConnectionsBL, JsonFileParser headersParser,
                                 PostConnectionBL postConnectionBL, DeleteConnectionsBL deleteConnectionsBL) {
        this.getConnectionsBL = getConnectionsBL;
        this.headersParser = headersParser;
        this.postConnectionBL = postConnectionBL;
        this.deleteConnectionsBL = deleteConnectionsBL;
    }

    /**
     * Adds new service-to-service connections for a given account.
     * Validates the request payload, performs business logic, and returns the result.
     * Logs the request, validation steps, and any errors encountered during processing.
     *
     * @param accountIdentifier The account identifier from the path variable.
     * @param body              List of connection details to be added.
     * @param userDetails       Authenticated user details.
     * @return ResponseEntity containing the result and status.
     */
    @Operation(
            summary = "Add new service-to-service connections for a given account.",
            description = "Validates and adds new connections for the specified account identifier.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "List of connection details to be added.",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConnectionDetailsPojo.class)
                    )
            )
    )
    @SuccessResponse(description = "Connections added successfully.")
    @AopCustomAnnotation
    @PostMapping(value = "/accounts/{accountIdentifier}/connections")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> addConnections(
            @PathVariable String accountIdentifier,
            @Validated @RequestBody List<ConnectionDetailsPojo> body,
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.info("[addConnections] Received request to add connections. accountIdentifier={}, userId={}, bodySize={}", accountIdentifier, userDetails != null ? userDetails.getUserIdentifier() : "N/A", body != null ? body.size() : 0);
        UtilityBean<List<ConnectionDetailsPojo>> utilityBean = postConnectionBL.clientValidation(body, accountIdentifier, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        log.debug("[addConnections] Client validation successful for accountIdentifier={}", accountIdentifier);
        UtilityBean<List<com.heal.controlcenter.beans.ConnectionDetailsBean>> validatedBean = postConnectionBL.serverValidation(utilityBean);
        log.debug("[addConnections] Server validation successful for accountIdentifier={}", accountIdentifier);
        List<IdPojo> idPojoList = postConnectionBL.process(validatedBean);
        log.info("[addConnections] Successfully added {} connections for accountIdentifier={}", idPojoList.size(), accountIdentifier);
        ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Connections added successfully.", idPojoList, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    /**
     * Retrieves a paginated list of connections for the specified account.
     * Performs client and server validation, fetches connections, and returns the result.
     * Logs request parameters, validation steps, and any errors encountered during processing.
     *
     * @param authorization     Bearer token for authentication.
     * @param accountIdentifier Unique identifier of the account.
     * @param pageable          Pagination and sorting information.
     * @param searchTerm        Optional search term to filter connections by service name or identifier.
     * @param userDetails       Basic user details for audit and access control.
     * @return Paginated response containing connections.
     */
    @Operation(
            summary = "Retrieves connections for the specified account.",
            description = "Returns a paginated list of connections linked to the given account identifier. Optionally filters results using a search term."
    )
    @SuccessResponse(schema = GetConnectionPojo.class, description = "Connections fetched successfully.")
    @GetMapping(value = "/accounts/{identifier}/connections")
    public ResponseEntity<ResponsePojo<Page<GetConnectionPojo>>> getConnections(
            @RequestHeader("Authorization") String authorization,
            @PathVariable("identifier") String accountIdentifier,
            Pageable pageable,
            @RequestParam(required = false) String searchTerm,
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.info("[getConnections] Received request to fetch connections. accountIdentifier={}, userId={}, searchTerm={}, pageable={}", accountIdentifier, userDetails != null ? userDetails.getUserIdentifier() : "N/A", searchTerm, pageable);
        UtilityBean<String> utilityBean = getConnectionsBL.clientValidation(accountIdentifier, userDetails);
        utilityBean.setPageable(pageable);
        utilityBean.getRequestParams().put(Constants.SEARCH_TERM_KEY, searchTerm);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        log.debug("[getConnections] Client validation successful for accountIdentifier={}", accountIdentifier);
        UtilityBean<List<ControllerBean>> validatedBean = getConnectionsBL.serverValidation(utilityBean);
        log.debug("[getConnections] Server validation successful for accountIdentifier={}", accountIdentifier);
        Page<GetConnectionPojo> page = getConnectionsBL.process(validatedBean);
        log.info("[getConnections] Successfully fetched {} connections for accountIdentifier={}", page.getTotalElements(), accountIdentifier);
        ResponsePojo<Page<GetConnectionPojo>> responsePojo = new ResponsePojo<>("Connections fetched successfully.", page, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    /**
     * Deletes one or more connections for the specified account.
     * Validates the request payload, performs business logic, and returns the result.
     * Logs the request, validation steps, and any errors encountered during processing.
     *
     * @param accountIdentifier The account identifier from the path variable.
     * @param body              List of connection details to be deleted.
     * @param userDetails       Authenticated user details.
     * @return ResponseEntity containing the result and status.
     */
    @Operation(
            summary = "Delete one or more service-to-service connections for a given account.",
            description = "Validates and deletes connections for the specified account identifier.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "List of connection details to be deleted.",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ConnectionDetailsPojo.class)
                    )
            )
    )
    @SuccessResponse(description = "Connections deleted successfully.")
    @AopCustomAnnotation
    @DeleteMapping(value = "/accounts/{accountIdentifier}/connections")
    public ResponseEntity<ResponsePojo<String>> deleteConnections(
            @PathVariable String accountIdentifier,
            @Validated @RequestBody List<ConnectionDetailsPojo> body,
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.info("[deleteConnections] Received request to delete connections. accountIdentifier={}, userId={}, bodySize={}", accountIdentifier, userDetails != null ? userDetails.getUserIdentifier() : "N/A", body != null ? body.size() : 0);
        UtilityBean<List<ConnectionDetailsPojo>> utilityBean = deleteConnectionsBL.clientValidation(body, accountIdentifier, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        log.debug("[deleteConnections] Client validation successful for accountIdentifier={}", accountIdentifier);
        UtilityBean<List<com.heal.controlcenter.beans.ConnectionDetailsBean>> validatedBean = deleteConnectionsBL.serverValidation(utilityBean);
        log.debug("[deleteConnections] Server validation successful for accountIdentifier={}", accountIdentifier);
        String resultMessage = deleteConnectionsBL.process(validatedBean);
        log.info("[deleteConnections] Successfully deleted connections for accountIdentifier={}", accountIdentifier);
        ResponsePojo<String> responseBean = new ResponsePojo<>(resultMessage, null, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
