package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.businesslogic.TimeZoneBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@ErrorResponse
@Tag(name = "Timezones", description = "Endpoints for managing timezones.")
public class TimeZoneController {

    private final TimeZoneBL timeZoneBL;
    private final JsonFileParser headersParser;

    public TimeZoneController(TimeZoneBL timeZoneBL, JsonFileParser headersParser) {
        this.timeZoneBL = timeZoneBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves all available timezones."
    )
    @SuccessResponse(schema = TimezoneBean.class, description = "Timezones fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/timezones", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<TimezoneBean>>> getAllTimezones(BasicUserDetails userDetails)
            throws ClientException, DataProcessingException, ServerException {
        timeZoneBL.clientValidation(userDetails);
        List<TimezoneBean> listOfTimeZones = timeZoneBL.process("Timezones");

        ResponsePojo<List<TimezoneBean>> responsePojo = new ResponsePojo<>("Timezones fetched successfully", listOfTimeZones, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
