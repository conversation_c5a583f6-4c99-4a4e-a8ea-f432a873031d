package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetSMSConfigurationsBL;
import com.heal.controlcenter.businesslogic.PostSMSConfigurationsBL;
import com.heal.controlcenter.businesslogic.PutSMSConfigurationsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.SMSDetailsPojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "SMS Configurations", description = "Endpoints for managing SMS configurations within accounts.")
public class SMSConfigurationsController {

    private final GetSMSConfigurationsBL getSMSConfigurationsBL;
    private final PutSMSConfigurationsBL putSMSConfigurationsBL;
    private final PostSMSConfigurationsBL postSMSConfigurationsBL;
    private final JsonFileParser headersParser;

    public SMSConfigurationsController(GetSMSConfigurationsBL getSMSConfigurationsBL,
                                       PutSMSConfigurationsBL putSMSConfigurationsBL,
                                       PostSMSConfigurationsBL postSMSConfigurationsBL,
                                       JsonFileParser headersParser) {
        this.getSMSConfigurationsBL = getSMSConfigurationsBL;
        this.putSMSConfigurationsBL = putSMSConfigurationsBL;
        this.postSMSConfigurationsBL = postSMSConfigurationsBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieves SMS configurations for the specified account."
    )
    @SuccessResponse(schema = SMSDetailsPojo.class, description = "SMS configurations fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/sms-configurations", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<SMSDetailsPojo>> getSmsConfigurations(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getSmsConfigurations");

        UtilityBean<Object> smsDetailsUtilityBean = getSMSConfigurationsBL.clientValidation(accountIdentifier, userDetails);
        Integer accountId = getSMSConfigurationsBL.serverValidation(smsDetailsUtilityBean);
        SMSDetailsPojo data = getSMSConfigurationsBL.process(accountId);

        ResponsePojo<SMSDetailsPojo> responsePojo = new ResponsePojo<>("SMS configurations fetched successfully.", data, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Updates SMS configurations for the specified account."
    )
    @SuccessResponse(description = "SMS configurations updated successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/sms-configurations", method = RequestMethod.PUT)
    public ResponseEntity<ResponsePojo<Object>> putSmsConfigurations(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "SMS configuration details to update",
                    required = true
            )
            @Validated @RequestBody SMSDetailsPojo body,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : putSmsConfigurations");

        UtilityBean<SMSDetailsPojo> smsDetailsUtilityBean = putSMSConfigurationsBL.clientValidation(body, accountIdentifier, userDetails);
        putSMSConfigurationsBL.serverValidation(smsDetailsUtilityBean);
        putSMSConfigurationsBL.process(smsDetailsUtilityBean);

        ResponsePojo<Object> responsePojo = new ResponsePojo<>("SMS configurations updated successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Adds SMS configurations for the specified account."
    )
    @SuccessResponse(description = "SMS configurations added successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/accounts/{identifier}/sms-configurations", method = RequestMethod.POST)
    public ResponseEntity<ResponsePojo<Object>> addSMSConfiguration(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "SMS configuration details to add",
                    required = true
            )
            @Validated @RequestBody SMSDetailsPojo body,
            BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : addSMSConfiguration");

        UtilityBean<SMSDetailsPojo> smsDetailsUtilityBean = postSMSConfigurationsBL.clientValidation(body, accountIdentifier, userDetails);
        postSMSConfigurationsBL.serverValidation(smsDetailsUtilityBean);
        postSMSConfigurationsBL.process(smsDetailsUtilityBean);

        ResponsePojo<Object> responsePojo = new ResponsePojo<>("SMS configurations added successfully.", null, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
