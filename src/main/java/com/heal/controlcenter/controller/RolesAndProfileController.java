package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.IdBean;
import com.heal.controlcenter.businesslogic.UserProfilesByRoleIdBL;
import com.heal.controlcenter.businesslogic.UserRoleBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Roles and Profiles", description = "Endpoints for managing roles and profiles within the system.")
public class RolesAndProfileController {

    private final UserRoleBL userRoleBL;
    private final JsonFileParser headersParser;
    private final UserProfilesByRoleIdBL userProfilesByRoleIdBL;

    public RolesAndProfileController(UserRoleBL userRoleBL, JsonFileParser headersParser, UserProfilesByRoleIdBL userProfilesByRoleIdBL) {
        this.userRoleBL = userRoleBL;
        this.headersParser = headersParser;
        this.userProfilesByRoleIdBL = userProfilesByRoleIdBL;
    }

    @Operation(
            summary = "Retrieves all user roles in the system."
    )
    @SuccessResponse(schema = IdBean.class, description = "User roles fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/roles", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<IdBean>>> getUserRoles(BasicUserDetails userDetails)
            throws ClientException, DataProcessingException, ServerException {
        userRoleBL.clientValidation(userDetails);
        List<IdBean> listOfUserRoles = userRoleBL.process("User roles");
        ResponsePojo<List<IdBean>> responsePojo = new ResponsePojo<>("User roles fetched successfully", listOfUserRoles, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Retrieves user profiles associated with a specific role."
    )
    @SuccessResponse(schema = IdBean.class, description = "User profiles fetched successfully.")
    @AopCustomAnnotation
    @RequestMapping(value = "/roles/{roleId}/profiles", method = RequestMethod.GET)
    public ResponseEntity<ResponsePojo<List<IdBean>>> getProfilesByRoleId(
            @Parameter(
                    name = "roleId",
                    description = "Unique identifier of the role",
                    required = true,
                    example = "1"
            )
            @PathVariable(value = "roleId") Long id,
            BasicUserDetails userDetails)
            throws ClientException, DataProcessingException, ServerException {
        userProfilesByRoleIdBL.clientValidation(userDetails);
        List<IdBean> listOfProfilesByRoleId = userProfilesByRoleIdBL.process(id);
        ResponsePojo<List<IdBean>> responseBean = new ResponsePojo<>("User profiles fetched successfully", listOfProfilesByRoleId, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
