package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetForensicActionsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ForensicActionsPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Forensic Actions", description = "Endpoints for managing forensic actions within accounts.")
public class ForensicActionsController {

    @Autowired
    private GetForensicActionsBL getForensicActionsBL;
    @Autowired
    private JsonFileParser headersParser;

    /**
     * Retrieves a paginated and filtered list of forensic actions for a specific account.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param pageable Pagination and sorting information.
     * @param searchTerm A search term to filter actions by name (contains search).
     * @param commandName A filter for the exact command name.
     * @param commandTimeoutInSeconds A filter for the exact command timeout.
     * @param categoryList A list of category names to filter by (OR logic).
     * @param status A filter for the exact action status.
     * @param lastModifiedBy A filter for the exact user who last modified the action.
     * @param userDetails The details of the authenticated user.
     * @return A ResponseEntity containing a paginated list of {@link ForensicActionsPojo} objects.
     */
    @Operation(
            summary = "Retrieve forensic actions",
            description = "Fetches a paginated and filtered list of forensic actions for a given account."
    )
    @SuccessResponse(schema = ForensicActionsPojo.class, description = "Forensic actions fetched successfully")
    @AopCustomAnnotation
    @GetMapping("/accounts/{accountIdentifier}/forensic-actions")
    public ResponseEntity<ResponsePojo<Page<ForensicActionsPojo>>> getForensicActions(
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=name,asc`",
                    required = true
            )
            Pageable pageable,
            @Parameter(
                    description = "Search term to filter actions by name.",
                    example = "collect")
            @RequestParam(required = false) String searchTerm,
            @RequestParam(required = false) String commandName,
            @RequestParam(required = false) String commandTimeoutInSeconds,
            @RequestParam(name = "category", required = false) List<String> categoryList,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String lastModifiedBy,
            @Parameter(
                    description = "Filter actions by type. Can be 'standard' or 'custom'.",
                    required = true,
                    example = "standard")
            @RequestParam String type,
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getForensicActions");
        UtilityBean<Object> utilityBean = getForensicActionsBL.clientValidation(accountIdentifier, searchTerm, commandName, commandTimeoutInSeconds,
                categoryList == null ? null : String.join(",", categoryList), status, lastModifiedBy, type, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        utilityBean.setPageable(pageable);
        UtilityBean<Integer> accountIdBean = getForensicActionsBL.serverValidation(utilityBean);
        Page<ForensicActionsPojo> data = getForensicActionsBL.process(accountIdBean);
        ResponsePojo<Page<ForensicActionsPojo>> responseBean = new ResponsePojo<>("Forensic actions fetched successfully.", data, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
