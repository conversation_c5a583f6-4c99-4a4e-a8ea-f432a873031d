package com.heal.controlcenter.controller;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetKpiDetailsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.KpiDetailsPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "KPI Management", description = "Operations related to Key Performance Indicators (KPIs)")
public class KpiController {

    private final GetKpiDetailsBL getKpiDetailsBL;
    private final JsonFileParser headersParser;

    public KpiController(GetKpiDetailsBL getKpiDetailsBL, JsonFileParser headersParser) {
        this.getKpiDetailsBL = getKpiDetailsBL;
        this.headersParser = headersParser;
    }

    /**
     * Retrieves a paginated and filtered list of KPIs for a specific account.
     *
     * @param accountIdentifier The unique identifier of the account.
     * @param pageable          Pagination and sorting information.
     * @param searchTerm        A search term to filter KPIs by name or identifier.
     * @param kpiType           Filter by KPI type.
     * @param group             Filter by group name.
     * @param category          Filter by category name.
     * @param component         Filter by component name.
     * @param userDetails       The details of the authenticated user.
     * @return A ResponseEntity containing a paginated list of {@link KpiDetailsPojo} objects.
     */
    @Operation(summary = "Retrieves all KPIs for the authenticated user.")
    @SuccessResponse(schema = KpiDetailsPojo.class, description = "KPIs fetched successfully.")
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{identifier}/kpis")
    public ResponseEntity<ResponsePojo<Page<KpiDetailsPojo>>> getAllKpis(
            @Parameter(description = "Account identifier to fetch associated KPIs", required = true)
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=name,asc`",
                    required = true
            )
            Pageable pageable,
            @Parameter(description = "Search term to filter KPIs by name or identifier.", example = "cpu")
            @RequestParam(required = false) String searchTerm,
            @RequestParam(required = false) String kpiType,
            @RequestParam(required = false) String group,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String component,
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : getAllKpis");
        log.debug("Request received for getAllKpis with accountIdentifier: {}, searchTerm: {}, kpiType: {}, group: {}, category: {}, component: {}", accountIdentifier, searchTerm, kpiType, group, category, component);
        UtilityBean<String> utilityBean = getKpiDetailsBL.clientValidation(null, accountIdentifier, searchTerm, kpiType, group, category, component, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        utilityBean.setPageable(pageable);
        log.debug("UtilityBean created and metadata set for user: {}", userDetails.getUserIdentifier());
        UtilityBean<Account> accountUtilityBean = getKpiDetailsBL.serverValidation(utilityBean);
        log.debug("Server validation successful for accountIdentifier: {}", accountIdentifier);
        Page<KpiDetailsPojo> data = getKpiDetailsBL.process(accountUtilityBean);
        log.debug("KPIs fetched successfully for accountIdentifier: {}. Total elements: {}", accountIdentifier, data.getTotalElements());
        ResponsePojo<Page<KpiDetailsPojo>> responseBean = new ResponsePojo<>("KPIs fetched successfully.", data, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
