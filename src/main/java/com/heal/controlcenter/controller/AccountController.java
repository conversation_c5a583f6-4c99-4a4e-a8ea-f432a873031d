package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.aop.EnableHealthMetrics;
import com.heal.controlcenter.beans.AccessDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAccountsBL;
import com.heal.controlcenter.businesslogic.PostAccountBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for managing Account resources within the Control Center application.
 * This controller provides endpoints for retrieving and creating user accounts,
 * integrating with business logic components for validation and processing.
 */
@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Accounts", description = "Endpoints for managing accounts.")
public class AccountController {

    final private GetAccountsBL getAccountsBL;
    final private JsonFileParser headersParser;
    final private PostAccountBL postAccountBL;

    public AccountController(GetAccountsBL getAccountsBL, JsonFileParser headersParser, PostAccountBL postAccountBL) {
        this.getAccountsBL = getAccountsBL;
        this.headersParser = headersParser;
        this.postAccountBL = postAccountBL;
    }

    /**
     * Retrieves all active accounts for the authenticated user with pagination, search filtering, and sorting.
     *
     * <p>This endpoint provides a paginated view of accounts, allowing clients to
     * search by term and define sorting preferences.
     *
     * @param pageable   Pagination and sorting information (e.g., page number, page size, sort order).
     * @param searchTerm An optional string to filter accounts by name or identifier.
     * @return {@link ResponseEntity} containing a paginated list of {@link Account} objects and a success message.
     */
    @Operation(summary = "Retrieve paged accounts for user",
            description = "Fetches a paginated list of all accounts accessible to the authenticated user. " +
                    "Supports searching and sorting.")
    @SuccessResponse(schema = Page.class, description = "Accounts fetched successfully.")
    @AopCustomAnnotation
    @EnableHealthMetrics
    @GetMapping(value = "/accounts")
    public ResponseEntity<ResponsePojo<Page<Account>>> getAllAccounts(@Parameter(description = "Pagination and sorting information. " +
                                                                              "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). Example: `?page=0&size=10&sort=accountName,asc`")
                                                                      Pageable pageable,
                                                                      @Parameter(description = "Search term to filter accounts by name or identifier.", example = "TestAccount")
                                                                      @RequestParam(required = false) String searchTerm, BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {

        UtilityBean<String> utilityBean = getAccountsBL.clientValidation(searchTerm, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        utilityBean.setPageable(pageable);
        UtilityBean<AccessDetailsBean> accessDetailsBean = getAccountsBL.serverValidation(utilityBean);
        Page<Account> data = getAccountsBL.process(accessDetailsBean);

        ResponsePojo<Page<Account>> responseBean = new ResponsePojo<>("Accounts fetched successfully.", data, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    /**
     * Creates a new account in the system.
     *
     * <p>This method handles the creation of a new account, performing necessary
     * validations and orchestrating the business logic for persistence and configuration.
     *
     * @param body The {@link Account} object representing the incoming account creation payload.
     * @return A {@link ResponseEntity} containing a success message and HTTP headers.
     */
    @Operation(summary = "Create a new account",
            description = "Validates and creates a new account in the system. This includes persisting to the database, " +
                    "creating default tag mappings, and updating Redis caches.",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Account details for creation. 'name' and 'identifier' are mandatory.",
                    required = true, content = @Content(mediaType = "application/json", schema = @Schema(implementation = Account.class))))
    @SuccessResponse(schema = ResponsePojo.class, description = "Accounts created successfully.")
    @AopCustomAnnotation
    @EnableHealthMetrics
    @PostMapping(value = "/accounts")
    public ResponseEntity<ResponsePojo<Account>> createAccount(@Validated @RequestBody Account body, BasicUserDetails userDetails)
            throws ClientException, ServerException, DataProcessingException {

        UtilityBean<Account> utilityBean = postAccountBL.clientValidation(body, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<Account> updatedUtilityBean = postAccountBL.serverValidation(utilityBean);
        postAccountBL.process(updatedUtilityBean);

        ResponsePojo<Account> responseBean = new ResponsePojo<>("Account created successfully.", null, HttpStatus.CREATED);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}