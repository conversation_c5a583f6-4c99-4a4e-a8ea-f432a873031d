package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ComponentInstanceBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetCompInstanceAtAccLvlBL;
import com.heal.controlcenter.businesslogic.PostComponentInstanceBL;
import com.heal.controlcenter.businesslogic.DeleteInstanceBL;
import com.heal.controlcenter.businesslogic.UpdateComponentInstanceBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.pojo.DeleteInstancePojo;

import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for managing component instances within accounts.
 * This controller provides endpoints for retrieving component instance information
 * including both pre-existing instances from the system and auto-discovered instances.
 * It supports pagination, search functionality, and returns comprehensive instance
 * details including associated services, applications, and agent mappings.
 */
@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Component Instances", description = "Endpoints for managing component instances within accounts.")
public class ComponentInstanceController {

    private final GetCompInstanceAtAccLvlBL getCompInstanceBL;
    private final PostComponentInstanceBL postComponentInstanceBL;
    private final DeleteInstanceBL deleteInstanceBL;
    private final UpdateComponentInstanceBL updateComponentInstanceBL;
    private final JsonFileParser headersParser;

    /**
     * Constructor for dependency injection.
     * @param getCompInstanceBL Business logic for component instance operations
     * @param postComponentInstanceBL Business logic for creating component instances
     * @param deleteInstanceBL Business logic for deleting component instances
     * @param updateComponentInstanceBL Business logic for updating component instances
     * @param headersParser Parser for JSON header configurations
     */
    public ComponentInstanceController(GetCompInstanceAtAccLvlBL getCompInstanceBL,
                                       PostComponentInstanceBL postComponentInstanceBL,
                                       DeleteInstanceBL deleteInstanceBL,
                                       UpdateComponentInstanceBL updateComponentInstanceBL,
                                       JsonFileParser headersParser) {
        this.getCompInstanceBL = getCompInstanceBL;
        this.postComponentInstanceBL = postComponentInstanceBL;
        this.deleteInstanceBL = deleteInstanceBL;
        this.updateComponentInstanceBL = updateComponentInstanceBL;
        this.headersParser = headersParser;
    }

    /**
     * Retrieves all component instances for the specified account with optional search filtering.
     * This endpoint fetches both pre-existing component instances from the database and
     * auto-discovered instances, merges them while removing duplicates, and applies
     * optional search filtering. The results are paginated and sorted by status,
     * last discovery time, and instance name.
     *
     * @param accountIdentifier The unique identifier of the account to fetch instances for.
     *                         Must be a valid account identifier that exists in the system.
     * @param searchTerm       Optional search term to filter instances by name, component name,
     *                        or host name. Case-insensitive partial matching is applied.
     *                        Can be null or empty to return all instances.
     * @param userDetails     The authenticated user details injected by the authentication aspect.
     *                       Contains user identifier and authorization information.
     * @param pageable        Pagination parameters including page number, size, and sorting criteria.
     *                       Supports standard Spring Data pagination format.
     * @return ResponseEntity containing a paginated list of component instances wrapped in ResponsePojo,
     *         or error response with appropriate HTTP status code and error message.
     */
    @Operation(summary = "Retrieves all component instances for the specified account.")
    @SuccessResponse(schema = GetCompInstance.class, description = "Component instances fetched successfully.")
    @AopCustomAnnotation
    @GetMapping("accounts/{accountIdentifier}/instances")
    public ResponseEntity<ResponsePojo<Page<GetCompInstance>>> getComponentInstanceDetails(
            @Parameter(
                    description = "The unique identifier of the account to fetch instances for. " +
                            "Must be a valid account identifier that exists in the system.",
                    example = "heal_health"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,

            @Parameter(
                    description = "Search term to filter instances by name, component name, or host name. " +
                            "Case-insensitive partial matching is applied. Can be null or empty to return all instances.",
                    example = "tomcat"
            )
            @RequestParam(value = "searchTerm", required = false) String searchTerm,

            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=instanceName,asc`"
            )
            Pageable pageable,            @Parameter(
                    description = "The authenticated user details injected by the authentication aspect. " +
                            "Contains user identifier and authorization information."
            )
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : GetCompInstanceAtAccLvlService/getComponentInstanceDetails");
        UtilityBean<String> utilityBean = getCompInstanceBL.clientValidation(accountIdentifier, searchTerm, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        utilityBean.setPageable(pageable);
        UtilityBean<AccountBean> accountInfo = getCompInstanceBL.serverValidation(utilityBean);
        Page<GetCompInstance> result = getCompInstanceBL.process(accountInfo);

        ResponsePojo<Page<GetCompInstance>> responseBean = new ResponsePojo<>("Component Instance(s) fetched successfully.", result, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    /**
     * Creates new component instances for the specified account.
     * This endpoint allows creation of one or more component instances with their associated
     * metadata, service mappings, and agent configurations. The instances are stored in both
     * the database and Redis cache for optimal performance.
     *
     * @param accountIdentifier The unique identifier of the account to create instances for.
     *                         Must be a valid account identifier that exists in the system.
     * @param componentInstanceRequests List of component instance requests containing all necessary
     *                                 information for instance creation including component details,
     *                                 host information, and service mappings.
     * @param userDetails The authenticated user details injected by the authentication aspect.
     *                   Contains user identifier and authorization information.
     * @return ResponseEntity containing a list of created component instance IDs wrapped in ResponsePojo,
     *         or error response with appropriate HTTP status code and error message.
     */
    @Operation(summary = "Retrieves all component instances for the specified account.")
    @SuccessResponse(schema = IdPojo.class, description = "Component instances created successfully.")
    @AopCustomAnnotation
    @PostMapping("accounts/{accountIdentifier}/instances")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> createComponentInstances(
            @Parameter(
                    description = "The unique identifier of the account to create instances for. " +
                            "Must be a valid account identifier that exists in the system.",
                    example = "heal_health"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,

            @Parameter(
                    description = "List of component instance requests containing all necessary " +
                            "information for instance creation including component details, " +
                            "host information, and service mappings.",
                    required = true
            )
            @RequestBody List<ComponentInstancePojo> componentInstanceRequests,

            @Parameter(
                    description = "The authenticated user details injected by the authentication aspect. " +
                            "Contains user identifier and authorization information."
            )
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
            log.trace("Method Invoked : createComponentInstances");
            UtilityBean<List<ComponentInstancePojo>> utilityBean = postComponentInstanceBL.clientValidation(componentInstanceRequests, accountIdentifier, userDetails);
            utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
            UtilityBean<List<ComponentInstanceBean>> validatedBean = postComponentInstanceBL.serverValidation(utilityBean);
            List<IdPojo> result = postComponentInstanceBL.process(validatedBean);

            ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Component Instance(s) created successfully.", result, HttpStatus.OK);
            return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    /**
     * Updates component instance details following appsone-controlcenter pattern.
     * This endpoint allows updating instance name, environment, and application service mappings.
     * It supports adding/removing service mappings and updating instance metadata.
     * The updates are applied to both database and Redis cache for consistency.
     *
     * @param accountIdentifier The unique identifier of the account containing the instances.
     *                         Must be a valid account identifier that exists in the system.
     * @param updateRequests   List of component instance update requests containing instance
     *                        details and the fields to be updated (name, environment, service mappings).
     * @param userDetails The authenticated user details injected by the authentication aspect.
     *                   Contains user identifier and authorization information.
     * @return ResponseEntity containing a list of updated component instance IDs wrapped in ResponsePojo,
     *         or error response with appropriate HTTP status code and error message.
     */
    @Operation(summary = "Updates component instance details following appsone-controlcenter pattern.")
    @SuccessResponse(schema = IdPojo.class, description = "Component instances updated successfully.")
    @AopCustomAnnotation
    @PutMapping("accounts/{accountIdentifier}/instances/details")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> updateComponentInstances(
            @Parameter(
                    description = "The unique identifier of the account containing the instances. " +
                            "Must be a valid account identifier that exists in the system.",
                    example = "heal_health"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,

            @Parameter(
                    description = "List of component instance update requests containing instance " +
                            "details and the fields to be updated. Supports updating name, environment, " +
                            "and application service mappings (add/remove services).",
                    required = true
            )
            @RequestBody List<UpdateInstancePojo> updateRequests,

            @Parameter(
                    description = "The authenticated user details injected by the authentication aspect. " +
                            "Contains user identifier and authorization information."
            )
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : updateComponentInstanceDetails for accountIdentifier: {} with {} requests",
                accountIdentifier, updateRequests != null ? updateRequests.size() : 0);

        // Use the UpdateComponentInstanceBL following appsone-controlcenter pattern
        UtilityBean<List<UpdateInstancePojo>> utilityBean = updateComponentInstanceBL.clientValidation(updateRequests, accountIdentifier, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<List<UpdateInstancePojo>> validatedBean = updateComponentInstanceBL.serverValidation(utilityBean);
        List<IdPojo> result = updateComponentInstanceBL.process(validatedBean);

        ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Component Instance(s) updated successfully.", result, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    /**
     * Deletes component instances for the specified account.
     * This endpoint permanently removes component instances and all their related data.
     * The instances are also removed from Redis cache for optimal performance.
     *
     * @param accountIdentifier The unique identifier of the account to delete instances from.
     *                         Must be a valid account identifier that exists in the system.
     * @param deleteRequest Request containing list of instance identifiers to delete.
     * @param userDetails The authenticated user details injected by the authentication aspect.
     *                   Contains user identifier and authorization information.
     * @return ResponseEntity containing a success message wrapped in ResponsePojo,
     *         or error response with appropriate HTTP status code and error message.
     */
    @Operation(summary = "Deletes component instances for the specified account.")
    @SuccessResponse(schema = String.class, description = "Component instances deleted successfully.")
    @AopCustomAnnotation
    @DeleteMapping("accounts/{accountIdentifier}/instances")
    public ResponseEntity<ResponsePojo<String>> deleteComponentInstances(
            @Parameter(
                    description = "The unique identifier of the account to delete instances from. " +
                            "Must be a valid account identifier that exists in the system.",
                    example = "heal_health"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,

            @Parameter(
                    description = "Request containing list of instance identifiers to delete.",
                    required = true
            )
            @RequestBody DeleteInstancePojo deleteRequest,

            @Parameter(
                    description = "The authenticated user details injected by the authentication aspect. " +
                            "Contains user identifier and authorization information."
            )
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked : deleteComponentInstances for accountIdentifier: {} with request: {}",
                accountIdentifier, deleteRequest);

        UtilityBean<DeleteInstancePojo> utilityBean = deleteInstanceBL.clientValidation(deleteRequest, accountIdentifier, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<DeleteInstancePojo> validatedBean = deleteInstanceBL.serverValidation(utilityBean);
        String message = deleteInstanceBL.process(validatedBean);

        ResponsePojo<String> responseBean = new ResponsePojo<>(message, null, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
