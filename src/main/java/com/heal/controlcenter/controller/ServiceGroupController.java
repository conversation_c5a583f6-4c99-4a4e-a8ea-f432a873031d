package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.AccountServiceValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServiceGroupListPage;
import com.heal.controlcenter.businesslogic.GetAccountServiceGroupsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Service Groups", description = "Endpoints for managing service groups within accounts.")
public class ServiceGroupController {

    private final GetAccountServiceGroupsBL getAccountServiceGroupsBL;
    private final JsonFileParser headersParser;

    public ServiceGroupController(GetAccountServiceGroupsBL getAccountServiceGroupsBL, JsonFileParser headersParser) {
        this.getAccountServiceGroupsBL = getAccountServiceGroupsBL;
        this.headersParser = headersParser;
    }

    @Operation(
            summary = "Retrieve Service Groups for Account"
    )
    @SuccessResponse(schema = Page.class, description = "Service groups retrieved successfully")
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{accountIdentifier}/service-groups")
    public ResponseEntity<ResponsePojo<Page<ServiceGroupListPage>>> getAccountServiceGroups(
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=name,asc`"
            )
            Pageable pageable,
            @Parameter(
                    description = "Search term to filter service groups by name or identifier.",
                    example = "TestGroup")
            @RequestParam(required = false) String searchTerm, BasicUserDetails userDetails)
            throws ClientException, DataProcessingException, ServerException {

        UtilityBean<Object> utilityBean = getAccountServiceGroupsBL.clientValidation(accountIdentifier, searchTerm, userDetails);
        utilityBean.setPageable(pageable);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<AccountServiceValidationBean> validatedBean = getAccountServiceGroupsBL.serverValidation(utilityBean);
        Page<ServiceGroupListPage> serviceGroups = getAccountServiceGroupsBL.process(validatedBean);

        ResponsePojo<Page<ServiceGroupListPage>> responsePojo = new ResponsePojo<>("success", serviceGroups, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}
