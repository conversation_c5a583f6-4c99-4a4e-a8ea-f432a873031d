package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.GetHost;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetHostsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Host Management", description = "Operations related to host discovery and management")
public class HostController {

    private final JsonFileParser headersParser;
    private final GetHostsBL getHostsBL;

    public HostController(JsonFileParser headersParser, GetHostsBL getHostsBL) {
        this.headersParser = headersParser;
        this.getHostsBL = getHostsBL;
    }

    /**
     * Retrieves a paginated list of discovered hosts for a given account.
     *
     * <p>This endpoint performs the following steps:
     * <ul>
     *   <li>Validates the client request including authorization and search term</li>
     *   <li>Performs server-side validation including user access checks</li>
     *   <li>Processes the business logic to fetch host details (with filtering, sorting, pagination)</li>
     *   <li>Wraps the result in a standardized response object</li>
     * </ul>
     *
     * @param accountIdentifier Unique identifier for the target account (passed as path variable)
     * @param pageable        Spring Data Pageable object populated automatically from query params (`?page=0&size=10`)
     * @param searchTerm      Optional search term for filtering hosts based on name, application, status, etc.
     * @return ResponseEntity containing a paginated list of `GetHost` objects wrapped in a `ResponsePojo`
     *
     */
    @Operation(summary = "Retrieve discovered hosts",
            description = "Fetches all discovered hosts with their status, connections, and associated services/applications"
    )
    @SuccessResponse(schema = GetHost.class, description = "Hosts fetched successfully")
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{identifier}/hosts")
    public ResponseEntity<ResponsePojo<Page<GetHost>>> getAllHosts(
            @PathVariable("identifier") String accountIdentifier,
            @Parameter(
                    description = "Pagination information. Use `page` (0-indexed) and `size` (items per page). " +
                            "Example: `?page=0&size=10`"
            )
            Pageable pageable,
            @Parameter(
                    description = "Search term to filter hosts by name, application, type, etc.",
                    required = false,
                    example = "production"
            )
            @RequestParam(required = false) String searchTerm, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {

        log.trace("Invoked getAllHosts for account: {}", accountIdentifier);
        // Step 1: Client validation
        UtilityBean<Object> utilityBean = getHostsBL.clientValidation(accountIdentifier, searchTerm, userDetails);
        utilityBean.setPageable(pageable);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        // Step 2: Server validation
        UtilityBean<AccountBean> accessDetailsBean = getHostsBL.serverValidation(utilityBean);
        // Step 3: Process business logic (returns Page<GetHost> now)
        Page<GetHost> hosts = getHostsBL.process(accessDetailsBean);
        // Step 4: Prepare response
        ResponsePojo<Page<GetHost>> responseBean = new ResponsePojo<>("Hosts fetched successfully", hosts, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }
}
