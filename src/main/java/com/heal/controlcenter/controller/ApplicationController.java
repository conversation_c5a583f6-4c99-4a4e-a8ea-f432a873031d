package com.heal.controlcenter.controller;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.ApplicationBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.*;
import com.heal.controlcenter.businesslogic.PostApplicationsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * REST Controller for retrieving applications associated with an account.
 * This controller provides endpoints for fetching applications with optional cluster data and search filters.
 */
@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Applications", description = "Endpoints for managing applications within accounts.")
public class ApplicationController {

    private final GetApplicationsBL getApplicationsBL;
    private final JsonFileParser headersParser;
    private final PostApplicationsBL postApplicationsBL;
    private final PutApplicationsBL putApplicationsBL;
    private final DeleteApplicationsBL deleteApplicationsBL;
    private final GetApplicationPercentilesBL getApplicationPercentilesBL;
    private final PutApplicationPercentilesBL putApplicationPercentilesBL;

    public ApplicationController(GetApplicationsBL getApplicationsBL, JsonFileParser headersParser,
                                 PostApplicationsBL postApplicationsBL, PutApplicationsBL putApplicationsBL,
                                 DeleteApplicationsBL deleteApplicationsBL,
                                 GetApplicationPercentilesBL getApplicationPercentilesBL,
                                 PutApplicationPercentilesBL putApplicationPercentilesBL) {
        this.getApplicationsBL = getApplicationsBL;
        this.headersParser = headersParser;
        this.postApplicationsBL = postApplicationsBL;
        this.putApplicationsBL = putApplicationsBL;
        this.deleteApplicationsBL = deleteApplicationsBL;
        this.getApplicationPercentilesBL = getApplicationPercentilesBL;
        this.putApplicationPercentilesBL = putApplicationPercentilesBL;
    }

    /**
     * Retrieves paginated list of applications for the specified account.
     * <p>
     * This endpoint performs the following operations:
     * 1. Validates client input (authorization token, account identifier)
     * 2. Performs server-side validation of the account
     * 3. Retrieves applications with optional search filtering and cluster data inclusion
     * 4. Returns paginated results with standard response headers
     *
     * @param accountIdentifier     Unique identifier of the account (required)
     * @param clusterDataRequired   Flag to include cluster data (default: true)
     * @param searchTerm            Optional term to filter applications by name/identifier
     * @param pageable             Pagination and sorting parameters
     * @return ResponseEntity containing paginated ApplicationPojo results
     */
    @Operation(
            summary = "Retrieves applications for the specified account.",
            description = "Returns a paginated list of applications linked to the given account identifier. " +
                    "Optionally filters results using a search term and includes cluster data if requested."
    )
    @SuccessResponse(schema = ApplicationPojo.class, description = "Applications fetched successfully.")
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{identifier}/applications")
    public ResponseEntity<ResponsePojo<Page<ApplicationPojo>>> getApplications(
            @Parameter(
                    description = "Account identifier to fetch associated applications",
                    required = true
            )
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    description = "Boolean flag indicating whether cluster data is required",
                    example = "true"
            )
            @RequestParam(value = "clusterDataRequired", required = false, defaultValue = "true") String clusterDataRequired,
            @Parameter(
                    description = "Search term to filter applications by name or identifier",
                    example = "InventoryApp"
            )
            @RequestParam(value = "searchTerm", required = false, defaultValue = "") String searchTerm,
            @Parameter(
                    hidden = true
            )
            Pageable pageable, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.info("Request received to fetch applications for account [{}] with parameters - clusterDataRequired: {}, searchTerm: {}, pageable: {}",
                accountIdentifier, clusterDataRequired, searchTerm, pageable);
        log.debug("Performing client validation for account [{}]", accountIdentifier);
        UtilityBean<String> applicationBean = getApplicationsBL.clientValidation(accountIdentifier, clusterDataRequired, searchTerm, userDetails);
        applicationBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        applicationBean.setPageable(pageable);

        UtilityBean<Account> account = getApplicationsBL.serverValidation(applicationBean);
        Page<ApplicationPojo> listOfApplications = getApplicationsBL.process(account);

        ResponsePojo<Page<ApplicationPojo>> responsePojo =
                new ResponsePojo<>("Applications fetched successfully", listOfApplications, HttpStatus.OK);

        log.info("Successfully fetched {} applications for account [{}]",
                listOfApplications.getNumberOfElements(), accountIdentifier);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    /**
     * Adds new applications for the specified account.
     * <p>
     * This endpoint performs the following operations:
     * 1. Validates the input application list and account identifier (client-side)
     * 2. Performs server-side validation for account, user, and application uniqueness
     * 3. Processes and persists the applications, including tags, anomaly configs, notification preferences, and percentiles
     * 4. Returns a list of created application IDs
     *
     * @param accountIdentifier Unique identifier of the account (from path variable)
     * @param body              List of Application objects to be added (from request body)
     * @param userDetails       Authenticated user details (injected)
     * @return ResponseEntity containing a list of created application IDs
     */
    @Operation(
            summary = "Adds a new application for the specified account."
    )
    @SuccessResponse(schema = Application.class, description = "Application added successfully.")
    @AopCustomAnnotation
    @PostMapping(value = "/accounts/{identifier}/applications")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> addApplications(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    description = "Application details to add (list)",
                    required = true
            )
            @RequestBody List<Application> body, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.info("Request received to add {} applications for account [{}] by user [{}]", body != null ? body.size() : 0, accountIdentifier, userDetails.getUserIdentifier());
        UtilityBean<List<Application>> applicationBean = postApplicationsBL.clientValidation(body, accountIdentifier, userDetails);
        applicationBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());

        UtilityBean<List<ApplicationBean>> bean = postApplicationsBL.serverValidation(applicationBean);
        List<IdPojo> idPojoList = postApplicationsBL.process(bean);

        log.info("Successfully added {} applications for account [{}]", idPojoList.size(), accountIdentifier);
        ResponsePojo<List<IdPojo>> responsePojo = new ResponsePojo<>("Applications added successfully", idPojoList, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    /**
     * Updates an existing application.
     *
     * @param accountIdentifier Unique identifier of the account
     * @param body List of applications to update
     * @param userDetails Details of the authenticated user
     * @return A response entity indicating success or failure
     */
    @Operation(
            summary = "Updates an existing application",
            description = "Updates allowed fields (name, environment, timezone) for an existing application. Identifier and linkedEnvironment cannot be updated.",
            security = @SecurityRequirement(name = "bearerAuth"),
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Application details for update",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = Application.class)
                    )
            )
    )
    @SuccessResponse(description = "Application updated successfully")
    @AopCustomAnnotation
    @PutMapping(value = "/accounts/{identifier}/applications")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> updateApplication(
            @PathVariable(value = "identifier") String accountIdentifier,
            @RequestBody List<Application> body, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked: updateApplication");

        UtilityBean<List<Application>> utilityBean = putApplicationsBL.clientValidation(body,accountIdentifier, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<List<ApplicationBean>> validatedBean = putApplicationsBL.serverValidation(utilityBean);
        putApplicationsBL.process(validatedBean);
        ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Application updated successfully.", null, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    @Operation(
            summary = "Retrieves application percentiles for a given account and application.",
            description = "Returns a list of percentile configurations for the specified application within an account."
    )
    @SuccessResponse(schema = ApplicationPercentilePojo.class, description = "Application percentiles fetched successfully.")
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{accountIdentifier}/applications/{applicationId}/percentiles")
    public ResponseEntity<ResponsePojo<List<ApplicationPercentilePojo>>> getApplicationPercentiles(
            @Parameter(
                    description = "Account identifier to fetch associated application percentiles",
                    required = true
            )
            @PathVariable(value = "accountIdentifier") String accountIdentifier,
            @Parameter(
                    description = "Application ID to fetch percentiles for",
                    required = true
            )
            @PathVariable(value = "applicationId") String applicationId,
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.info("Request received to fetch application percentiles for account [{}] and application [{}]", accountIdentifier, applicationId);
        UtilityBean<String> utilityBean = getApplicationPercentilesBL.clientValidation(accountIdentifier, applicationId, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());

        UtilityBean<String> validatedBean = getApplicationPercentilesBL.serverValidation(utilityBean);
        List<ApplicationPercentilePojo> percentiles = getApplicationPercentilesBL.process(validatedBean);

        ResponsePojo<List<ApplicationPercentilePojo>> responsePojo =
                new ResponsePojo<>("Application percentiles fetched successfully", percentiles, HttpStatus.OK);

        log.info("Successfully fetched {} percentiles for application [{}] in account [{}]", percentiles.size(), applicationId, accountIdentifier);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Updates application percentiles for a given account and application.",
            description = "Updates a list of percentile configurations for the specified application within an account."
    )
    @SuccessResponse(schema = ApplicationPercentilePojo.class, description = "Application percentiles updated successfully.")
    @AopCustomAnnotation
    @PutMapping(value = "/accounts/{accountIdentifier}/applications/{applicationId}/percentiles")
    public ResponseEntity<ResponsePojo<Void>> updateApplicationPercentiles(
            @Parameter(
                    description = "Account identifier to update associated application percentiles",
                    required = true
            )
            @PathVariable(value = "accountIdentifier") String accountIdentifier,
            @Parameter(
                    description = "Application ID to update percentiles for",
                    required = true
            )
            @PathVariable(value = "applicationId") String applicationId,
            @Parameter(
                    description = "List of Application Percentile details to update",
                    required = true
            )
            @RequestBody List<ApplicationPercentilePojo> body, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.info("Request received to update application percentiles for account [{}] and application [{}]", accountIdentifier, applicationId);
        UtilityBean<List<ApplicationPercentilePojo>> utilityBean = putApplicationPercentilesBL.clientValidation(body, accountIdentifier, applicationId, userDetails);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());

        UtilityBean<List<ApplicationPercentilePojo>> validatedBean = putApplicationPercentilesBL.serverValidation(utilityBean);
        putApplicationPercentilesBL.process(validatedBean);

        ResponsePojo<Void> responsePojo =
                new ResponsePojo<>("Application percentiles updated successfully", null, HttpStatus.OK);

        log.info("Successfully updated application percentiles for application [{}] in account [{}]", applicationId, accountIdentifier);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

    @Operation(
            summary = "Deletes applications for the specified account."
    )
    @SuccessResponse(description = "Application(s) removed successfully.")
    @AopCustomAnnotation
    @DeleteMapping(value = "accounts/{identifier}/applications")
    public ResponseEntity<ResponsePojo<String>> deleteApplications(
            @Parameter(
                    name = "identifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    description = "Application details to add (list)",
                    required = true
            )
            @RequestBody DeleteApplicationsPojo body, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked: deleteApplications");

        UtilityBean<DeleteApplicationsPojo> applicationList = deleteApplicationsBL.clientValidation(body, accountIdentifier, userDetails);
        applicationList.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<List<ControllerBean>> controllerBeanList = deleteApplicationsBL.serverValidation(applicationList);
        String message = deleteApplicationsBL.process(controllerBeanList);

        ResponsePojo<String> responsePojo = new ResponsePojo<>(message, null, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }
}