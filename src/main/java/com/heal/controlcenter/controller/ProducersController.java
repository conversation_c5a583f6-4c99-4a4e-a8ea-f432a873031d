package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.ProducerKpiValidationBean;
import com.heal.controlcenter.beans.ProducerValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetProducersBL;
import com.heal.controlcenter.businesslogic.GetProducerMappedKpisBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.GetProducerPojo;
import com.heal.controlcenter.pojo.ProducerKpiMappingPojo;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@Slf4j
@RestController
@ErrorResponse
@Tag(name = "Producers", description = "Endpoints for managing producers within accounts.")
public class ProducersController {
    private final GetProducersBL getProducersBL;
    private final JsonFileParser headersParser;
    private final GetProducerMappedKpisBL getProducerMappedKpisBL;

    public ProducersController(GetProducersBL getProducersBL, HealthMetrics healthMetrics, JsonFileParser headersParser,
                               GetProducerMappedKpisBL getProducerMappedKpisBL) {
        this.getProducersBL = getProducersBL;
        this.headersParser = headersParser;
        this.getProducerMappedKpisBL = getProducerMappedKpisBL;
    }

    /**
     * Retrieves a list of producers for a given account.
     *
     * @param accountIdentifier The identifier of the account.
     * @param pageable          Pagination and sorting information.
     * @return A ResponseEntity containing a list of producers.
     */
    @Operation(summary = "Retrieves all Producers for the authenticated user.")
    @SuccessResponse(schema = GetProducerPojo.class, description = "Successfully retrieved producers")
    @AopCustomAnnotation
    @GetMapping("/accounts/{accountIdentifier}/producers")
    public ResponseEntity<ResponsePojo<Page<GetProducerPojo>>> getProducers(@PathVariable(value = "accountIdentifier") String accountIdentifier,
                                                                            @RequestParam(value = "name", required = false) String name,
                                                                            @RequestParam(value = "status", required = false) String status,
                                                                            @RequestParam(value = "producerType", required = false) String producerType,
                                                                            @RequestParam(value = "isCustom") int isCustom,
                                                                            Pageable pageable, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("GetProducers API called with account identifier: {}", accountIdentifier);
        UtilityBean<String> clientBean = getProducersBL.clientValidation(accountIdentifier, name,
                status, producerType, String.valueOf(isCustom), userDetails);
        clientBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        clientBean.setPageable(pageable);
        UtilityBean<ProducerValidationBean> serverBean = getProducersBL.serverValidation(clientBean);
        Page<GetProducerPojo> producers = getProducersBL.process(serverBean);
        ResponsePojo<Page<GetProducerPojo>> responseBean = new ResponsePojo<>("Producers fetching successful.", producers, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

    /**
     * Retrieves mapped KPIs for a given producer with pagination support.
     */
    @Operation(summary = "Get mapped KPIs for a producer")
    @AopCustomAnnotation
    @SuccessResponse(schema = GetProducerPojo.class, description = "Successfully retrieved mapped kpis")
    @GetMapping("/accounts/{accountIdentifier}/producers/{producerId}/mapped-kpis")
    public ResponseEntity<ResponsePojo<Page<ProducerKpiMappingPojo>>> getProducerMappedKpis(
            @PathVariable("accountIdentifier") String accountIdentifier,
            @PathVariable("producerId") Integer producerId,
            Pageable pageable,
            BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("getProducerMappedKpis API called with accountIdentifier: {}, producerId: {}", accountIdentifier, producerId);

        UtilityBean<Void> clientBean = getProducerMappedKpisBL.clientValidation(accountIdentifier, String.valueOf(producerId), userDetails);
        clientBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        clientBean.setPageable(pageable);
        UtilityBean<ProducerKpiValidationBean> serverBean = getProducerMappedKpisBL.serverValidation(clientBean);
        Page<ProducerKpiMappingPojo> resultPage = getProducerMappedKpisBL.process(serverBean);
        ResponsePojo<Page<ProducerKpiMappingPojo>> responseBean = new ResponsePojo<>("Producer KPI mapping fetched successfully.", resultPage, HttpStatus.OK);
        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responseBean);
    }

}
