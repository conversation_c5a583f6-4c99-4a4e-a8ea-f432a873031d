package com.heal.controlcenter.exception;

import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.JsonFileParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.time.Instant;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@ControllerAdvice
public class ExceptionHandler extends ResponseEntityExceptionHandler {

    @Autowired
    JsonFileParser headersParser;
    @Autowired
    HealthMetrics healthMetrics;

    public static final String LIST_JOIN_DELIMITER = ",";
    private static final String ERRORS_FOR_PATH = "errors {} for path {}";
    private static final String PATH = "path";
    private static final String ERRORS = "error";
    private static final String STATUS = "status";
    private static final String MESSAGE = "message";
    private static final String TIMESTAMP = "timestamp";
    private static final String TYPE = "type";

    @org.springframework.web.bind.annotation.ExceptionHandler(value = {DataProcessingException.class, ClientException.class, ServerException.class})
    public ResponseEntity<Object> handleHealExceptions(Exception exception, WebRequest request) {
        final HttpStatus status = HttpStatus.BAD_REQUEST;
        final String localizedMessage = exception.getLocalizedMessage();
        final String path = request.getDescription(false);

        healthMetrics.updateHealControlCenterErrors();
        healthMetrics.addStatusCodes(Constants.SERVER_PARAMETER_VALIDATION_FAILED_STATUS_CODE, 1);

        String message = (StringUtils.isNotEmpty(localizedMessage) ? localizedMessage:status.getReasonPhrase());
        log.error("message: {}, requested uri: {}", message, path, exception);

        ResponsePojo<Map<String, Object>> responsePojo = getExceptionResponseEntity(exception, status, request, Collections.singletonList(message));
        return ResponseEntity.badRequest().body(new ResponseEntity<>(responsePojo, headersParser.loadHeaderConfiguration(), status));
    }

    @org.springframework.web.bind.annotation.ExceptionHandler(value = {Exception.class})
    public ResponseEntity<Object> handleAllExceptions(Exception exception, WebRequest request) {
        final HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        final String localizedMessage = exception.getLocalizedMessage();
        final String path = request.getDescription(false);

        healthMetrics.updateHealControlCenterErrors();
        healthMetrics.addStatusCodes(Constants.INTERNAL_SERVER_ERROR_STATUS_CODE, 1);

        String message = (StringUtils.isNotEmpty(localizedMessage) ? localizedMessage:status.getReasonPhrase());
        log.error("message: {}, requested uri: {}", message, path, exception);

        ResponsePojo<Map<String, Object>> responsePojo = getExceptionResponseEntity(exception, status, request, Collections.singletonList(message));
        return ResponseEntity.internalServerError().body(new ResponseEntity<>(responsePojo, headersParser.loadHeaderConfiguration(), status));
    }

    private ResponsePojo<Map<String, Object>> getExceptionResponseEntity(final Exception exception,
                                                              final HttpStatus status,
                                                              final WebRequest request,
                                                              final List<String> errors) {
        final Map<String, Object> body = new LinkedHashMap<>();
        final String path = request.getDescription(false);
        body.put(TIMESTAMP, Instant.now());
        body.put(STATUS, status.value());
        body.put(ERRORS, errors);
        body.put(TYPE, exception.getClass().getSimpleName());
        body.put(PATH, path);
        body.put(MESSAGE, exception.getMessage());
        final String errorsMessage = CollectionUtils.isNotEmpty(errors) ?
                errors.stream().filter(StringUtils::isNotEmpty).collect(Collectors.joining(LIST_JOIN_DELIMITER))
                :status.getReasonPhrase();
        log.error(ERRORS_FOR_PATH, errorsMessage, path);
        return new ResponsePojo<>(exception.getMessage(), body, status);
    }
}
