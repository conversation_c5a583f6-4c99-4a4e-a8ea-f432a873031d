package com.heal.controlcenter.aop;

import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.KeyCloakAuthService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class UserAuthAspect {

    @Autowired
    KeyCloakAuthService keyCloakAuthService;
    @Autowired
    HttpServletRequest request;
    @Autowired
    HttpServletResponse response;
    @Autowired
    HealthMetrics healthMetrics;

    @Autowired
    @Qualifier("headerConfigurations")
    Map<String, String> headerConfigs;

    /**
     * Pointcut that matches any controller method annotated with @AopCustomAnnotation.
     */
    @Pointcut("@annotation(AopCustomAnnotation)")
    public void controllerClassMethodsPointcut() {
    }

    /**
     * Aspect advice that runs before controller methods matched by the pointcut.
     * <p>
     * Performs authentication and authorization checks using Keycloak, validates the user,
     * injects user details into the controller method, and handles exceptions and metrics.
     *
     * @param joinPoint the join point representing the intercepted method
     * @return the result of the intercepted method execution
     * @throws Throwable if authentication fails or the intercepted method throws an exception
     */
    @Around("controllerClassMethodsPointcut()")
    public Object beforeControllerAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        String currentAPI = joinPoint.getSignature().getName();
        long st = System.currentTimeMillis();

        log.trace("controllerClassMethodsPointcut is called in beforeControllerAdvice with jointPoint:{}, API:{}", joinPoint.getSignature(), currentAPI);

        if (request == null) {
            log.error("Request object is null");
            healthMetrics.updateHealControlCenterErrors();
            throw new Exception("Request is null");
        }

        String authKey = request.getHeader(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            healthMetrics.updateUnauthorizedRequests();
            log.warn("Missing Authorization header in API: {}", currentAPI);
            throw new HealControlCenterException("Missing or invalid authorization token", String.valueOf(HttpStatus.UNAUTHORIZED.value()));
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        AopCustomAnnotation annotation = signature.getMethod().getAnnotation(AopCustomAnnotation.class);


        boolean skipAuthTokenValidation = annotation.skipAuthTokenValidation();
        if (skipAuthTokenValidation) {
            log.info("Skipping auth token validation check for API: {}", currentAPI);
        } else {
            if (!keyCloakAuthService.isValidKey(authKey)) {
                healthMetrics.updateUnauthorizedRequests();
                log.warn("Invalid Keycloak token in API: {}", currentAPI);
                throw new HealControlCenterException("You are not welcome here", HttpStatus.UNAUTHORIZED.value());
            }
        }

        BasicUserDetails userDetails = keyCloakAuthService.verifyUserStatus(authKey);
        if (userDetails == null || userDetails.getUserIdentifier() == null
                || userDetails.getUserIdentifier().trim().isEmpty() || !userDetails.getUserStatus()) {
            log.warn("Inactive or invalid user for API: {}", currentAPI);
            healthMetrics.updateUnauthorizedRequests();
            throw new HealControlCenterException("You are dormant", HttpStatus.UNAUTHORIZED.value());
        }

        /*boolean skipUserAPIAccessCheck = annotation.skipUserAPIAccessCheck();
        if (skipUserAPIAccessCheck) {
            log.info("Skipping user access check for API: {}", currentAPI);
        } else {
            boolean userHasAccess = checkUserAccess(userDetails.getProfileId(), currentAPI);
            if (!userHasAccess) {
                healthMetrics.updateUnauthorizedRequests();
                throw new HealControlCenterException("You do not have access to the resource", HttpStatus.UNAUTHORIZED.value());
            }
        }*/

        log.debug("Basic validation time took for API:{} is {}ms", currentAPI, System.currentTimeMillis() - st);

        // Inject userDetails into the method argument of type BasicUserDetails
        joinPoint.getArgs()[joinPoint.getArgs().length - 1] = userDetails;

        // Proceed with method execution
        Object result = joinPoint.proceed(joinPoint.getArgs());

        log.debug("Total execution time of API:{} is {}ms", currentAPI, System.currentTimeMillis() - st);

        headerConfigs.forEach((key, value) -> response.setHeader(key, value));
        return result;
    }

    /**
     * TODO: After Percona updates, revisit this method for GraphQL query access checks.
     * This method is currently commented out and should be updated or removed post-Percona.
     */
   /*private boolean checkUserAccess(int profileId, String gqlQuery) {
       List<AccessProfileGqlQueries> gqlQueries = cacheWrapper.getAccessProfileGqlQueries(profileId);
       if (gqlQueries == null || gqlQueries.isEmpty()) {
           return false;
       }

       Optional<AccessProfileGqlQueries> accessibleQueries = gqlQueries.stream()
               .filter(accessProfileGqlQueries -> accessProfileGqlQueries.getGqlQueryName().equals(gqlQuery))
               .findFirst();

       return accessibleQueries.isPresent();
   }*/
}