package com.heal.controlcenter.aop;

import com.heal.controlcenter.util.HealthMetrics;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * An aspect that intercepts methods annotated with {@link EnableHealthMetrics}.
 * It measures the execution time of the intercepted method and records the metrics
 * using the {@link HealthMetrics}.
 */
@Aspect
@Component
public class HealthMetricsAspect {

    private final HealthMetrics healthMetrics;

    public HealthMetricsAspect(HealthMetrics healthMetrics) {
        this.healthMetrics = healthMetrics;
    }

    /**
     * An "around" advice that wraps the execution of methods annotated with {@link EnableHealthMetrics}.
     * It measures the execution time and updates the health metrics.
     *
     * @param joinPoint The join point representing the intercepted method.
     * @return The result of the original method call.
     * @throws Throwable If the intercepted method throws an exception.
     */
    @Around("@annotation(com.heal.controlcenter.aop.EnableHealthMetrics)")
    public Object measureExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        // Record the start time of the method execution.
        long startTime = System.currentTimeMillis();
        try {
            // Proceed with the actual method execution.
            return joinPoint.proceed();
        } finally {
            // This block will always execute, even if an exception is thrown.
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            // Get a short string representation of the method signature to use as the API name.
            String apiName = joinPoint.getSignature().toShortString();
            // Update the metrics in the HealthMetrics.
            healthMetrics.updateResponseTime(apiName, executionTime);
        }
    }
}
