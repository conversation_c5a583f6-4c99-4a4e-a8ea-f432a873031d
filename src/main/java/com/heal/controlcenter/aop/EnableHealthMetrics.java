package com.heal.controlcenter.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * This annotation, when applied to a method, enables the collection of health metrics for that method's execution.
 * It is intended to be used on controller methods to monitor API endpoint performance.
 * The {@link HealthMetricsAspect} intercepts methods annotated with this annotation.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface EnableHealthMetrics {
}