package com.heal.controlcenter.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AopCustomAnnotation {
    boolean skipAuthTokenValidation() default false;

    boolean skipUserAPIAccessCheck() default false;
}