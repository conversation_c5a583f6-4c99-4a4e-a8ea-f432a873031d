package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.heal.configuration.pojos.TenantDetails;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import static com.heal.controlcenter.util.Constants.ALLOWED_IDENTIFIER_CHARACTERS;
import static com.heal.controlcenter.util.Constants.ALLOWED_NAME_CHARACTERS;
import com.heal.controlcenter.util.UIMessages;
import com.heal.controlcenter.util.ValidationUtils;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Account {

    private Integer accountId;
    private String accountName;
    private String identifier;
    private int status;
    @JsonIgnore
    private String privateKey;
    @JsonIgnore
    private String publicKey;
    private List<Tags> tags;
    private long timezoneMilli;
    private String timeZoneString;
    private Long updatedTime;
    private String lastModifiedBy;
    private String dateFormat;
    private String timeFormat;
    private ThresholdSeverity thresholdSeverity;
    private int closingWindow;
    private int maxDataBreaks;
    private TenantDetails tanantDetails;


    public Map<String, String> validate() {
        final int ACCOUNT_NAME_MIN_LENGTH = 2;
        final int ACCOUNT_NAME_MAX_LENGTH = 32;
        final int IDENTIFIER_MIN_LENGTH = 2;
        final int IDENTIFIER_MAX_LENGTH = 64;

        Map<String, String> errors = new HashMap<>();

        // Account Name Validation
        ValidationUtils.validateField(accountName, "accountName", ACCOUNT_NAME_MIN_LENGTH, ACCOUNT_NAME_MAX_LENGTH,
                ALLOWED_NAME_CHARACTERS, UIMessages.EMPTY_ACCOUNT_NAME, UIMessages.INVALID_ACCOUNT_NAME_LENGTH,
                UIMessages.INVALID_ACCOUNT_NAME, errors);

        //Account Identifier validation
        if (identifier == null || identifier.trim().isEmpty()) {
            this.identifier = UUID.randomUUID().toString();
        } else {
            ValidationUtils.validateField(identifier, "identifier", IDENTIFIER_MIN_LENGTH, IDENTIFIER_MAX_LENGTH,
                    ALLOWED_IDENTIFIER_CHARACTERS, null, UIMessages.INVALID_IDENTIFIER_LENGTH,
                    UIMessages.INVALID_IDENTIFIER, errors);
        }

        //Tags Validation
        if (tags != null && !tags.isEmpty()) {
            int index = 0;
            for (Tags tag : tags) {
                if (tag.getName() == null || tag.getName().trim().isEmpty()) {
                    String message = UIMessages.EMPTY_TAG_NAME + " at index " + index;
                    log.error(message);
                    errors.put("tagRequests[" + index + "].name", UIMessages.EMPTY_TAG_NAME);
                }
                if (tag.getIdentifier() == null || tag.getIdentifier().trim().isEmpty()) {
                    String message = UIMessages.EMPTY_TAG_IDENTIFIER + " at index " + index;
                    log.error(message);
                    errors.put("tagRequests[" + index + "].identifier", UIMessages.EMPTY_TAG_IDENTIFIER);
                }
                index++;
            }
        }
        return errors;
    }
}
