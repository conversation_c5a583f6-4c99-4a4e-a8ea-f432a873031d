package com.heal.controlcenter.pojo;

import com.heal.controlcenter.util.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * POJO for updating component instance details.
 * Converted from appsone-controlcenter UpdateInstancePojo.
 */
@Slf4j
@Data
public class UpdateInstancePojo {

    private int instanceId;
    private String instanceIdentifier;
    private String name;
    private List<ApplicationServiceMapping> application;
    private String environment;
    private boolean hostInstance;

    @Data
    public static class ApplicationServiceMapping {
        int id;
        String name;
        String identifier;
        List<IdAction> service;
    }

    @Data
    public static class IdAction {
        private int id;
        private String name;
        private String identifier;
        private String action;
    }

    /**
     * Validates the update instance request following appsone-controlcenter pattern
     */
    public boolean isValid() {
        boolean retVal = true;

        if (instanceId <= 0) {
            log.error("Invalid instance id [{}] found.", instanceId);
            retVal = false;
        }

        if (StringUtils.isEmpty(instanceIdentifier)) {
            log.error("Invalid instance identifier [{}] found.", instanceIdentifier);
            retVal = false;
        }

        if (name != null && (name.trim().length() == 0 || name.length() > 128)) {
            log.error("Invalid instance name [{}] found.", name);
            retVal = false;
        }

        if (application != null) {
            for (ApplicationServiceMapping appData : application) {

                if (appData.getId() <= 0) {
                    log.error("Invalid application id [{}] found.", appData.getId());
                    retVal = false;
                }

                if (StringUtils.isEmpty(appData.getName())) {
                    log.error("Invalid application name [{}] found.", appData.getName());
                    retVal = false;
                }

                if (StringUtils.isEmpty(appData.getIdentifier())) {
                    log.error("Invalid application identifier [{}] found.", appData.getIdentifier());
                    retVal = false;
                }

                if (appData.service == null || appData.service.size() == 0) {
                    log.error("No services found in application [{}].", appData.getIdentifier());
                    retVal = false;
                }

                if (appData.service != null) {

                    for (IdAction serviceData : appData.service) {

                        if (serviceData.getId() <= 0) {
                            log.error("Invalid service id [{}] found.", serviceData.getId());
                            retVal = false;
                        }

                        if (StringUtils.isEmpty(serviceData.getName())) {
                            log.error("Invalid service name [{}] found.", serviceData.getName());
                            retVal = false;
                        }

                        if (StringUtils.isEmpty(serviceData.getIdentifier())) {
                            log.error("Invalid service identifier [{}] found.", serviceData.getIdentifier());
                            retVal = false;
                        }

                        if (StringUtils.isEmpty(serviceData.getAction())
                                || !(serviceData.getAction().equalsIgnoreCase("Add")
                                || serviceData.getAction().equalsIgnoreCase("Remove"))) {
                            log.error("Invalid service action [{}] found. Only 'Add' and 'Remove' allowed.", serviceData.getAction());
                            retVal = false;
                        }
                    }
                }
            }
        }

        if (environment != null && environment.trim().length() == 0) {
            log.error("Invalid Environment name [{}] found. Empty Strings not allowed.", environment);
            retVal = false;
        }

        return retVal;
    }
}
