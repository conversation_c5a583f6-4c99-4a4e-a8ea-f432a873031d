package com.heal.controlcenter.pojo;

import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class Attributes {
    private String name;
    private String value;
    private List<ViolationConfig> violationConfig;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Attributes that = (Attributes) o;
        return name.equals(that.name) &&
                value.equals(that.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, value);
    }

    @Override
    public String toString() {
        return "Attributes{" +
                "name='" + name + '\'' +
                ", value='" + value + '\'' +
                '}';
    }
}
