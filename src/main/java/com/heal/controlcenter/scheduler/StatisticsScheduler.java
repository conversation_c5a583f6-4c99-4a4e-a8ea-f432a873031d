package com.heal.controlcenter.scheduler;

import com.heal.controlcenter.util.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.lang.management.ManagementFactory;

@Slf4j
@Component
public class StatisticsScheduler {

    private final HealthMetrics healthMetrics;

    public StatisticsScheduler(HealthMetrics healthMetrics) {
        this.healthMetrics = healthMetrics;
    }

    @Scheduled(initialDelay = 1000, fixedRateString = "${health.metrics.log.interval.milliseconds:60000}")
    public void logHealthMetrics() {
        HealthMetrics.HealthMetricsSnapshot snapshot = healthMetrics.getMetrics();
        log.info("Health Metrics: Total Requests={}, Slow Requests={}, Min Response Time={}ms, Max Response Time={}ms, Avg Response Time={}ms, Status Codes={}, API Metrics={}",
                snapshot.getTotalRequests(),
                snapshot.getSlowRequests(),
                snapshot.getMinResponseTime(),
                snapshot.getMaxResponseTime(),
                snapshot.getAvgResponseTime(),
                healthMetrics.getStatusCodes(),
                snapshot.getApiMetrics());

        log.info("Memory statistics : ");
        printUsage(Runtime.getRuntime());
        log.info("Available Processors : {}", ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors());
        log.info("System Load Average : {}", ManagementFactory.getOperatingSystemMXBean().getSystemLoadAverage());
    }

    public static void printUsage(Runtime runtime) {
        long max, total, free, used;
        int mb = 1024 * 1024;

        max = runtime.maxMemory();
        total = runtime.totalMemory();
        free = runtime.freeMemory();
        used = total - free;
        log.info("Max Memory: {} MB", max / mb);
        log.info("Total Memory: {} MB", total / mb);
        log.info("Memory Used: {} MB", used / mb);
        log.info("Memory Free: {} MB", free / mb);
        log.info("Memory Percent Used: {} %", ((double) used / (double) total) * 100);
        log.info("Memory Percent Free: {} %", ((double) free / (double) total) * 100);

        log.info("Direct memory used: {} MB", getDirectMemoryUsage() / mb);
    }

    public static long getDirectMemoryUsage() {
        try {
            MBeanServer mBeanServer = ManagementFactory.getPlatformMBeanServer();
            ObjectName objectName = new ObjectName("java.nio:type=BufferPool,name=direct");
            return (Long) mBeanServer.getAttribute(objectName, "MemoryUsed");
        } catch (Exception e) {
            log.error("Error in fetching direct memory usage.", e);
            return -1;
        }
    }
}