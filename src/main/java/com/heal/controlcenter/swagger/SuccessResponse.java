package com.heal.controlcenter.swagger;

import com.heal.controlcenter.pojo.ResponsePojo;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface SuccessResponse {
    String code() default "200";
    String description() default "";
    Class<?> schema() default ResponsePojo.class;                 // e.g., Page.class or ResponsePojo.class
    String mediaType() default "application/json";
}

