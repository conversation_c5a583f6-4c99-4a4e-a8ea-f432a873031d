package com.heal.controlcenter.swagger;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ErrorResponse {
    boolean badRequest() default true;
    boolean unauthorized() default true;
    boolean serverError() default true;
}
