package com.heal.controlcenter.beans;

import com.heal.controlcenter.dao.mysql.entity.CompInstanceKPIDetailsBean;
import com.heal.controlcenter.pojo.Tags;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComponentInstanceBean {
    private int id;
    private String name;
    private int status;
    private int hostId;
    private int isDR;
    private int isCluster;
    private int mstComponentVersionId;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int accountId;
    private int mstComponentId;
    private int mstComponentTypeId;
    private String identifier;
    private int discovery;
    private String hostAddress;
    private int mstCommonVersionId;
    private String mstCommonVersionName;
    private int isHost;
    private String clusterName;
    private String mstComponentVersion;
    private List<String> agentIdentifiers;
    private String mstComponentName;
    private String mstComponentType;
    private List<CompInstanceKPIDetailsBean> nonGroupKpi;
    private List<CompInstanceKpiGroupDetailsBean> groupKpi;
    private List<CompInstanceAttributesBean> attributes;
    private Map<String, String> errorMessage = new HashMap<>();
    private List<Tags> tags;
    private Set<String> mappingIdentifiers = new HashSet<>();
    private String parentIdentifier;
    private String parentName;
    private int parentId;
    private List<String> serviceIdentifiers;
    private int[] serviceIds;
    private int isUpdate;
    private Map<Integer, String> agentIdsMap;
    private int appId;
    private int isKubernetes;
    private int isPod;
    private int supervisorId;
    private int forensicsAgentTypeId;
}
