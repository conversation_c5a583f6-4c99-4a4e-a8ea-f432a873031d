package com.heal.controlcenter.beans;

import com.heal.controlcenter.enums.ActionsEnum;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentCompInstMappingBean {
    @EqualsAndHashCode.Exclude
    private int id;
    private int compInstanceId;
    private int agentId;
    private int agentTypeId;
    @EqualsAndHashCode.Exclude
    private ActionsEnum actionForUpdate;
    @EqualsAndHashCode.Exclude
    private String createdTime;
    @EqualsAndHashCode.Exclude
    private int accountId;
    @EqualsAndHashCode.Exclude
    private String compInstanceName;
    @EqualsAndHashCode.Exclude
    private String compInstanceIdentifier;
    @EqualsAndHashCode.Exclude
    private int status;
    @EqualsAndHashCode.Exclude
    private String userDetailsId;
    @EqualsAndHashCode.Exclude
    private String accountIdentifier;
}
