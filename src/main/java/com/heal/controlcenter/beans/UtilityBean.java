package com.heal.controlcenter.beans;

import com.heal.controlcenter.pojo.BasicUserDetails;
import lombok.Builder;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Pageable;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UtilityBean<T> {

    T pojoObject;
    Map<String, String> requestParams;
    Map<String, Object> metadata;
    Pageable pageable;
    BasicUserDetails basicUserDetails;
}
