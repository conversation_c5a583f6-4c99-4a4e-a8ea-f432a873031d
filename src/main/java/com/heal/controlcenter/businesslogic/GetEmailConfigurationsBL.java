package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.SMTPDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Slf4j
@Service
public class GetEmailConfigurationsBL implements BusinessLogic<Object, Integer, SMTPDetailsPojo> {

    private final AccountsDao accountDao;
    private final NotificationsDao notificationsDao;
    private final MasterDataDao masterDataDao;
    private final AECSBouncyCastleUtil aecsBouncyCastleUtil;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetEmailConfigurationsBL(AccountsDao accountDao, NotificationsDao notificationsDao,
                                    MasterDataDao masterDataDao, AECSBouncyCastleUtil aecsBouncyCastleUtil,
                                    ClientValidationUtils clientValidationUtils,
                                    ServerValidationUtils serverValidationUtils) {
        this.accountDao = accountDao;
        this.notificationsDao = notificationsDao;
        this.masterDataDao = masterDataDao;
        this.aecsBouncyCastleUtil = aecsBouncyCastleUtil;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<Object> clientValidation(Object... arguments) throws ClientException {
        try {
            String accountIdentifier = (String) arguments[0];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);

            return UtilityBean.builder()
                    .requestParams(requestParamsMap)
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    @Override
    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);

        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        return account.getId();
    }

    @Override
    public SMTPDetailsPojo process(Integer accountId) throws DataProcessingException {
        String security = "";
        SMTPDetailsBean smtpDetailsBean = notificationsDao.getSMTPDetails(accountId);

        if (smtpDetailsBean == null) {
            log.error("SMTP details not found for accountId [{}].", accountId);
            return null;
        }

        String encryptedString = smtpDetailsBean.getPassword();
        try {
            smtpDetailsBean.setPassword(decryptBCECAndEncryptAECS(encryptedString));
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        if (smtpDetailsBean.getSecurityId() > 0) {
            security = masterDataDao.getMstSubTypeBySubTypeId(smtpDetailsBean.getSecurityId()).getSubTypeName();
        }

        return SMTPDetailsPojo.builder()
                .id(smtpDetailsBean.getId())
                .address(smtpDetailsBean.getAddress())
                .port(smtpDetailsBean.getPort())
                .username(smtpDetailsBean.getUsername())
                .password(smtpDetailsBean.getPassword())
                .fromRecipient(smtpDetailsBean.getFromRecipient())
                .security(security)
                .build();
    }

    private String decryptBCECAndEncryptAECS(String input) throws HealControlCenterException {
        String plainTxt;
        try {
            plainTxt = CommonUtils.decryptInBCEC(input);
        } catch (Exception e) {
            log.error("Exception encountered while decrypting the password. Details: {}", e.getMessage());
            throw new HealControlCenterException("Error occurred while decrypting the password from the database.");
        }
        try {
            return aecsBouncyCastleUtil.encrypt(plainTxt);
        } catch (Exception e) {
            log.error("Exception encountered while encrypting the password. Details: {}", e.getMessage());
            throw new HealControlCenterException("Error occurred while encrypting the password from the database.");
        }
    }
}