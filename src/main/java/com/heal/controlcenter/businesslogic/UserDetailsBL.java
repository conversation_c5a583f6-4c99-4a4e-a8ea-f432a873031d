package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.UserDetailsBean;
import com.heal.controlcenter.beans.UserNotificationDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.NotificationPreferencesDataDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserDetailsBL implements BusinessLogic<String, String, List<UserDetailsBean>> {

    private final UserDao userDao;
    private final NotificationPreferencesDataDao notificationPreferencesDataDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public UserDetailsBL(UserDao userDao, NotificationPreferencesDataDao notificationPreferencesDataDao, ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.userDao = userDao;
        this.notificationPreferencesDataDao = notificationPreferencesDataDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(Object... arguments) throws ClientException {
        try {
            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            return UtilityBean.<String>builder()
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    @Override
    public String serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        return null;
    }

    @Override
    public List<UserDetailsBean> process(String bean) throws DataProcessingException {
        try {
            log.info("fetching users detail");
            List<UserDetailsBean> userDetailsBeanList = userDao.getNonSuperUsers();

            if (userDetailsBeanList == null || userDetailsBeanList.isEmpty()) {
                log.info("User list fetched from schema is empty.");
                return Collections.emptyList();
            }

            List<String> userIdentifiers = userDetailsBeanList.stream()
                    .map(UserDetailsBean::getUpdatedBy)
                    .distinct()
                    .collect(Collectors.toList());

            Map<String, String> usernameMap = userDao.getUsernamesFromIdentifiers(userIdentifiers);
            Map<String, UserNotificationDetailsBean> userNotificationDetailsBeanMap = notificationPreferencesDataDao.getEmailSmsForensicNotificationStatusForUsers();

            return userDetailsBeanList.stream().sorted(Comparator.comparing(UserDetailsBean::getUpdatedOn))
                    .peek(user -> {
                        UserNotificationDetailsBean detailsBean = userNotificationDetailsBeanMap.get(user.getUserId());
                        user.setUpdatedBy(usernameMap.get(user.getUpdatedBy()));
                        user.setEmailNotification(detailsBean == null ? 0 : detailsBean.getEmailEnabled());
                        user.setSmsNotification(detailsBean == null ? 0 : detailsBean.getSmsEnabled());
                        user.setForensicNotification(detailsBean == null ? 0 : detailsBean.getForensicEnabled());
                    }).collect(Collectors.toList());
        } catch (HealControlCenterException e) {
            log.error("Error occurred while forming response object", e);
            throw new DataProcessingException(e.getMessage());
        }
    }
}