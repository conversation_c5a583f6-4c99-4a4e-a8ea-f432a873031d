package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import com.heal.controlcenter.beans.ComponentInstanceBean;
import com.heal.controlcenter.beans.InstancesKpisBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.CompInstanceDao;
import com.heal.controlcenter.dao.mysql.KPIDao;
import com.heal.controlcenter.dao.mysql.ThresholdDao;
import com.heal.controlcenter.dao.mysql.entity.InstanceKpiAttributeThresholdBean;
import com.heal.controlcenter.dao.mysql.entity.KpiBean;
import com.heal.controlcenter.dao.opensearch.CollatedKpiRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.groupingBy;

@Slf4j
@Service
public class GetInstanceLevelKpiThresholdBL implements BusinessLogic<InstancesKpisBean, UtilityBean<InstancesKpisBean>, KpiAttrThresholdInfo> {

    private final KPIDao kpiDao;
    private final ThresholdDao thresholdDao;
    private final CacheWrapper cacheWrapper;
    private final CompInstanceDao compInstanceDao;
    private final CollatedKpiRepo collatedKpiRepo;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    @Value("${comp.instance.range:1440}")
    private int compInstRange;

    public GetInstanceLevelKpiThresholdBL(KPIDao kpiDao, ThresholdDao thresholdDao,
                                          CacheWrapper cacheWrapper, CompInstanceDao compInstanceDao,
                                          CollatedKpiRepo collatedKpiRepo, ClientValidationUtils clientValidationUtils,
                                          ServerValidationUtils serverValidationUtils) {
        this.kpiDao = kpiDao;
        this.thresholdDao = thresholdDao;
        this.cacheWrapper = cacheWrapper;
        this.compInstanceDao = compInstanceDao;
        this.collatedKpiRepo = collatedKpiRepo;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<InstancesKpisBean> clientValidation(Object... arguments) throws ClientException {
        try {
            InstancesKpisBean requestBody = (InstancesKpisBean) arguments[0];
            clientValidationUtils.nullRequestBodyCheck(requestBody, UIMessages.REQUEST_BODY_NULL);

            String authKey = (String) arguments[1];
            clientValidationUtils.nullOrEmptyCheck(authKey, UIMessages.AUTH_KEY_INVALID);

            String accountIdentifier = (String) arguments[2];
            clientValidationUtils.nullOrEmptyCheck(accountIdentifier, UIMessages.ACCOUNT_IDENTIFIER_INVALID);

            if (arguments[3] == null || ((String) arguments[3]).trim().isEmpty()) {
                String message = String.format(UIMessages.INVALID_INSTANCE_ID, arguments[3]);
                log.error(message);
                throw new ClientException(message);
            }

            String instanceIds = ((String) arguments[3]).trim();
            List<Integer> instanceIdList;
            try {
                instanceIdList = Arrays.stream(instanceIds.split(",")).map(s -> Integer.parseInt(s.trim()))
                        .toList();
            } catch (NumberFormatException e) {
                String message = "Invalid instance ID format. All IDs must be integers.";
                log.error(message, e);
                throw new ClientException(message);
            }

            boolean isLteZero = instanceIdList.stream().anyMatch(id -> id <= 0);
            if (instanceIdList.isEmpty() || isLteZero) {
                log.error("Invalid instance ID. All the instance IDs should be a non-zero integer");
                throw new ClientException("Invalid instance ID. All the instance IDs should be a non-zero integer");
            }

            int kpiId = 0;
            if (arguments[4] != null && !((String) arguments[4]).trim().isEmpty()) {
                kpiId = Integer.parseInt((String) arguments[4]);
            }

            if (kpiId <= 0) {
                String message = String.format(UIMessages.INVALID_KPI_ID, kpiId);
                log.error(message);
                throw new ClientException(message);
            }

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            InstancesKpisBean bean = InstancesKpisBean.builder().instanceIds(instanceIdList).kpiId(kpiId).build();

            HashMap<String, String> requestParamMap = new HashMap<>();
            requestParamMap.put(Constants.AUTH_KEY, authKey);
            requestParamMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);

            return UtilityBean.<InstancesKpisBean>builder()
                    .pojoObject(bean)
                    .basicUserDetails(basicUserDetails)
                    .requestParams(requestParamMap)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    @Override
    public UtilityBean<InstancesKpisBean> serverValidation(UtilityBean<InstancesKpisBean> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Map<String, Object> metadata = new HashMap<>();

        serverValidationUtils.authKeyValidation(authKey);

        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();
        metadata.put(Constants.ACCOUNT, account);

        String thresholdSeverityViewTypeErrorMsg = "Invalid thresholds view types. Subtype: {} unavailable";
        Map<String, List<ViewTypes>> viewTypes = cacheWrapper.getAllViewTypesIdMap();
        validateViewType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW, metadata, thresholdSeverityViewTypeErrorMsg);
        validateViewType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM, metadata, thresholdSeverityViewTypeErrorMsg);
        validateViewType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH, metadata, thresholdSeverityViewTypeErrorMsg);

        String operationViewTypeError = "Invalid operation view types. Subtype: {} unavailable";
        validateViewType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_LESSER_THAN, metadata, operationViewTypeError);
        validateViewType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_GREATER_THAN, metadata, operationViewTypeError);
        validateViewType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_NOT_BETWEEN, metadata, operationViewTypeError);

        InstancesKpisBean instancesKpisBean = utilityBean.getPojoObject();
        List<Integer> instanceIds = instancesKpisBean.getInstanceIds();
        int kpiId = instancesKpisBean.getKpiId();

        KpiBean kpiBean = kpiDao.fetchKpiUsingKpiId(kpiId, account.getId());
        if (kpiBean == null) {
            log.error("KPI with ID [{}] is unavailable", kpiId);
            throw new ServerException(String.format("KPI with ID [%d] is unavailable", kpiId));
        }

        instancesKpisBean.setGroupKpiId(kpiBean.getGroupKpiId());
        instancesKpisBean.setDataType(kpiBean.getDataType());
        instancesKpisBean.setUnit(kpiBean.getMeasureUnits());

        int discoveryFlag = 0;
        if (kpiBean.getGroupKpiId() > 0) {
            discoveryFlag = kpiDao.getGroupKpiDiscovery(kpiBean.getGroupKpiId());
        }
        instancesKpisBean.setDiscovery(discoveryFlag);

        ViewTypes lowThresholdSeverityView = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        ViewTypes mediumThresholdSeverityView = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        ViewTypes highThresholdSeverityView = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
        Map<Integer, ViewTypes> thresholdSeverityMap = Stream.of(lowThresholdSeverityView, mediumThresholdSeverityView, highThresholdSeverityView)
                .collect(Collectors.toMap(ViewTypes::getSubTypeId, identity()));

        ViewTypes lessThanOperationType = (ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_LESSER_THAN);
        ViewTypes greaterThanOperationType = (ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_GREATER_THAN);
        ViewTypes notBetweenOperationType = (ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_NOT_BETWEEN);
        Map<Integer, ViewTypes> operationTypeMap = Stream.of(lessThanOperationType, greaterThanOperationType, notBetweenOperationType)
                .collect(Collectors.toMap(ViewTypes::getSubTypeId, identity()));

        Map<Integer, String> componentInstanceIdToIdentifierMap = new HashMap<>();

        // fetch bulk component instances and their thresholds
        List<ComponentInstanceBean> compInstances = compInstanceDao.getComponentInstancesByIdsAndAccountId(instanceIds, accountId);
        Map<Integer, ComponentInstanceBean> compInstanceIdVsCompInst = compInstances.stream().collect(Collectors.toMap(ComponentInstanceBean::getId, identity()));

        List<InstanceKpiAttributeThresholdBean> thresholdBeans = thresholdDao.getCompInstanceThresholdDetail(accountId, kpiId, instanceIds);
        metadata.put(Constants.ALL_THRESHOLDS, thresholdBeans);

        Map<Integer, List<InstanceKpiAttributeThresholdBean>> instanceIdVsThresholds = thresholdBeans.stream()
                .collect(groupingBy(InstanceKpiAttributeThresholdBean::getCompInstanceId));

        for (int instanceId : instanceIds) {
            ComponentInstanceBean compInst = compInstanceIdVsCompInst.get(instanceId);
            if (compInst == null) {
                log.error("Component instance with ID [{}] with account ID [{}] is unavailable", instanceId, accountId);
                throw new ServerException("Invalid instanceId provided");
            }

            List<InstanceKpiAttributeThresholdBean> currThresholdBeans = instanceIdVsThresholds.get(instanceId);
            List<InstanceKpiAttributeThresholdBean> validOperationIdList = currThresholdBeans.parallelStream().filter(t -> {
                ViewTypes operationTypeView = operationTypeMap.get(t.getOperationId());
                ViewTypes thresholdSeverity = thresholdSeverityMap.get(t.getThresholdSeverityId());
                return operationTypeView != null && thresholdSeverity != null;
            }).toList();

            if (validOperationIdList.size() != currThresholdBeans.size()) {
                log.error("KPI(s) mapped to instanceId [{}] have invalid operationId", instanceId);
                throw new ServerException("KPI(s) have invalid operationId");
            }

            componentInstanceIdToIdentifierMap.put(instanceId, compInst.getIdentifier());
        }

        metadata.put(Constants.COMP_INSTANCE_ID_VS_IDENTIFIER, componentInstanceIdToIdentifierMap);

        return UtilityBean.<InstancesKpisBean>builder().pojoObject(instancesKpisBean)
                .requestParams(utilityBean.getRequestParams())
                .metadata(metadata).build();
    }

    @Override
    public KpiAttrThresholdInfo process(UtilityBean<InstancesKpisBean> bean) throws DataProcessingException {
        Set<String> attributes = new HashSet<>();
        List<InstanceKpiAttributeThresholdBean> thresholdBeanList = new ArrayList<>();
        Set<KpiIdVsAttributeSeverityId> instanceThresholdNotPresent = new HashSet<>();

        long range = compInstRange * 60 * 1000L;

        InstancesKpisBean instancesKpisBean = bean.getPojoObject();
        Map<String, Object> metadata = bean.getMetadata();

        Account account = (Account) metadata.get(Constants.ACCOUNT);

        @SuppressWarnings("unchecked")
        Map<Integer, String> compInstanceIdVsIdentifier = (Map<Integer, String>) metadata.get(Constants.COMP_INSTANCE_ID_VS_IDENTIFIER);

        ViewTypes lowThresholdSeverityView = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        ViewTypes mediumThresholdSeverityView = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        ViewTypes highThresholdSeverityView = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
        Map<Integer, ViewTypes> thresholdSeverityMap = Stream.of(lowThresholdSeverityView, mediumThresholdSeverityView, highThresholdSeverityView)
                .collect(Collectors.toMap(ViewTypes::getSubTypeId, identity()));

        ViewTypes lessThanOperationType = (ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_LESSER_THAN);
        ViewTypes greaterThanOperationType = (ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_GREATER_THAN);
        ViewTypes notBetweenOperationType = (ViewTypes) metadata.get(Constants.OPERATIONS_TYPE_NOT_BETWEEN);
        Map<Integer, ViewTypes> operationTypeMap = Stream.of(lessThanOperationType, greaterThanOperationType, notBetweenOperationType)
                .collect(Collectors.toMap(ViewTypes::getSubTypeId, identity()));

        List<Integer> instanceIds = instancesKpisBean.getInstanceIds();
        int kpiId = instancesKpisBean.getKpiId();
        int groupKpiId = instancesKpisBean.getGroupKpiId();
        String accountIdentifier = account.getIdentifier();

        Map<Pair<Integer, Integer>, List<CompInstanceKpiGroupDetailsBean>> instanceKpisMap;
        Map<String, Set<String>> instanceVsGroupKpiAttributeMap = new HashMap<>();
        List<CompInstanceKpiGroupDetailsBean> mappedKpis;

        if (groupKpiId > 0) {
            mappedKpis = compInstanceDao.getGroupKpiListForCompInstance(groupKpiId);

            // Check if discovery is enabled for the KPI
            if (instancesKpisBean.getDiscovery() == 1) {
                Set<String> instanceIdentifierSet = new HashSet<>(compInstanceIdVsIdentifier.values());
                instanceVsGroupKpiAttributeMap = collatedKpiRepo.getGroupKpiAttributesWithDataCollected(accountIdentifier, instanceIdentifierSet, range, kpiId);
                Set<String> kpiAttributes = instanceVsGroupKpiAttributeMap.values().stream()
                        .flatMap(Set::stream).collect(Collectors.toSet());
                if (!kpiAttributes.isEmpty()) {
                    attributes.addAll(kpiAttributes);
                }
            }
        } else {
            mappedKpis = compInstanceDao.getNonGroupKpiListForCompInstance(kpiId);
        }

        instanceKpisMap = mappedKpis.stream()
                .collect(Collectors.groupingBy(k -> Pair.of(k.getCompInstanceId(), k.getMstKpiDetailsId())));

        @SuppressWarnings("unchecked")
        List<InstanceKpiAttributeThresholdBean> thresholdBeans = (List<InstanceKpiAttributeThresholdBean>) metadata.get(Constants.ALL_THRESHOLDS);
        Map<Integer, List<InstanceKpiAttributeThresholdBean>> instanceIdVsThresholds = thresholdBeans.stream()
                .collect(groupingBy(InstanceKpiAttributeThresholdBean::getCompInstanceId));

        List<CompInstanceKpiGroupDetailsBean> kpiDetailsBeans;

        for (int instanceId : instanceIds) {
            kpiDetailsBeans = instanceKpisMap.getOrDefault(Pair.of(instanceId, kpiId), new ArrayList<>());

            if (instancesKpisBean.getDiscovery() == 1) {
                Set<String> kpiAttributes = instanceVsGroupKpiAttributeMap.get(compInstanceIdVsIdentifier.get(instanceId));
                if (kpiAttributes == null || kpiAttributes.isEmpty()) {
                    log.error("No kpi group attributes found for the instance identifier:{}", compInstanceIdVsIdentifier.get(instanceId));
                } else {
                    attributes.addAll(kpiAttributes);
                }
            }

            List<InstanceKpiAttributeThresholdBean> list = instanceIdVsThresholds.getOrDefault(instanceId, new ArrayList<>());
            thresholdBeanList.addAll(list);

            kpiDetailsBeans.forEach(c -> {
                // Call the new helper method for each severity level
                addMissingThresholdIfRequired(list, instanceThresholdNotPresent, c.getAttributeValue(), kpiId, lowThresholdSeverityView);
                addMissingThresholdIfRequired(list, instanceThresholdNotPresent, c.getAttributeValue(), kpiId, mediumThresholdSeverityView);
                addMissingThresholdIfRequired(list, instanceThresholdNotPresent, c.getAttributeValue(), kpiId, highThresholdSeverityView);
            });
        }

        Map<KpiIdVsAttributeSeverityId, List<InstanceKpiAttributeThresholdBean>> groupingBasedOnAttribute = thresholdBeanList.stream()
                .collect(groupingBy(i -> new KpiIdVsAttributeSeverityId(i.getKpiId(), i.getAttributeValue(), i.getThresholdSeverityId())));

        Map<String, KpiAttributeThresholdInfo> resultMap = new HashMap<>();
        groupingBasedOnAttribute.forEach((key, beans) -> {
            if (beans.isEmpty()) {
                return; // continue to next entry
            }

            InstanceKpiAttributeThresholdBean firstBean = beans.get(0);
            String attributeValue = firstBean.getAttributeValue();
            int groupKpiIdFromBean = firstBean.getKpiGroupId();
            String mapKey = key.getKpiId() + "_" + groupKpiIdFromBean + "_" + attributeValue;

            attributes.add(attributeValue);

            KpiAttributeThresholdInfo info = resultMap.computeIfAbsent(mapKey, k ->
                    KpiAttributeThresholdInfo.builder()
                            .kpiId(key.getKpiId())
                            .groupKpiId(groupKpiIdFromBean)
                            .attributeValue(attributeValue)
                            .dataType(instancesKpisBean.getDataType())
                            .unit(instancesKpisBean.getUnit())
                            .build());

            ThresholdDetails thresholdDetails = createThresholdDetails(beans, instanceThresholdNotPresent, operationTypeMap);

            ViewTypes thresholdSeverity = thresholdSeverityMap.getOrDefault(key.getThresholdSeverityId(), ViewTypes.builder().build());
            String severityType = thresholdSeverity.getSubTypeName();

            if (Constants.THRESHOLD_SEVERITY_TYPE_LOW.equalsIgnoreCase(severityType)) {
                info.setLowThreshold(thresholdDetails);
            } else if (Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM.equalsIgnoreCase(severityType)) {
                info.setMediumThreshold(thresholdDetails);
            } else if (Constants.THRESHOLD_SEVERITY_TYPE_HIGH.equalsIgnoreCase(severityType)) {
                info.setHighThreshold(thresholdDetails);
            }
        });

        // The final set of thresholds is simply the values of the map.
        Set<KpiAttributeThresholdInfo> output = new HashSet<>(resultMap.values());
        return KpiAttrThresholdInfo.builder()
                .attributes(attributes.stream().filter(Objects::nonNull).collect(Collectors.toSet()))
                .thresholds(output)
                .build();
    }

    private ThresholdDetails createThresholdDetails(List<InstanceKpiAttributeThresholdBean> beans,
                                                    Set<KpiIdVsAttributeSeverityId> instanceThresholdNotPresent,
                                                    Map<Integer, ViewTypes> operationTypeMap) {
        InstanceKpiAttributeThresholdBean firstBean = beans.get(0);

        // set threshold status
        ThresholdDetails.Status status;
        boolean sameStatus = beans.stream().map(InstanceKpiAttributeThresholdBean::getStatus)
                .distinct().count() == 1;
        if (sameStatus) {
            status = ThresholdDetails.Status.builder()
                    .common(1).value(firstBean.getStatus()).build();
        } else {
            status = ThresholdDetails.Status.builder()
                    .common(0).value(0).build();
        }

        // set threshold operation
        ThresholdDetails.Operation thresholdOperation;
        boolean sameThresholdOperation = beans.stream()
                .allMatch(x -> x.getOperationId() == firstBean.getOperationId()
                        && x.getMaxThreshold().equals(firstBean.getMaxThreshold())
                        && x.getMinThreshold().equals(firstBean.getMinThreshold()));

        KpiIdVsAttributeSeverityId kpiIdVsAttributeSeverityId = KpiIdVsAttributeSeverityId.builder()
                .kpiId(firstBean.getKpiId())
                .attributeName(firstBean.getAttributeValue())
                .thresholdSeverityId(firstBean.getThresholdSeverityId())
                .build();
        boolean commonThreshold = !instanceThresholdNotPresent.contains(kpiIdVsAttributeSeverityId);

        if (sameThresholdOperation && commonThreshold) {
            ViewTypes operationTypeView = operationTypeMap.get(firstBean.getOperationId());
            thresholdOperation = ThresholdDetails.Operation.builder()
                    .common(1).value(operationTypeView.getSubTypeName())
                    .maxThreshold(firstBean.getMaxThreshold()).minThreshold(firstBean.getMinThreshold()).build();
        } else {
            thresholdOperation = ThresholdDetails.Operation.builder()
                    .common(0).value(null).maxThreshold(null).minThreshold(null).build();
        }

        return new ThresholdDetails(thresholdOperation, status);
    }

    private void addMissingThresholdIfRequired(List<InstanceKpiAttributeThresholdBean> existingThresholds, Set<KpiIdVsAttributeSeverityId> missingSet,
                                               String attributeValue, int kpiId, ViewTypes severityView) {
        boolean isMissing = existingThresholds.stream()
                .noneMatch(t -> attributeValue.equals(t.getAttributeValue()) &&
                        t.getThresholdSeverityId() == severityView.getSubTypeId());
        if (isMissing) {
            missingSet.add(new KpiIdVsAttributeSeverityId(kpiId, attributeValue, severityView.getSubTypeId()));
        }
    }

    private void validateViewType(Map<String, List<ViewTypes>> viewTypes, String type, String subType, Map<String, Object> metadata, String errorMsg) throws ServerException {
        ViewTypes viewType = CommonUtils.getViewTypeByNameAndSubType(viewTypes, type, subType);
        if (viewType == null) {
            log.error(errorMsg, subType);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(subType, viewType);
    }
}