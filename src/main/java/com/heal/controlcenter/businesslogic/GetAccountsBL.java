package com.heal.controlcenter.businesslogic;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.AnomalyConfiguration;
import com.heal.configuration.pojos.TenantDetails;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.dao.mysql.TenantsDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.PaginationUtils;
import com.heal.controlcenter.util.UIMessages;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetAccountsBL implements BusinessLogic<String, UtilityBean<AccessDetailsBean>, Page<Account>> {

    private final TagsDao tagsDao;
    private final UserDao userDao;
    private final AccountsDao accountDao;
    private final ObjectMapper objectMapper;
    private final TenantsDao tenantsDao;

    public GetAccountsBL(TagsDao tagsDao, UserDao userDao, AccountsDao accountDao, ObjectMapper objectMapper,
                         TenantsDao tenantsDao) {
        this.tagsDao = tagsDao;
        this.userDao = userDao;
        this.accountDao = accountDao;
        this.objectMapper = objectMapper;
        this.tenantsDao = tenantsDao;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(Object... arguments) throws ClientException {
        try {
            String strParam = (String) arguments[0];
            String searchTerm = (strParam != null) ? strParam.trim() : "";
            if (searchTerm.isEmpty()) {
                log.warn(LogMessages.SEARCH_TERM_EMPTY_OR_NULL);
            }

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.SEARCH_TERM_KEY, searchTerm);

            return UtilityBean.<String>builder()
                    .requestParams(requestParamsMap)
                    .metadata(new HashMap<>())
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<AccessDetailsBean> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String userId = utilityBean.getBasicUserDetails().getUserIdentifier();
        try {
            UserAccessBean accessDetails = userDao.fetchUserAccessDetailsUsingIdentifier(userId);
            AccessDetailsBean accessDetailsBean = objectMapper.readValue(accessDetails.getAccessDetails(), AccessDetailsBean.class);
            if (accessDetailsBean == null) throw new ServerException(UIMessages.INVALID_USER_ACCESS_DETAILS);

            return UtilityBean.<AccessDetailsBean>builder()
                    .pojoObject(accessDetailsBean)
                    .metadata(Map.of(Constants.USER_ID_KEY, userId))
                    .pageable(utilityBean.getPageable())
                    .requestParams(utilityBean.getRequestParams())
                    .basicUserDetails(utilityBean.getBasicUserDetails())
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.FAILED_TO_PROCESS_USER_ACCESS, userId, e);
            throw new ServerException(UIMessages.USER_ACCESS_DETAILS_NOT_FOUND);
        }
    }

    @Override
    @LogExecutionAnnotation
    public Page<Account> process(UtilityBean<AccessDetailsBean> utilityBean) throws DataProcessingException {
        AccessDetailsBean accessDetails = utilityBean.getPojoObject();
        Pageable pageable = utilityBean.getPageable();
        String userId = utilityBean.getBasicUserDetails().getUserIdentifier();
        String searchTerm = utilityBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);

        PaginationUtils.validatePagination(pageable);

        if (searchTerm == null) {
            log.warn(LogMessages.SEARCH_TERM_NULL_WARNING);
            searchTerm = "";
        }
        log.info(LogMessages.PROCESSING_ACCOUNTS_WITH_PAGEABLE_AND_SEARCH_TERM, pageable, searchTerm);
        List<AccountBean> accessibleAccounts;
        int totalCount;
        try {
            log.info(LogMessages.PROCESSING_ACCOUNTS_FOR_USER_ID, userId, searchTerm, pageable);

            accessibleAccounts = accountDao.getAccounts(searchTerm, pageable);
            totalCount = accountDao.countAccounts(searchTerm);

            // Apply account-level access filtering
            if (!accessDetails.getAccounts().contains("*")) {
                accessibleAccounts = accessibleAccounts.stream()
                        .filter(a -> accessDetails.getAccounts().contains(a.getIdentifier()))
                        .collect(Collectors.toList());
                totalCount = accessibleAccounts.size();
            }
            log.info(LogMessages.FOUND_ACCESSIBLE_ACCOUNTS, accessibleAccounts.size(), userId, searchTerm);

            if (accessibleAccounts.isEmpty()) {
                return PaginationUtils.createPage(Collections.emptyList(), pageable, totalCount);
            }

            List<Integer> accountIds = accessibleAccounts.stream().map(AccountBean::getId).collect(Collectors.toList());
            List<String> userIdentifiers = accessibleAccounts.stream().map(AccountBean::getLastModifiedBy)
                    .distinct().collect(Collectors.toList());

            Map<Integer, TimezoneBean> timezoneMap = accountDao.getAccountTimezoneDetailsForAccounts(accountIds);
            Map<Integer, AnomalyConfiguration> anomalyConfigMap = accountDao.getAccountAnomalyConfigurationForAccounts(accountIds);
            Map<Integer, List<com.heal.configuration.pojos.Tags>> tagsMap = tagsDao.getTagsForObjectIds(accountIds, "account");
            Map<Integer, Integer> tenantIdMap = tenantsDao.getTenantIdsForAccounts(accountIds);
            Map<String, String> usernameMap = userDao.getUsernamesFromIdentifiers(userIdentifiers);

            List<Account> finalAccounts = new ArrayList<>();
            for (AccountBean accountBean : accessibleAccounts) {
                try {
                    TimezoneBean timezone = timezoneMap.get(accountBean.getId());
                    if (timezone == null) {
                        log.error(LogMessages.NO_TIMEZONE_FOUND_FOR_ACCOUNT, accountBean.getId());
                        continue;
                    }

                    AnomalyConfiguration config = anomalyConfigMap.get(accountBean.getId());
                    List<Tags> tags = tagsMap.getOrDefault(accountBean.getId(),Collections.emptyList()).stream()
                            .map(t -> new Tags(t.getType(), t.getKey())).collect(Collectors.toList());

                    ThresholdSeverity severity = new ThresholdSeverity(
                            config != null && config.isLowEnable(),
                            config != null && config.isMediumEnable(),
                            config != null && config.isHighEnable());

                    Integer tenantId = tenantIdMap.get(accountBean.getId());
                    TenantDetails tenantDetails = null;
                    if (tenantId != null) {
                        tenantDetails = tenantsDao.getTenantDetailsById(tenantId);
                    } else {
                        log.warn(LogMessages.NO_TENANT_MAPPING_FOUND, accountBean.getId());
                    }

                    Account account = Account.builder()
                            .accountId(accountBean.getId())
                            .accountName(accountBean.getName())
                            .identifier(accountBean.getIdentifier())
                            .privateKey(accountBean.getPrivateKey())
                            .publicKey(accountBean.getPublicKey())
                            .updatedTime(accountBean.getUpdatedTime() != null ? Timestamp.valueOf(accountBean.getUpdatedTime()).getTime() : null)
                            .lastModifiedBy(usernameMap.get(accountBean.getLastModifiedBy()))
                            .timezoneMilli(timezone.getOffset())
                            .timeZoneString(timezone.getTimeZoneId())
                            .status(accountBean.getStatus())
                            .dateFormat("YYYY-MM-DD")
                            .timeFormat("HH:mm")
                            .tags(tags)
                            .closingWindow(config != null ? config.getClosingWindow() : 0)
                            .maxDataBreaks(config != null ? config.getMaxDataBreaks() : 0)
                            .thresholdSeverity(severity)
                            .tanantDetails(tenantDetails)
                            .build();

                    finalAccounts.add(account);
                } catch (Exception e) {
                    log.error(LogMessages.SKIPPING_ACCOUNT_DUE_TO_PROCESSING_ERROR, accountBean.getId(), e);
                }
            }

            if (pageable.getSort().isSorted() && !finalAccounts.isEmpty()) {
                log.debug(LogMessages.APPLYING_IN_MEMORY_SORTING, pageable.getSort());
                Comparator<Account> comparator = getAccountComparator(pageable);
                if (comparator != null) {
                    finalAccounts.sort(comparator);
                    log.debug(LogMessages.SORTING_COMPLETE, finalAccounts.get(0).getIdentifier());
                }
            }

            return PaginationUtils.createPage(finalAccounts, pageable, totalCount);

        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_PROCESSING_ACCOUNTS, e);
            throw new DataProcessingException("Error processing accounts: " + e.getMessage());
        }
    }

    @Nullable
    private static Comparator<Account> getAccountComparator(Pageable pageable) {
        Comparator<Account> comparator = null;
        for (org.springframework.data.domain.Sort.Order order : pageable.getSort()) {
            Comparator<Account> currentComparator = null;
            if (order.getProperty().equalsIgnoreCase("accountName")) {
                currentComparator = Comparator.comparing(Account::getAccountName, Comparator.naturalOrder());
            } else if (order.getProperty().equalsIgnoreCase("lastModifiedBy")) {
                currentComparator = Comparator.comparing(
                        Account::getLastModifiedBy,
                        Comparator.nullsLast(Comparator.naturalOrder())
                );
            } else if (order.getProperty().equalsIgnoreCase("updatedTime")) {
                currentComparator = Comparator.comparing(
                        Account::getUpdatedTime,
                        Comparator.nullsLast(Comparator.naturalOrder())
                );
            }
            if (currentComparator != null) {
                if (!order.isAscending()) {
                    currentComparator = currentComparator.reversed();
                }
                comparator = comparator == null ? currentComparator : comparator.thenComparing(currentComparator);
            }
        }
        return comparator;
    }
}