package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.AutoDiscoveryDao;
import com.heal.controlcenter.dao.mysql.ConnectionDetailsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.GetConnectionPojo;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.DateTimeUtil;
import com.heal.controlcenter.util.PaginationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class GetConnectionsBL implements BusinessLogic<String, UtilityBean<List<ControllerBean>>, Page<GetConnectionPojo>> {

    private final AccountsDao accountDao;
    private final ControllerDao controllerDao;
    private final ConnectionDetailsDao connectionDetailsDao;
    private final AutoDiscoveryDao autoDiscoveryDao;
    private final ClientValidationUtils clientValidationUtils;

    public GetConnectionsBL(AccountsDao accountDao, ControllerDao controllerDao, ConnectionDetailsDao connectionDetailsDao,
                            AutoDiscoveryDao autoDiscoveryDao, ClientValidationUtils clientValidationUtils) {
        this.accountDao = accountDao;
        this.controllerDao = controllerDao;
        this.connectionDetailsDao = connectionDetailsDao;
        this.autoDiscoveryDao = autoDiscoveryDao;
        this.clientValidationUtils = clientValidationUtils;
    }

    /**
     * Validates the client request for account identifier and prepares UtilityBean for downstream processing.
     * Adds detailed logging for request parameters and execution time.
     *
     * @return UtilityBean with request parameters and metadata.
     * @throws ClientException if validation fails.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(Object... arguments) throws ClientException {
        try {
            String accountIdentifier = (String) arguments[0];
            accountIdentifier = (accountIdentifier != null) ? accountIdentifier.trim() : "";
            log.debug("[clientValidation] Received accountIdentifier: {}", accountIdentifier);
            if (accountIdentifier.isEmpty()) log.warn("Account identifier is empty or null");

            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];
            log.info("[clientValidation] Validation successful for accountIdentifier: {}", accountIdentifier);
            return UtilityBean.<String>builder()
                    .requestParams(requestParamsMap)
                    .metadata(new HashMap<>())
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error("[clientValidation] Exception occurred", e);
            throw new ClientException("Invalid request body");
        }
    }

    /**
     * Performs server-side validation for the account and fetches the list of services.
     * Adds detailed logging for account identifier, result size, and execution time.
     *
     * @param utilityBean UtilityBean containing client validated parameters.
     * @return UtilityBean with list of ControllerBean services and metadata.
     * @throws ServerException if validation or data fetch fails.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ControllerBean>> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        try {
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            log.debug("[serverValidation] Validating accountIdentifier: {}", accountIdentifier);
            AccountBean accountBean = accountDao.getAccountDetailsForIdentifier(accountIdentifier);
            int accountId = accountBean.getId();
            List<ControllerBean> controllerBeanList;
            try {
                controllerBeanList = controllerDao.getServicesList(accountId);
                log.info("[serverValidation] Fetched {} services for accountId: {}", controllerBeanList.size(), accountId);
            } catch (HealControlCenterException e) {
                log.warn("[serverValidation] No services found for accountId: {}", accountId);
                controllerBeanList = Collections.emptyList();
            }

            return UtilityBean.<List<ControllerBean>>builder()
                    .requestParams(utilityBean.getRequestParams())
                    .metadata(new HashMap<>())
                    .pageable(utilityBean.getPageable())
                    .pojoObject(controllerBeanList)
                    .build();
        } catch (Exception e) {
            log.error("[serverValidation] Exception occurred", e);
            throw new ServerException("Server validation failed");
        }
    }

    /**
     * Processes the validated UtilityBean to fetch, merge, and paginate connection data.
     * Adds detailed logging for input parameters, result size, and execution time.
     *
     * @param utilityBean UtilityBean containing validated ControllerBean list and parameters.
     * @return Paginated Page of GetConnectionPojo objects.
     * @throws DataProcessingException if any error occurs during processing.
     */
    @Override
    @LogExecutionAnnotation
    public Page<GetConnectionPojo> process(UtilityBean<List<ControllerBean>> utilityBean) throws DataProcessingException {
        Pageable pageable = utilityBean.getPageable();
        String searchTerm = utilityBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);
        PaginationUtils.validatePagination(pageable);
        ControllerBean controllerBean = utilityBean.getPojoObject().get(0);
        int accountId = controllerBean.getAccountId();

        try {
            if (searchTerm == null) {
                log.warn("[process] Search term is null in UtilityBean requestParams. Defaulting to empty string.");
                searchTerm = "";
            }
            log.info("[process] Fetching connections for accountId={}, searchTerm={}, pageable={}", accountId, searchTerm, pageable);
            List<GetConnectionPojo> connections = connectionDetailsDao.getConnectionsByAccountId(accountId, searchTerm, pageable)
                    .parallelStream()
                    .map(c -> GetConnectionPojo.builder()
                            .sourceServiceId(c.getSourceId())
                            .sourceServiceName(c.getSourceName())
                            .sourceServiceIdentifier(c.getSourceIdentifier())
                            .destinationServiceId(c.getDestinationId())
                            .destinationServiceName(c.getDestinationName())
                            .destinationServiceIdentifier(c.getDestinationIdentifier())
                            .process(c.getIsDiscovery() == 0 ? "Manual" : "Auto")
                            .status(DiscoveryStatus.ADDED_TO_SYSTEM)
                            .lastDiscoveryRunTime(DateTimeUtil.getGMTToEpochTime(String.valueOf(c.getUpdatedTime())))
                            .build())
                    .toList();
            log.debug("[process] Found {} manual connections for accountId={}", connections.size(), accountId);
            List<GetConnectionPojo> autoDiscoConnections = autoDiscoveryDao.getDiscoveredConnectionsList(searchTerm, pageable)
                    .parallelStream()
                    .map(c -> GetConnectionPojo.builder()
                            .sourceServiceId(c.getSourceId())
                            .sourceServiceName(c.getSourceName())
                            .sourceServiceIdentifier(c.getSourceIdentifier())
                            .destinationServiceId(c.getDestinationId())
                            .destinationServiceName(c.getDestinationName())
                            .destinationServiceIdentifier(c.getDestinationIdentifier())
                            .process("Auto")
                            .status(c.getDiscoveryStatus())
                            .lastDiscoveryRunTime(c.getLastUpdatedTime())
                            .build())
                    .toList();
            log.debug("[process] Found {} auto-discovered connections for accountId={}", autoDiscoConnections.size(), accountId);
            List<GetConnectionPojo> allConnections = new ArrayList<>();
            allConnections.addAll(connections);
            allConnections.addAll(autoDiscoConnections);
            allConnections = allConnections.parallelStream()
                    .sorted(Comparator.comparing(GetConnectionPojo::getProcess)
                            .thenComparing(GetConnectionPojo::getLastDiscoveryRunTime, Comparator.reverseOrder()))
                    .distinct().toList();
            int totalCount = connectionDetailsDao.countConnectionsByAccountId(accountId, searchTerm) +
                    autoDiscoveryDao.countDiscoveredConnections(searchTerm);
            log.info("[process] Total connections found for accountId={}: {}", accountId, allConnections.size());
            return PaginationUtils.createPage(allConnections, pageable, totalCount);
        } catch (Exception e) {
            log.error("[process] Exception occurred", e);
            throw new DataProcessingException("Error processing connections: " + e.getMessage());
        }
    }
}
