package com.heal.controlcenter.businesslogic;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.ActionScriptDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ForensicActionsCategoryPojo;
import com.heal.controlcenter.pojo.ForensicActionsParametersPojo;
import com.heal.controlcenter.pojo.ForensicActionsPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetForensicActionsBL implements BusinessLogic<Object, UtilityBean<Integer>, Page<ForensicActionsPojo>> {

    private final ActionScriptDao actionScriptDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final UserValidationUtil userValidationUtil;
    private final MasterDataRepo masterDataRepo;
    private final UserDao userDao;
    private final ObjectMapper objectMapper;

    public GetForensicActionsBL(ActionScriptDao actionScriptDao, ClientValidationUtils clientValidationUtils,
                                ServerValidationUtils serverValidationUtils, UserValidationUtil userValidationUtil,
                                MasterDataRepo masterDataRepo, UserDao userDao, ObjectMapper objectMapper) {
        this.actionScriptDao = actionScriptDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.userValidationUtil = userValidationUtil;
        this.masterDataRepo = masterDataRepo;
        this.userDao = userDao;
        this.objectMapper = objectMapper;
    }

    /**
     * Validates the client request for getting forensic actions.
     *
     * @param arguments The request parameters, including account identifier, search term, and other filters.
     * @return A {@link UtilityBean} containing the parsed and validated request parameters.
     * @throws ClientException if the validation fails, for example, if the account identifier is invalid or a mandatory parameter is missing.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<Object> clientValidation(Object... arguments) throws ClientException {
        try {
            String accountIdentifier = (String) arguments[0];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            String searchTerm = (arguments.length > 1) ? (String) arguments[1] : "";
            searchTerm = (searchTerm != null) ? searchTerm.trim() : "";

            String commandName = (arguments.length > 2) ? (String) arguments[2] : null;
            String commandTimeoutInSeconds = (arguments.length > 3) ? (String) arguments[3] : null;
            String categoryList = (arguments.length > 4) ? (String) arguments[4] : null;
            String status = (arguments.length > 5) ? (String) arguments[5] : null;
            String lastModifiedBy = (arguments.length > 6) ? (String) arguments[6] : null;
            String type = (arguments.length > 7) ? (String) arguments[7] : null;

            if (type == null || type.trim().isEmpty()) {
                throw new ClientException("Mandatory parameter 'type' is missing.");
            }
            type = type.trim();
            if (!Constants.OOB.equalsIgnoreCase(type) && !Constants.CUSTOM.equalsIgnoreCase(type)) {
                throw new ClientException("Parameter 'type' must be either 'OOB' or 'Custom'.");
            }

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            requestParamsMap.put(Constants.SEARCH_TERM_KEY, searchTerm);
            if (commandName != null) requestParamsMap.put("commandName", commandName);
            if (commandTimeoutInSeconds != null) requestParamsMap.put("commandTimeoutInSeconds", commandTimeoutInSeconds);
            if (categoryList != null) requestParamsMap.put("categoryList", categoryList);
            if (status != null) requestParamsMap.put("status", status);
            if (lastModifiedBy != null) requestParamsMap.put("lastModifiedBy", lastModifiedBy);
            requestParamsMap.put("type", type);

            return UtilityBean.builder()
                    .requestParams(requestParamsMap)
                    .basicUserDetails(basicUserDetails)
                    .metadata(new HashMap<>())
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Validates the server-side requirements for fetching forensic actions.
     *
     * @param utilityBean A {@link UtilityBean} containing the request parameters from the client validation step.
     * @return A {@link UtilityBean} containing the account ID as its POJO object.
     * @throws ServerException if the account is not found or if there's an issue with user access details.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<Integer> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
        try {
            UserAccessBean accessDetails = userDao.fetchUserAccessDetailsUsingIdentifier(userId);
            AccessDetailsBean accessDetailsBean = objectMapper.readValue(accessDetails.getAccessDetails(), AccessDetailsBean.class);
            if (accessDetailsBean == null) throw new ServerException(UIMessages.INVALID_USER_ACCESS_DETAILS);

            return UtilityBean.<Integer>builder()
                    .pojoObject(account.getId())
                    .pageable(utilityBean.getPageable())
                    .requestParams(utilityBean.getRequestParams())
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.FAILED_TO_PROCESS_USER_ACCESS, userId, e);
            throw new ServerException(UIMessages.USER_ACCESS_DETAILS_NOT_FOUND);
        }

    }

    /**
     * Processes the request to fetch a paginated list of forensic actions based on the provided filters.
     *
     * @param utilityBean A {@link UtilityBean} containing the account ID, pagination information, and filtering criteria.
     * @return A {@link Page} of {@link ForensicActionsPojo} objects representing the forensic actions.
     * @throws DataProcessingException if there is an error while fetching data from the database or processing it.
     */
    @Override
    @LogExecutionAnnotation
    public Page<ForensicActionsPojo> process(UtilityBean<Integer> utilityBean) throws DataProcessingException {
        Integer accountId = utilityBean.getPojoObject();
        Pageable pageable = utilityBean.getPageable();
        String searchTerm = utilityBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);
        String commandName = utilityBean.getRequestParams().get("commandName");
        String commandTimeoutInSeconds = utilityBean.getRequestParams().get("commandTimeoutInSeconds");
        String categoryListString = utilityBean.getRequestParams().get("categoryList");
        List<String> categoryList = null;
        if (categoryListString != null && !categoryListString.isEmpty()) {
            categoryList = Arrays.asList(categoryListString.split(","));
        }
        String status = utilityBean.getRequestParams().get("status");
        String lastModifiedBy = utilityBean.getRequestParams().get("lastModifiedBy");
        String type = utilityBean.getRequestParams().get("type");

        if (searchTerm == null) {
            searchTerm = "";
        }

        PaginationUtils.validatePagination(pageable);
        List<ViewTypes> viewTypes = masterDataRepo.getTypes();
        Optional<ViewTypes> standardType = viewTypes.stream().filter(t -> t.getTypeName().equalsIgnoreCase(Constants.STANDARD_TYPE)).filter(t -> t.getSubTypeName().equalsIgnoreCase(type)).findFirst();

        if (standardType.isEmpty()) {
            String errorMessage = String.format("Configuration error: Action type '%s' not found in mst_sub_type table for type name '%s'.", type, Constants.STANDARD_TYPE);
            log.error(errorMessage);
            throw new DataProcessingException(errorMessage);
        }
        int subTypeId = standardType.get().getSubTypeId();

        try {
            List<ForensicActionBean> forensicActions = actionScriptDao.getForensicActions(accountId, pageable, searchTerm, commandName, commandTimeoutInSeconds, categoryList, status, lastModifiedBy, subTypeId);
            int totalCount = actionScriptDao.getForensicActionsCount(accountId, searchTerm, commandName, commandTimeoutInSeconds, categoryList, status, lastModifiedBy, subTypeId);

            if (forensicActions.isEmpty()) {
                return new PageImpl<>(Collections.emptyList(), pageable, totalCount);
            }

            Map<Integer, List<ForensicActionsCategoryPojo>> forensicActionsCategoryMap = forensicActions.stream()
                    .collect(Collectors.groupingBy(ForensicActionBean::getActionId,
                            Collectors.mapping(f -> ForensicActionsCategoryPojo.builder()
                                    .action(f.getActionName())
                                    .actionId(f.getActionId())
                                    .custom(String.valueOf(f.getIsCustomCategory()))
                                    .id(f.getCategoryId())
                                    .name(f.getCategoryName())
                                    .identifier(f.getCategoryIdentifier())
                                    .build(), Collectors.toList())));

            Map<Integer, List<ForensicActionsParametersPojo>> forensicActionArgumentsMap = actionScriptDao.getForensicsParameters()
                    .stream()
                    .collect(Collectors.groupingBy(ForensicActionArgumentsBean::getCommandId,
                            Collectors.mapping(f -> ForensicActionsParametersPojo.builder()
                                    .id(f.getId())
                                    .type(f.getType())
                                    .argument_key(f.getArgument_key())
                                    .value(f.getValue())
                                    .defaultValue(f.getDefaultValue())
                                    .valueType(f.getValueType())
                                    .build(), Collectors.toList())));

            Map<Integer, ForensicActionBean> uniqueForensicActionsMap = new LinkedHashMap<>();
            for (ForensicActionBean bean : forensicActions) {
                uniqueForensicActionsMap.putIfAbsent(bean.getActionId(), bean);
            }

            List<ForensicActionsPojo> actionsList = uniqueForensicActionsMap.values().stream()
                    .map(bean -> ForensicActionsPojo.builder()
                            .id(bean.getActionId())
                            .name(bean.getActionName())
                            .type(bean.getActionType())
                            .commandName(bean.getCommandName())
                            .commandIdentifier(bean.getCommandIdentifier())
                            .commandTimeoutInSeconds(String.valueOf(bean.getCommandTimeoutInSeconds()))
                            .supCtrlTimeoutInSeconds(String.valueOf(bean.getSupCtrlTimeoutInSeconds()))
                            .supCtrlRetryCount(String.valueOf(bean.getSupCtrlRetryCount()))
                            .status(bean.getStatus())
                            .lastModifiedBy(bean.getLastModifiedBy())
                            .lastModifiedOn(bean.getLastModifiedOn())
                            .categoryList(forensicActionsCategoryMap.getOrDefault(bean.getActionId(), new ArrayList<>()))
                            .parameters(forensicActionArgumentsMap.getOrDefault(bean.getCommandId(), new ArrayList<>()))
                            .build())
                    .collect(Collectors.toList());

            return new PageImpl<>(actionsList, pageable, totalCount);

        } catch (Exception e) {
            log.error("Error while fetching forensic actions for account: {}. Details: ", accountId, e);
            throw new DataProcessingException("Error occurred while getting forensic actions.");
        }
    }
}