package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.UserAccessibleActions;
import com.heal.controlcenter.beans.UserAttributesBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class UserAccessibleActionBL implements BusinessLogic<String, UserAttributesBean, UserAccessibleActions> {

    private final UserDao userAccessDataDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public UserAccessibleActionBL(UserDao userAccessDataDao, ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.userAccessDataDao = userAccessDataDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(Object... arguments) throws ClientException {
        try {
            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            return UtilityBean.<String>builder()
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    @Override
    public UserAttributesBean serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        String userId = utilityBean.getBasicUserDetails().getUserIdentifier();

        UserAttributesBean userAttributesBean;
        try {
            userAttributesBean = userAccessDataDao.getRoleProfileInfoForUserId(userId);
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }

        if(null == userAttributesBean) {
            log.error("User details unavailable for userId [{}]", userId);
            throw new ServerException("User details unavailable");
        }

        return userAttributesBean;
    }

    @Override
    public UserAccessibleActions process(UserAttributesBean userAttributesBean) throws DataProcessingException {
        List<String> allowedActions;
        try {
            allowedActions = userAccessDataDao.getUserAccessibleActions(userAttributesBean.getAccessProfileId());
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        return UserAccessibleActions.builder()
                .profileId(userAttributesBean.getAccessProfileId())
                .profile(userAttributesBean.getAccessProfileName())
                .roleId(userAttributesBean.getRoleId())
                .role(userAttributesBean.getRoleName())
                .isActiveDirectory(0)
                .allowedActions(allowedActions)
                .build();
    }
}