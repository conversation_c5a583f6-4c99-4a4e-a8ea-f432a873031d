package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentCommandArgumentsPojo;
import com.heal.controlcenter.pojo.AgentCommandsPojo;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.heal.controlcenter.util.Constants.*;

@Slf4j
@Component
public class GetAgentCommandsBL implements BusinessLogic<List<String>, UtilityBean<List<String>>, AgentCommandsPojo> {

    private final AccountsDao accountsDao;
    private final MasterDataDao masterDataDao;
    private final CommandDataDao commandDataDao;
    private final ControllerDao controllerDao;
    private final AgentDao agentDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private ViewTypesBean jimAgentType;

    public GetAgentCommandsBL(AccountsDao accountsDao, MasterDataDao masterDataDao, CommandDataDao commandDataDao,
                              ControllerDao controllerDao, AgentDao agentDao,
                              ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.accountsDao = accountsDao;
        this.masterDataDao = masterDataDao;
        this.commandDataDao = commandDataDao;
        this.controllerDao = controllerDao;
        this.agentDao = agentDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<String>> clientValidation(Object... arguments) throws ClientException {
        try {
            String accountIdentifier = (String) arguments[0];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            String serviceIdentifier = (String) arguments[1];
            clientValidationUtils.serviceIdentifierValidation(serviceIdentifier);

            int serviceId;
            try {
                serviceId = Integer.parseInt(serviceIdentifier);
            } catch (NumberFormatException e) {
                log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, serviceIdentifier));
                throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
            }

            if(serviceId <= 0) {
                log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, serviceIdentifier));
                throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
            }

            String agentType = (String) arguments[2];
            if (agentType.isEmpty()) {
                agentType = JIM_AGENT_SUB_TYPE;
            }

            List<String> pojoObject = new ArrayList<>();
            pojoObject.add(serviceIdentifier);
            pojoObject.add(agentType);

            HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            return UtilityBean.<List<String>>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(pojoObject)
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }


    @Override
    public UtilityBean<List<String>> serverValidation(UtilityBean<List<String>> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountsDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE,accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setMetadata(Map.of(Constants.ACCOUNT, account));

        int serviceId = Integer.parseInt(utilityBean.getPojoObject().get(0));

        ControllerBean service = null;
        try {
            service = controllerDao.getServiceById(serviceId, account.getId());
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
        if (null == service) {
            String message = String.format("ServiceId [%d] is unavailable for account [%s]", serviceId, utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
            log.error(message);
            throw new ServerException(message);
        }

        return utilityBean;
    }

    @Override
    public AgentCommandsPojo process(UtilityBean<List<String>> bean) throws DataProcessingException {
        AgentCommandsPojo agentCommand = new AgentCommandsPojo();
        String agentType = bean.getPojoObject().get(1);
        int serviceId = Integer.parseInt(bean.getPojoObject().get(0));
        int accountId = ((AccountBean) bean.getMetadata().get(ACCOUNT)).getId();

        try {
            jimAgentType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(AGENT_TYPE, agentType);
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        List<CommandDetailsBean> commandDetails;
        try {
            commandDetails = getCommandsByAgentType(agentType);
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        AgentCommandArgumentsPojo agentCommandArguments = new AgentCommandArgumentsPojo();
        for (CommandDetailsBean command : commandDetails) {
            List<CommandArgumentBean> serviceCmdArgs = null;
            try {
                serviceCmdArgs = agentDao.getServiceCommandArguments(serviceId,
                        jimAgentType.getSubTypeId(), command.getId());
            } catch (HealControlCenterException e) {
                throw new DataProcessingException(e.getMessage());
            }

            Map<String, String> cmdsMap = serviceCmdArgs.parallelStream()
                    .collect(Collectors.toMap(CommandArgumentBean::getArgumentKey, CommandArgumentBean::getDefaultValue));

            getAgentCommandArgs(agentCommandArguments, cmdsMap, command.getName());
        }

        AgentModeConfigBean configBean;
        CommandDetailsBean commandBean;
        try {
            configBean = agentDao.getAgentModeConfig(serviceId, accountId, jimAgentType.getSubTypeId());
            if (configBean == null) {
                commandBean = getSelectedCommand(agentType, null);
            } else {
                commandBean = commandDetails.parallelStream()
                        .filter(c -> c.getId() == configBean.getCommandId())
                        .findAny()
                        .orElse(null);
                agentCommand.setLastUpdatedTime(String.valueOf(configBean.getUpdatedTime()));
            }
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        if (commandBean == null) {
            agentCommand.setCommandMode(AGENT_MODE_AUTO);
            agentCommand.setCommandName(agentType);
        } else {
            agentCommand.setCommandMode(commandBean.getName());
            agentCommand.setCommandName(agentType);
            agentCommand.setIdentifier("JIMAgentIdentifier");
        }

        agentCommand.setServerTime(String.valueOf(DateTimeUtil.getCurrentTimestampInGMT()));
        agentCommand.setArguments(agentCommandArguments);

        return agentCommand;
    }

    public List<CommandDetailsBean> getCommandsByAgentType(String agentType) throws HealControlCenterException {
        List<CommandDetailsBean> commandDetails;
        try {
            jimAgentType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(AGENT_TYPE, agentType);
            commandDetails = commandDataDao.getCommandDetailsByAgentType(jimAgentType.getSubTypeId());
        } catch (HealControlCenterException e) {
            throw new HealControlCenterException(e.getMessage());
        }
        if(commandDetails.isEmpty()) {
            log.error("No commands found to process the request. agentType:{}", agentType);
            throw new HealControlCenterException("No commands found to process the request.");
        }
        return commandDetails;
    }

    public void getAgentCommandArgs(AgentCommandArgumentsPojo agentCommandArguments, Map<String, String> cmdArgsMap, String commandName) {
        String SNAPSHOT_COUNT = "snapshot-count";
        String SNAPSHOT_DURATION = "snapshot-duration";
        String SILENT_WINDOW = "silent-window";

        if(AGENT_MODE_AUTO.equalsIgnoreCase(commandName)) {
            agentCommandArguments.setAutoSnapshotDuration(Integer.parseInt(cmdArgsMap.get(SNAPSHOT_DURATION)));
            agentCommandArguments.setAutoSnapshotCount(Integer.parseInt(cmdArgsMap.get(SNAPSHOT_COUNT)));
            agentCommandArguments.setAutoSnapshotForException(Boolean.parseBoolean(cmdArgsMap.get("exceptions")));
            agentCommandArguments.setAutoSnapshotSilentWindow(Integer.parseInt(cmdArgsMap.getOrDefault(SILENT_WINDOW, Constants.AGENT_SILENT_WINDOW)));
            agentCommandArguments.setJvmCpuUtil(Integer.parseInt(cmdArgsMap.get("jvm_cpu")));
            agentCommandArguments.setJvmMemUtil(Integer.parseInt(cmdArgsMap.get("jvm_mem")));
            agentCommandArguments.setAutoSnapshotCollectionLevel(cmdArgsMap.get("collection-mode"));
        } else if(AGENT_MODE_VERBOSE.equalsIgnoreCase(commandName)) {
            agentCommandArguments.setVerboseSnapshotCount(Integer.parseInt(cmdArgsMap.get(SNAPSHOT_COUNT)));
            agentCommandArguments.setVerboseSnapshotDuration(Integer.parseInt(cmdArgsMap.get(SNAPSHOT_DURATION)));
            agentCommandArguments.setVerboseSnapshotSilentWindow(Integer.parseInt(cmdArgsMap.getOrDefault(SILENT_WINDOW,Constants.AGENT_SILENT_WINDOW)));
        }
    }

    public CommandDetailsBean getSelectedCommand(String agentType, String commandName) throws HealControlCenterException {
        List<CommandDetailsBean> commandDetails = getCommandsByAgentType(agentType);
        if(commandDetails.isEmpty()) {
            log.error("No commands found to process the request. commandName:{}, agentType:{}", commandName, agentType);
            return null;
        }

        CommandDetailsBean commandSelected = null;
        if (commandName != null) {
            commandSelected = commandDetails.stream()
                    .filter(c -> c.getName().equalsIgnoreCase(commandName))
                    .findAny()
                    .orElse(null);
        }

        if(commandSelected == null) {
            commandSelected = commandDetails.stream()
                    .filter(c -> c.getIsDefault() == 1)
                    .findAny()
                    .orElse(null);
        }
        return commandSelected;
    }
}
