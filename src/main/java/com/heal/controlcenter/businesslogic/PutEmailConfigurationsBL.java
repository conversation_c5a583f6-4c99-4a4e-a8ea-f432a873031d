package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.SMTPDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.SMTPDetailsPojo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.DataLengthException;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class PutEmailConfigurationsBL implements BusinessLogic<SMTPDetailsPojo, UtilityBean<SMTPDetailsPojo>, Object> {

    private final AccountsDao accountDao;
    private final NotificationsDao notificationsDao;
    private final MasterDataDao masterDataDao;
    private final AECSBouncyCastleUtil aecsBouncyCastleUtil;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public PutEmailConfigurationsBL(AccountsDao accountDao, NotificationsDao notificationsDao,
                                    MasterDataDao masterDataDao, AECSBouncyCastleUtil aecsBouncyCastleUtil,
                                    ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.accountDao = accountDao;
        this.notificationsDao = notificationsDao;
        this.masterDataDao = masterDataDao;
        this.aecsBouncyCastleUtil = aecsBouncyCastleUtil;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    /**
     * Validates client-side inputs including auth key, account identifier, and SMTP payload.
     * Uses utility validation methods and internal validation logic of SMTPDetailsPojo.
     * Returns a UtilityBean containing request parameters and the SMTP details object.
     * Throws ClientException if validation fails.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<SMTPDetailsPojo> clientValidation(Object... arguments) throws ClientException {
        try {
            SMTPDetailsPojo smtpDetailsPojo = (SMTPDetailsPojo) arguments[0];
            String accountIdentifier = (String) arguments[1];

            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            Map<String, String> error = smtpDetailsPojo.validate();
            if (!error.isEmpty()) {
                String err = error.toString();
                log.error(err);
                throw new ClientException(err);
            }

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);

            return UtilityBean.<SMTPDetailsPojo>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(smtpDetailsPojo)
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Validates server-side inputs such as auth key and account identifier.
     * Fetches and attaches the AccountBean to the UtilityBean for further processing.
     * Adds the validated userId to the request parameters.
     * Throws ServerException if the account or user is invalid.
     */
    @Override
    public UtilityBean<SMTPDetailsPojo> serverValidation(UtilityBean<SMTPDetailsPojo> utilityBean) throws ServerException {
        String userId = utilityBean.getBasicUserDetails().getUserIdentifier();

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setMetadata(Map.of(Constants.ACCOUNT, account));

        return UtilityBean.<SMTPDetailsPojo>builder()
                .metadata(Map.of(Constants.USER_ID, userId))
                .build();
    }

    /**
     * Updates existing SMTP configuration in the database for a valid account.
     * Handles password decryption and re-encryption, and validates security protocol.
     * Throws DataProcessingException if update fails or if data is not in expected state.
     * Ensures transaction rollback on exceptions.
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Object process(UtilityBean<SMTPDetailsPojo> smtpUtilityBean) throws DataProcessingException {
        Date time = DateTimeUtil.getCurrentTimestampInGMT();
        DateFormat dateFormat = new SimpleDateFormat(Constants.DATE_TIME);
        String createdTime = dateFormat.format(time);
        SMTPDetailsPojo smtpDetails = smtpUtilityBean.getPojoObject();
        AccountBean account = (AccountBean) smtpUtilityBean.getMetadata().get(Constants.ACCOUNT);
        String userId = (String) smtpUtilityBean.getMetadata().get(Constants.USER_ID);
        String plainTxt = "";

        SMTPDetailsBean smtpDetailsBeanExists = notificationsDao.getSMTPDetails(account.getId());
        if (smtpDetailsBeanExists == null) {
            log.error("Email details not found for accountId [{}].", account.getId());
            throw new DataProcessingException("Error occurred, Email details not found.");
        }

        ViewTypesBean securityType;
        try {
            securityType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.SMTP_PROTOCOLS, smtpDetails.getSecurity());
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        if (securityType == null) {
            log.error("Security type details unavailable for type [{}].", smtpDetails.getSecurity());
            throw new DataProcessingException("Security type details unavailable");
        }

        if (smtpDetails.getPassword() == null || smtpDetails.getPassword().trim().isEmpty()) {
            smtpDetails.setPassword("");

        } else {
            try {
                plainTxt = aecsBouncyCastleUtil.decrypt(smtpDetails.getPassword());
                if (plainTxt.isEmpty()) {
                    String err = "Password is not encrypted properly";
                    log.error(err);
                    throw new DataProcessingException(err);
                }
            } catch (InvalidCipherTextException | DataLengthException e) {
                log.error("Exception encountered while decrypting the password. Details: ", e);
                throw new DataProcessingException("Error occurred while decrypting the password.");
            }
        }

        try {
            smtpDetails.setPassword(CommonUtils.encryptInBCEC(plainTxt));
        } catch (Exception e) {
            log.error("Exception encountered while encrypting the password. Details: ", e);
            throw new DataProcessingException("Error occurred while encrypting the password from the database");
        }

        SMTPDetailsBean smtpDetailsBean = SMTPDetailsBean.builder()
                .accountId(account.getId())
                .address(smtpDetails.getAddress())
                .port(smtpDetails.getPort())
                .lastModifiedBy(userId)
                .username(smtpDetails.getUsername())
                .password(smtpDetails.getPassword())
                .securityId(securityType.getSubTypeId())
                .createdTime(createdTime)
                .updatedTime(createdTime)
                .status(1)
                .fromRecipient(smtpDetails.getFromRecipient())
                .build();

        try {
            notificationsDao.updateSMTPDetails(smtpDetailsBean);
        } catch (Exception e) {
            throw new DataProcessingException(e.getMessage());
        }

        return null;
    }
}