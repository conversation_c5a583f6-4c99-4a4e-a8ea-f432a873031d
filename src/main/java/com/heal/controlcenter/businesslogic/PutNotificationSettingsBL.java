package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.NotificationSettingsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.NotificationSettingsPojo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;

@Slf4j
@Service
public class PutNotificationSettingsBL implements BusinessLogic<List<NotificationSettingsPojo>, UtilityBean<List<NotificationSettingsPojo>>, Object> {

    private final NotificationsDao notificationsDao;
    private final AccountsDao accountDao;
    private final MasterDataDao masterDataDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public PutNotificationSettingsBL(NotificationsDao notificationsDao, AccountsDao accountDao, MasterDataDao masterDataDao,
                                     ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.notificationsDao = notificationsDao;
        this.accountDao = accountDao;
        this.masterDataDao = masterDataDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    private int longId;
    private int tooLongId;
    private String longName;
    private String tooLongName;

    @Value("${openForLong.minDuration.time.min:15}")
    private int MIN_OPEN_FOR_LONG;
    @Value("${openForTooLong.minDuration.time.min:30}")
    private int MIN_OPEN_FOR_TOO_LONG;
    @Value("${openForLong.maxDuration.time.min:1440}")
    private int MAX_OPEN_FOR_LONG;
    @Value("${openForTooLong.maxDuration.time.min:2880}")
    private int MAX_OPEN_FOR_TOO_LONG;

    /**
     * Validates client-side inputs including auth key, account identifier, and notification settings list.
     * Ensures there are no duplicates and each item is valid.
     * Validates specific configuration constraints like type names and durations.
     * Returns a UtilityBean wrapping the validated data or throws ClientException.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<NotificationSettingsPojo>> clientValidation(Object... arguments) throws ClientException {
        try {
            List<NotificationSettingsPojo> settings = (List<NotificationSettingsPojo>) arguments[0];
            String accountIdentifier = (String) arguments[1];

            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            if (settings.isEmpty()) {
                log.error(UIMessages.REQUEST_BODY_NULL);
                throw new ClientException(UIMessages.REQUEST_BODY_NULL_EXCEPTION_MESSAGE);
            }

            Set<NotificationSettingsPojo> settingsSet = new HashSet<>(settings);
            if (settingsSet.size() < settings.size()) {
                String DUPLICATE_NOTIFICATION_SETTING = "Duplicate Notification Settings found.";
                log.error(DUPLICATE_NOTIFICATION_SETTING);
                throw new ClientException(DUPLICATE_NOTIFICATION_SETTING);
            }

            for (NotificationSettingsPojo setting : settings) {
                setting.validate();
                if (!setting.getError().isEmpty()) {
                    String err = setting.getError().toString();
                    log.error(err);
                    throw new ClientException(err);
                }
            }

            validateData(settings);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);

            return UtilityBean.<List<NotificationSettingsPojo>>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(settings)
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Validates server-side parameters such as auth key and account identifier.
     * Fetches account details and attaches them to the UtilityBean for further processing.
     * Adds the user ID to request parameters for auditing and processing.
     * Throws ServerException on invalid or missing account.
     */
    @Override
    public UtilityBean<List<NotificationSettingsPojo>> serverValidation(UtilityBean<List<NotificationSettingsPojo>> utilityBean) throws ServerException {
        String userId = utilityBean.getBasicUserDetails().getUserIdentifier();

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setMetadata(Map.of(Constants.ACCOUNT, account));

        return UtilityBean.<List<NotificationSettingsPojo>>builder()
                .metadata(Map.of(Constants.USER_ID, userId))
                .build();
    }

    /**
     * Processes and updates the notification settings for a given account.
     * Validates minimum/maximum thresholds and adjusts duration values as per rules.
     * Persists the updated settings in the database.
     * Throws DataProcessingException if database update or data preparation fails.
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public Object process(UtilityBean<List<NotificationSettingsPojo>> settingsBean) throws DataProcessingException {
        List<NotificationSettingsPojo> notificationSettings = settingsBean.getPojoObject();
        List<NotificationSettingsBean> settings = new ArrayList<>();
        String userId = (String) settingsBean.getMetadata().get(Constants.USER_ID);
        AccountBean accountBean = (AccountBean) settingsBean.getMetadata().get(Constants.ACCOUNT);
        Timestamp timestamp;

        try {
            timestamp = new Timestamp(DateTimeUtil.getDateInGMT(System.currentTimeMillis()).getTime());
        } catch (ParseException e) {
            log.error("Error while fetching current time in GMT. Details: ", e);
            throw new DataProcessingException("Error while fetching current time in GMT");
        }

        List<NotificationSettingsBean> settingsDB;
        try {
            settingsDB = notificationsDao.getNotificationSetting(accountBean.getId());
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        if(settingsDB.isEmpty()) {
            log.error("Notification settings unavailable for account [{}].", accountBean.getIdentifier());
            throw new DataProcessingException("Notification settings unavailable for the account.");
        }

        int openForLong = notificationSettings.stream().filter(notificationSetting -> notificationSetting.getTypeName().equals(longName))
                .filter(notificationSetting -> (notificationSetting.getTypeId() == longId))
                .map(NotificationSettingsPojo::getDurationInMin).map(Float::intValue).findAny().orElse(MIN_OPEN_FOR_LONG);
        int openForTooLong = notificationSettings.stream().filter(notificationSetting -> notificationSetting.getTypeName().equals(tooLongName))
                .filter(notificationSetting -> (notificationSetting.getTypeId() == tooLongId))
                .map(NotificationSettingsPojo::getDurationInMin).map(Float::intValue).findAny().orElse(MIN_OPEN_FOR_TOO_LONG);

        if (openForLong < MIN_OPEN_FOR_LONG) {
            openForLong = MIN_OPEN_FOR_LONG;
        } else if (openForLong > MAX_OPEN_FOR_LONG) {
            openForLong = MAX_OPEN_FOR_LONG;
        }

        if (openForTooLong < MIN_OPEN_FOR_TOO_LONG) {
            openForTooLong = MIN_OPEN_FOR_TOO_LONG;
        } else if (openForTooLong > MAX_OPEN_FOR_TOO_LONG) {
            openForTooLong = MAX_OPEN_FOR_TOO_LONG;
        }

        int remainder = (openForTooLong % openForLong);
        if (remainder != 0) {
            openForTooLong = ((openForTooLong / openForLong) * openForLong);
        }
        if (openForLong == openForTooLong) {
            openForTooLong = 2 * openForLong;
        }

        for (NotificationSettingsPojo setting : notificationSettings) {
            NotificationSettingsBean entity = new NotificationSettingsBean();
            entity.setAccountId(accountBean.getId());
            entity.setUpdatedTime(String.valueOf(timestamp));
            if((setting.getTypeName().equals(longName) && (setting.getTypeId() == longId))) {
                entity.setDurationInMin(openForLong);
            }
            else if((setting.getTypeName().equals(tooLongName) && (setting.getTypeId() == tooLongId))) {
                entity.setDurationInMin(openForTooLong);
            }
            entity.setTypeId(setting.getTypeId());
            entity.setLastModifiedBy(userId);
            settings.add(entity);
        }

        try {
            notificationsDao.updateNotificationSetting(settings);
        } catch (Exception e) {
            throw new DataProcessingException("Error occurred, couldn't update notification settings.");
        }

        return null;
    }

    public void validateData(List<NotificationSettingsPojo> notificationSettings) throws ClientException {
        Map<String, String> error = new HashMap<>();
        try {
            longId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.LONG).getSubTypeId();
            tooLongId = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.TOO_LONG).getSubTypeId();
            longName = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.LONG).getSubTypeName();
            tooLongName = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(Constants.NOTIFICATION_TYPE_LITERAL, Constants.TOO_LONG).getSubTypeName();
        } catch (HealControlCenterException e) {
            log.error("Error while fetching viewtypes", e);
            throw new ClientException("Error while fetching viewtypes: " + e.getMessage());
        }

        for (NotificationSettingsPojo settings : notificationSettings) {
            if (!((settings.getTypeName().equals(longName)) || (settings.getTypeName().equals(tooLongName)))) {
                String INVALID_TYPE_NAME = "Invalid Type Name.";
                log.error(INVALID_TYPE_NAME);
                error.put("Type name", INVALID_TYPE_NAME);
            }

            if (!((settings.getTypeName().equals(longName) && (settings.getTypeId() == longId)) || (settings.getTypeName().equals(tooLongName) && (settings.getTypeId() == tooLongId)))) {
                String INVALID_COMBINATION = "Invalid Combination of Type Id and Type Name.";
                log.error(INVALID_COMBINATION);
                error.put("Type name/id combination", INVALID_COMBINATION);
            }

            if ((settings.getDurationInMin() <= 0)) {
                String INVALID_DURATION = "Invalid Duration.";
                log.error(INVALID_DURATION);
                error.put("Duration In Minutes", INVALID_DURATION);
            }

            if (!error.isEmpty()) {
                String err = error.toString();
                log.error(err);
                throw new ClientException(err);
            }
        }
    }
}