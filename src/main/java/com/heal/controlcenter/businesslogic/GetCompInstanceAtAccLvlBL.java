package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.entity.ComponentInstanceDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.GetCompInstance;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.util.PaginationUtils;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Business logic for retrieving component instances at account level.
 * Handles validation, data processing, and pagination for component instance queries.
 */
@Slf4j
@Component
public class GetCompInstanceAtAccLvlBL implements BusinessLogic<String, UtilityBean<AccountBean>, Page<GetCompInstance>> {

    private int hostComponentTypeId;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final ComponentInstanceDao componentInstanceDao;

    /**
     * Constructor for dependency injection.
     * @param clientValidationUtils Utility for client-side validation
     * @param serverValidationUtils Utility for server-side validation
     * @param componentInstanceDao DAO for component instance database operations
     */
    public GetCompInstanceAtAccLvlBL(ClientValidationUtils clientValidationUtils,
                                     ServerValidationUtils serverValidationUtils,
                                     ComponentInstanceDao componentInstanceDao) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.componentInstanceDao = componentInstanceDao;
    }

    /**
     * Validates client request parameters including account identifier and optional search term.
     * @param arguments Array containing account identifier, optional search term, and BasicUserDetails
     * @return UtilityBean with validated parameters
     * @throws ClientException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(Object... arguments) throws ClientException {
        log.debug("[clientValidation] Start with arguments: {}", (Object) arguments);
        try {
            String accountIdentifier = (String) arguments[0];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);
            log.info("[clientValidation] Validated accountIdentifier: {}", accountIdentifier);

            String searchTerm = null;
            if (arguments.length > 1 && arguments[1] instanceof String) {
                searchTerm = (String) arguments[1];
            }

            Map<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            if (searchTerm != null) requestParamsMap.put("searchTerm", searchTerm);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            log.debug("[clientValidation] Returning UtilityBean with params: {}", requestParamsMap);
            return UtilityBean.<String>builder()
                    .requestParams(requestParamsMap)
                    .metadata(new HashMap<>())
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Validates account existence and creates AccountBean from account identifier.
     * @param utilityBean Contains validated client parameters
     * @return UtilityBean with AccountBean object
     * @throws ServerException if account validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<AccountBean> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        log.debug("[serverValidation] Start for GET getComponentInstanceDetails: {}", utilityBean);
        try {
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            Account account = serverValidationUtils.accountValidation(accountIdentifier);
            log.info("[serverValidation] Validated account for identifier: {} with id: {}", accountIdentifier, account.getId());

            AccountBean accountBean = AccountBean.builder()
                    .id(account.getId())
                    .name(account.getName())
                    .createdTime(account.getCreatedTime())
                    .updatedTime(account.getUpdatedTime())
                    .status(account.getStatus())
                    .privateKey(account.getPrivateKey())
                    .publicKey(account.getPublicKey())
                    .lastModifiedBy(account.getLastModifiedBy())
                    .identifier(accountIdentifier)
                    .build();

            MasterComponentTypeBean componentTypeBean = componentInstanceDao.findByNameAndAccountId(Constants.COMPONENT_TYPE_HOST, account.getId());
            if (componentTypeBean == null) {
                String err = "Component with type '" + "Host" + "' doesn't exist.";
                log.error(err);
                throw new ServerException(err);
            }
            hostComponentTypeId = componentTypeBean.getId();

            return UtilityBean.<AccountBean>builder()
                    .pojoObject(accountBean)
                    .metadata(utilityBean.getMetadata())
                    .pageable(utilityBean.getPageable())
                    .requestParams(utilityBean.getRequestParams())
                    .build();
        } catch (Exception e) {
            log.error("[serverValidation] Exception: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }

    /**
     * Processes component instance retrieval by merging pre-existing and auto-discovery data.
     * Applies search filtering and pagination to the results using database-level pagination.
     * @param utilityBean Contains account information and request parameters
     * @return Paginated list of component instances
     * @throws DataProcessingException if data processing fails
     */
    @Override
    @LogExecutionAnnotation
    public Page<GetCompInstance> process(UtilityBean<AccountBean> utilityBean) throws DataProcessingException {
        log.debug("[process] Start fetching component instances with utilityBean: {}", utilityBean);
        Pageable pageable = utilityBean.getPageable();
        String searchTerm = utilityBean.getRequestParams().get("searchTerm");

        try {
            PaginationUtils.validatePagination(pageable);
            AccountBean accountBean = utilityBean.getPojoObject();

            if (searchTerm == null) {
                log.warn("[process] Search term is null. Defaulting to empty string.");
                searchTerm = "";
            }

            log.info("[process] Fetching component instances for accountId: {} with filters - searchTerm: {}, pageable: {}",
                    accountBean.getId(), searchTerm, pageable);

            // Get paginated pre-existing data from database
            List<GetCompInstance> preExistingData = componentInstanceDao.getComponentInstancesForAccount(
                    accountBean.getId(), searchTerm, hostComponentTypeId, pageable);

            // Get total count for pre-existing data
            int preExistingCount = componentInstanceDao.countComponentInstancesForAccount(
                    accountBean.getId(), searchTerm, hostComponentTypeId);

            // Get paginated auto-discovery data from database
            List<GetCompInstance> stagingTableData = componentInstanceDao.getAutoDiscoveryInstances(
                    accountBean.getId(), searchTerm, hostComponentTypeId, pageable);

            // Get total count for auto-discovery data
            int autoDiscoveryCount = componentInstanceDao.countAutoDiscoveryInstances(
                    accountBean.getId(), searchTerm, hostComponentTypeId);

            // Process auto-discovery instances (merge duplicates, filter ports)
            processAutoDiscoveryInstances(stagingTableData);

            // Merge data and remove duplicates
            Set<String> preExistingInstanceIdentifiers = preExistingData.stream()
                    .map(GetCompInstance::getInstanceIdentifier)
                    .collect(Collectors.toSet());

            List<GetCompInstance> uniqueStagingData = stagingTableData.stream()
                    .filter(s -> !preExistingInstanceIdentifiers.contains(s.getInstanceIdentifier()))
                    .collect(Collectors.toList());

            List<GetCompInstance> allInstances = new ArrayList<>();
            allInstances.addAll(preExistingData);
            allInstances.addAll(uniqueStagingData);

            // Populate related data for all instances
            allInstances = populateInstanceRelatedData(allInstances, accountBean.getId());

            // Apply sorting as per business requirements
            allInstances.sort(Comparator.comparing(GetCompInstance::getStatus, Comparator.reverseOrder())
                    .thenComparing(GetCompInstance::getLastDiscoveryRunTime, Comparator.reverseOrder())
                    .thenComparing(GetCompInstance::getInstanceName, Comparator.comparingInt(o -> Character.toLowerCase(o.charAt(0)))));

            // Calculate total count (accounting for potential duplicates)
            int totalCount = preExistingCount + autoDiscoveryCount;


            log.info("[process] Returning {} component instances for accountId: {} (total available: {})",
                    allInstances.size(), accountBean.getId(), totalCount);

            return PaginationUtils.createPage(allInstances, pageable, totalCount);

        } catch (Exception e) {
            log.error("[process] Failed to fetch paginated component instances for account. Reason: {}", e.getMessage(), e);
            throw new DataProcessingException("Failed to fetch paginated component instances for account. Reason: " + e.getMessage());
        }
    }

    /**
     * Populates related data (services, applications, agents, ports) for component instances.
     * @param instances List of component instances to populate
     * @param accountId Account ID
     * @throws DataProcessingException if database operations fail
     */
    private List<GetCompInstance> populateInstanceRelatedData(List<GetCompInstance> instances, int accountId) throws DataProcessingException {

        List<GetCompInstance> updatedInstances = new ArrayList<>();
        try {
            List<String> instanceIdentifiers = instances.stream()
                    .map(GetCompInstance::getInstanceIdentifier)
                    .collect(Collectors.toList());

            // Get service-application mappings from database
            Map<Integer, List<IdNamePojo>> serviceApplicationMap = componentInstanceDao.getServiceApplicationMappings(accountId);

            // Get agent mappings from database
            Map<Integer, List<AgentDetails>> agentBeanMap = componentInstanceDao.getInstanceAgentMappings(accountId);

            // Get services for all instances in one query
            Map<String, List<IdNamePojo>> instanceServiceMap = componentInstanceDao.getServicesForInstances(accountId, instanceIdentifiers);

            // Get port attributes for all instances in one query
            Map<String, List<GetCompInstance.AttributeNameValue>> instancePortMap = componentInstanceDao.getPortAttributesForInstances(accountId, instanceIdentifiers);

            // Process instances and populate related data
            for (GetCompInstance c : instances) {
                    List<IdNamePojo> services = instanceServiceMap.getOrDefault(c.getInstanceIdentifier(), Collections.emptyList());

                    // Get applications from service mappings
                    List<IdNamePojo> applications = services.stream()
                            .map(s -> serviceApplicationMap.get(s.getId()))
                            .filter(Objects::nonNull)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());

                    c.setService(services);
                    c.setApplication(applications);
                    c.setMappedAgents(agentBeanMap.getOrDefault(Integer.parseInt(c.getInstanceId()), new ArrayList<>()));

                    // Get port attributes from map
                    List<GetCompInstance.AttributeNameValue> ports = instancePortMap.getOrDefault(c.getInstanceIdentifier(), Collections.emptyList());
                    c.setPort(ports);
                    updatedInstances.add(c);
            }

        } catch (Exception e) {
            log.error("[populateInstanceRelatedData] Error while populating related data for accountId: {}", accountId, e);
            throw new DataProcessingException("Error while populating component instance related data, accountId:" + accountId);
        }
        return updatedInstances;
    }

    /**
     * Processes auto-discovery component instances by merging duplicates and filtering port attributes.
     * @param componentInstanceList List of auto-discovery component instances to process
     * @throws DataProcessingException if processing fails
     */
    private void processAutoDiscoveryInstances(List<GetCompInstance> componentInstanceList) throws DataProcessingException {
        try {
            log.debug("[processAutoDiscoveryInstances] Processing {} auto-discovery instances", componentInstanceList.size());

            // Process duplicates and merge data (same logic as before)
            componentInstanceList.sort(Comparator.comparing(GetCompInstance::getInstanceId));

            // Remove duplicates logic (keeping the same logic)
            for (int i = 0; i < componentInstanceList.size() - 1; i++) {
                List<IdNamePojo> temp = new ArrayList<>();
                List<GetCompInstance.AttributeNameValue> tempPort = new ArrayList<>();
                if (componentInstanceList.get(i).getInstanceId().equals(componentInstanceList.get(i + 1).getInstanceId())) {
                    if (!new HashSet<>(componentInstanceList.get(i).getService()).containsAll(componentInstanceList.get(i + 1).getService())) {
                        temp.addAll(componentInstanceList.get(i).getService());
                        temp.addAll(componentInstanceList.get(i + 1).getService());
                        componentInstanceList.get(i).setService(temp);
                        temp = new ArrayList<>();
                    }

                    if (!new HashSet<>(componentInstanceList.get(i).getApplication()).containsAll(componentInstanceList.get(i + 1).getApplication())) {
                        temp.addAll(componentInstanceList.get(i).getApplication());
                        temp.addAll(componentInstanceList.get(i + 1).getApplication());
                        componentInstanceList.get(i).setApplication(temp);
                    }

                    if (!new HashSet<>(componentInstanceList.get(i).getPort()).containsAll(componentInstanceList.get(i + 1).getPort())) {
                        tempPort.addAll(componentInstanceList.get(i).getPort());
                        tempPort.addAll(componentInstanceList.get(i + 1).getPort());
                        componentInstanceList.get(i).setPort(tempPort);
                    }

                    componentInstanceList.remove(i + 1);
                    i--;
                }
            }

            // Filter MonitorPort and set HostAddress (same logic as before)
            for (GetCompInstance getCompInstance : componentInstanceList) {
                for (int i = 0; i < getCompInstance.getPort().size(); i++) {
                    if (getCompInstance.getPort().get(i).getAttributeName().equalsIgnoreCase("HostAddress")) {
                        getCompInstance.setHostAddress(Collections.singletonList(getCompInstance.getPort().get(i).getAttributeValue()));
                    }

                    if (!getCompInstance.getPort().get(i).getAttributeName().equalsIgnoreCase("MonitorPort")) {
                        i--;
                        getCompInstance.getPort().remove(i + 1);
                    }
                }
            }

            log.debug("[processAutoDiscoveryInstances] Processed {} auto-discovery instances", componentInstanceList.size());

        } catch (Exception e) {
            log.error("[processAutoDiscoveryInstances] Error while processing auto-discovery data", e);
            throw new DataProcessingException("Error while processing auto-discovery component instance data");
        }
    }
}

