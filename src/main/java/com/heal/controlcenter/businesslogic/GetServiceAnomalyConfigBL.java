package com.heal.controlcenter.businesslogic;

import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Service;
import com.heal.controlcenter.beans.ServiceSuppPersistenceConfigurationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ServiceConfigurationDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class GetServiceAnomalyConfigBL implements BusinessLogic<String, UtilityBean<String>, Map<String, ServiceSuppPersistenceConfigPojo>> {

    private final ServiceConfigurationDao serviceConfigurationDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    @Value("${service.startTime.hour:1}")
    int serviceMinCollectionIntervalHour;
    @Value("${service.endTime.hour:59}")
    int serviceMaxCollectionIntervalHour;
    @Value("${service.startTime.greaterThan.hour:60}")
    int serviceMinCollectionIntervalGreaterThanHour;
    @Value("${service.endTime.greaterThan.hour:1440}")
    int serviceMaxCollectionIntervalGreaterThanHour;
    @Value("${service.low.persistence.hour:5}")
    int lowPersistenceHour;
    @Value("${service.low.suppression.hour:10}")
    int lowSuppressionHour;
    @Value("${service.low.persistence.greaterThan.hour:2}")
    int lowPersistenceGreaterThanHour;
    @Value("${service.low.suppression.greaterThan.hour:5}")
    int lowSuppressionGreaterThanHour;
    @Value("${service.medium.persistence.hour:5}")
    int mediumPersistenceHour;
    @Value("${service.medium.suppression.hour:10}")
    int mediumSuppressionHour;
    @Value("${service.medium.persistence.greaterThan.hour:2}")
    int mediumPersistenceGreaterThanHour;
    @Value("${service.medium.suppression.greaterThan.hour:5}")
    int mediumSuppressionGreaterThanHour;
    @Value("${service.high.persistence.hour:5}")
    int highPersistenceHour;
    @Value("${service.high.suppression.hour:10}")
    int highSuppressionHour;
    @Value("${service.high.persistence.greaterThan.hour:2}")
    int highPersistenceGreaterThanHour;
    @Value("${service.high.suppression.greaterThan.hour:5}")
    int highSuppressionGreaterThanHour;
    @Value("${service.high.suppression.persistence.enable:true}")
    boolean highEnable;
    @Value("${service.medium.suppression.persistence.enable:true}")
    boolean mediumEnable;
    @Value("${service.low.suppression.persistence.enable:true}")
    boolean lowEnable;
    @Value("${service.max.data.breaks:30}")
    int maxDataBreaks;
    @Value("${service.closing.window:3}")
    int closingWindow;

    public GetServiceAnomalyConfigBL(ServiceConfigurationDao serviceConfigurationDao, ClientValidationUtils clientValidationUtils,
                                     ServerValidationUtils serverValidationUtils) {
        this.serviceConfigurationDao = serviceConfigurationDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(Object... arguments) throws ClientException {
        try {
            String authKey = (String) arguments[0];
            clientValidationUtils.authKeyValidation(authKey);

            String accountIdentifier = (String) arguments[1];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            String serviceIdentifier = (String) arguments[2];
            clientValidationUtils.serviceIdentifierValidation(serviceIdentifier);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.AUTH_KEY, authKey);
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            requestParamsMap.put(Constants.SERVICE_IDENTIFIER, serviceIdentifier);

            return UtilityBean.<String>builder()
                    .requestParams(requestParamsMap)
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        //Entry-Exit logging is handled in LoggingAspect
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        String userId = serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        UserAccessDetails userAccessDetails = serverValidationUtils.userAccessDetailsValidation(userId, accountIdentifier);
        String serviceIdentifier = utilityBean.getRequestParams().get(Constants.SERVICE_IDENTIFIER);

        Service service = serverValidationUtils.serviceValidation(userId, accountIdentifier, serviceIdentifier, userAccessDetails);

        HashMap<String,Object> metadata = new HashMap<>();
        metadata.put(Constants.ACCOUNT, account);
        metadata.put(Constants.SERVICE, service);

        return UtilityBean.<String>builder()
                .requestParams(utilityBean.getRequestParams())
                .metadata(Map.of(Constants.USER_ID, userId))
                .metadata(metadata)
                .build();
    }

    @Transactional(rollbackFor = {Exception.class})
    public Map<String, ServiceSuppPersistenceConfigPojo> process(UtilityBean<String> utilityBean) throws DataProcessingException {
        //Entry-Exit logging is handled in LoggingAspect

        Map<String, Object> metadata = utilityBean.getMetadata();
        Account account = (Account) metadata.get(Constants.ACCOUNT);
        Service service = (Service) metadata.get(Constants.SERVICE);

        int accountId = account.getId();
        int serviceId = service.getId();
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID);

        log.info("Fetching service configuration for accountId: {}, serviceId: {}, userId: {}", accountId, serviceId, userId);
        List<ServiceSuppPersistenceConfigurationBean> serviceBeanList = null;
        try {
            serviceBeanList = serviceConfigurationDao.getServiceConfiguration(accountId, serviceId);

            if (null == serviceBeanList || serviceBeanList.isEmpty()) {
                serviceBeanList = createDefaultServiceConfiguration(serviceId, accountId, userId);
                serviceConfigurationDao.addServiceConfiguration(serviceBeanList);

            }
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        Map<String, ServiceSuppPersistenceConfigPojo> serviceConfigDetails = new HashMap<>();

        for (ServiceSuppPersistenceConfigurationBean s : serviceBeanList) {
            String collectionInterval = (s.getStartCollectionInterval() == serviceMinCollectionIntervalHour) ?
                    Constants.OPERATOR_LESS_THAN : Constants.OPERATOR_GREATER_THAN;
            serviceConfigDetails.put(collectionInterval, ServiceSuppPersistenceConfigPojo.builder()
                    .serviceConfigId(s.getId())
                    .lowPersistence(s.getLowPersistence())
                    .lowSuppression(s.getLowSuppression())
                    .highPersistence(s.getHighPersistence())
                    .highSuppression(s.getHighSuppression())
                    .mediumPersistence(s.getMediumPersistence())
                    .mediumSuppression(s.getMediumSuppression())
                    .highEnable(s.isHighEnable())
                    .mediumEnable(s.isMediumEnable())
                    .lowEnable(s.isLowEnable())
                    .closingWindow(s.getClosingWindow())
                    .maxDataBreaks(s.getMaxDataBreaks())
                    .build());
        }

        return serviceConfigDetails;
    }

    public List<ServiceSuppPersistenceConfigurationBean> createDefaultServiceConfiguration(int serviceId, int accountId, String userId) {
        log.info("Creating default service configuration for serviceId: {}, accountId: {}, userId: {}", serviceId, accountId, userId);

        return List.of(ServiceSuppPersistenceConfigurationBean.builder()
                .serviceId(serviceId)
                .accountId(accountId)
                .userDetailsId(userId)
                .createdTime(String.valueOf(DateTimeUtil.getCurrentTimestampInGMT()))
                .updatedTime(String.valueOf(DateTimeUtil.getCurrentTimestampInGMT()))
                .startCollectionInterval(serviceMinCollectionIntervalHour)
                .endCollectionInterval(serviceMaxCollectionIntervalHour)
                .lowPersistence(lowPersistenceHour)
                .lowSuppression(lowSuppressionHour)
                .mediumPersistence(mediumPersistenceHour)
                .mediumSuppression(mediumSuppressionHour)
                .highPersistence(highPersistenceHour)
                .highSuppression(highSuppressionHour)
                .highEnable(highEnable)
                .mediumEnable(mediumEnable)
                .lowEnable(lowEnable)
                .closingWindow(closingWindow)
                .maxDataBreaks(maxDataBreaks)
                .build(),

                ServiceSuppPersistenceConfigurationBean.builder()
                .serviceId(serviceId)
                .accountId(accountId)
                .userDetailsId(userId)
                .createdTime(String.valueOf(DateTimeUtil.getCurrentTimestampInGMT()))
                .updatedTime(String.valueOf(DateTimeUtil.getCurrentTimestampInGMT()))
                .startCollectionInterval(serviceMinCollectionIntervalGreaterThanHour)
                .endCollectionInterval(serviceMaxCollectionIntervalGreaterThanHour)
                .lowPersistence(lowPersistenceGreaterThanHour)
                .lowSuppression(lowSuppressionGreaterThanHour)
                .mediumPersistence(mediumPersistenceGreaterThanHour)
                .mediumSuppression(mediumSuppressionGreaterThanHour)
                .highPersistence(highPersistenceGreaterThanHour)
                .highSuppression(highSuppressionGreaterThanHour)
                .highEnable(highEnable)
                .mediumEnable(mediumEnable)
                .lowEnable(lowEnable)
                .closingWindow(closingWindow)
                .maxDataBreaks(maxDataBreaks)
                .build());
    }
}