package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.enums.AttributeSelectionType;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ServicePageAttributePojo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class GetServicePageAttributesBL implements BusinessLogic<Integer, Integer, List<ServicePageAttributePojo>> {

    private final AccountsDao accountDao;
    private final ControllerDao controllerDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetServicePageAttributesBL(AccountsDao accountDao, ControllerDao controllerDao,
                                      ClientValidationUtils clientValidationUtils,
                                      ServerValidationUtils serverValidationUtils) {
        this.accountDao = accountDao;
        this.controllerDao = controllerDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<Integer> clientValidation(Object... arguments) throws ClientException {
        try {
            String accountIdentifier = (String) arguments[0];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);
            if (((String) arguments[1]).isEmpty()) {
                log.error("Invalid serviceId. Reason: It is either null or empty.");
                throw new ClientException("Invalid serviceId");
            }
            int serviceId;
            try {
                serviceId = Integer.parseInt((String) arguments[1]);
            } catch (NumberFormatException e) {
                log.error("Invalid serviceId [{}]. Reason: It is not a valid integer.", (String) arguments[1]);
                throw new ClientException("Invalid serviceId");
            }
            if (serviceId <= 0) {
                log.error("Invalid serviceId [{}]. Reason: It is not a valid integer.", serviceId);
                throw new ClientException("Invalid serviceId");
            }

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);

            return UtilityBean.<Integer>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(serviceId)
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    @Override
    public Integer serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE,accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }
        ControllerBean service;
        try {
            service = controllerDao.getServiceById(utilityBean.getPojoObject(), account.getId());
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
        if (null == service) {
            String message = String.format("ServiceId [%d] is unavailable for account [%s]", utilityBean.getPojoObject(), utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
            log.error(message);
            throw new ServerException(message);
        }
        return service.getId();
    }

    @Override
    public List<ServicePageAttributePojo> process(Integer serviceId) throws DataProcessingException {
        List<ServicePageAttributePojo> attributes = new ArrayList<>();

        int id = 1;
        attributes.add(new ServicePageAttributePojo(id++, null, "Name", "name", "name",
                null, null, null, new ServicePageAttributePojo.AttributeProperties(1, 128,
                null, "^[a-zA-Z0-9._-]+$", AttributeSelectionType.TextBox, 1, 0,
                0, 1, null)));

        attributes.add(new ServicePageAttributePojo(id++, null, "Identifier", "identifier", "identifier",
                null, null, null, new ServicePageAttributePojo
                .AttributeProperties(1, 128, null, "^[a-zA-Z0-9._-]+$", AttributeSelectionType.TextBox,
                0, serviceId == 0 ? 0 : 1, 0, 1, null)));

        attributes.add(new ServicePageAttributePojo(id++, null, "Application(s)", "appIdentifiers",
                "application.identifier", "name", "identifier",
                "accounts/{accountIdentifier}/applications", new ServicePageAttributePojo.AttributeProperties(0,
                0, null, "", AttributeSelectionType.Dropdown, 1, 0, 1,
                0, null)));

        try {
            attributes.add(new ServicePageAttributePojo(id++, null, "Layer", "layer", "layer",
                    null, null, null, new ServicePageAttributePojo.AttributeProperties(1, 128,
                    controllerDao.getLayers(Constants.SERVICES_LAYER_TYPE), "^[a-zA-Z0-9._-]+$", AttributeSelectionType.Dropdown,
                    1, 0, 0, 0, null)));
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        attributes.add(new ServicePageAttributePojo(id++, null, "Timezone", "timezone", "timezone",
                "timeZoneId", "timeZoneId", "/timezones", new ServicePageAttributePojo.AttributeProperties(0,
                0, null, "", AttributeSelectionType.Dropdown, 1, 0, 0,
                0, null)));

        if (serviceId == 0) {
            Map<String, String> options = new HashMap<>();
            options.put("0", "No");
            options.put("1", "Yes");
            attributes.add(new ServicePageAttributePojo(id, 0, "Mark as Entry Point", "isEntryPointService",
                    "isEntryPoint", null, null, null, new ServicePageAttributePojo.
                    AttributeProperties(0, 0, options, "", AttributeSelectionType.Switch, 0,
                    0, 0, 0, null)));
        }

        return attributes;
    }
}