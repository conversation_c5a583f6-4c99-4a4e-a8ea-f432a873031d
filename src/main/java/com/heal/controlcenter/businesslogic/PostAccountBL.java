package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.AnomalyConfiguration;
import com.heal.configuration.pojos.EscalationSettings;
import com.heal.configuration.pojos.TenantDetails;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.TimezoneBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.NotificationRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.KeyPair;
import java.util.*;

@Slf4j
@Service
public class PostAccountBL implements BusinessLogic<Account, UtilityBean<Account>, Account> {

    private final AccountsDao accountsDao;
    private final MasterDataDao masterDataDao;
    private final TagsDao tagsDao;
    private final AccountRepo accountRepo;
    private final NotificationRepo notificationRepo;
    private final GetNotificationSettingsBL getNotificationSettingsBL;
    private final ClientValidationUtils clientValidationUtils;
    private final NotificationsDao notificationsDao;
    private final TenantsDao tenantsDao;

    @Value("${opensearch.nodes.txn}")
    private String txnNode;
    @Value("${opensearch.nodes.kpi}")
    private String kpiNode;
    @Value("${opensearch.nodes.misc}")
    private String miscNode;
    @Value("${opensearch.nodes.eum}")
    private String eumNode;
    @Value("${opensearch.nodes.jim}")
    private String jimNode;
    @Value("${opensearch.nodes.jaeger}")
    private String jaegerNode;
    @Value("${opensearch.nodes}")
    private String nozoneNode;
    //username
    @Value("${opensearch.username}")
    private String nozoneUsername;
    @Value("${opensearch.username.txn}")
    private String txnUsername;
    @Value("${opensearch.username.kpi}")
    private String kpiUsername;
    @Value("${opensearch.username.misc}")
    private String miscUsername;
    @Value("${opensearch.username.eum}")
    private String eumUsername;
    @Value("${opensearch.username.jim}")
    private String jimUsername;
    @Value("${opensearch.username}")
    private String jaegerUsername;
    //password
    @Value("${opensearch.password.encrypted}")
    private String nozonePassword;
    @Value("${opensearch.password.encrypted.txn}")
    private String txnPassword;
    @Value("${opensearch.password.encrypted.kpi}")
    private String kpiPassword;
    @Value("${opensearch.password.encrypted.misc}")
    private String miscPassword;
    @Value("${opensearch.password.encrypted.eum}")
    private String eumPassword;
    @Value("${opensearch.password.encrypted.jim}")
    private String jimPassword;
    @Value("${opensearch.password.encrypted}")
    private String jaegerPassword;

    public PostAccountBL(AccountsDao accountsDao, MasterDataDao masterDataDao, TagsDao tagsDao, AccountRepo accountRepo,
                         NotificationRepo notificationRepo, GetNotificationSettingsBL getNotificationSettingsBL,
                         ClientValidationUtils clientValidationUtils, NotificationsDao notificationsDao, TenantsDao tenantsDao) {
        this.accountsDao = accountsDao;
        this.masterDataDao = masterDataDao;
        this.tagsDao = tagsDao;
        this.accountRepo = accountRepo;
        this.notificationRepo = notificationRepo;
        this.getNotificationSettingsBL = getNotificationSettingsBL;
        this.clientValidationUtils = clientValidationUtils;
        this.notificationsDao = notificationsDao;
        this.tenantsDao = tenantsDao;
    }

    /**
     * Validates the client-side input for account creation.
     * Checks account identifier format and required fields in the request body.
     *
     * @param arguments   Account object from the request payload and basicUserDetails.
     * @throws ClientException if validation fails.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<Account> clientValidation(Object... arguments) throws ClientException {
        try {
            Account account = (Account) arguments[0];
            Map<String, String> error = account.validate();
            if (!error.isEmpty()) {
                String err = error.toString();
                log.error(err);
                throw new ClientException(err);
            }

            clientValidationUtils.accountIdentifierValidation(account.getIdentifier());

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, account.getIdentifier());

            return UtilityBean.<Account>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(account)
                    .basicUserDetails(basicUserDetails)
                    .metadata(new HashMap<>())
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Validates server-side constraints for account creation.
     * Ensures the account identifier is unique and not already in use.
     *
     * @param utilityBean UtilityBean containing request parameters and account object.
     * @return UtilityBean enriched with metadata.
     * @throws ServerException if the account identifier already exists.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<Account> serverValidation(UtilityBean<Account> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getPojoObject().getIdentifier();
        String accountName= utilityBean.getPojoObject().getAccountName();

        if (accountsDao.existsByIdentifier(accountIdentifier.trim())) {
            String msg = "Account identifier already in use, kindly use another identifier.";
            log.error("{} -> {}", msg, accountIdentifier);
            throw new ServerException(msg);
        }
        try {
            log.debug("[process] Checking if account name already exists: {}", accountName);
            int id = accountsDao.getAccountByName(accountName);
            if (id > 0) {
                String msg = String.format(UIMessages.ACCOUNT_NAME_ALREADY_EXISTS, utilityBean.getPojoObject().getAccountName());
                log.error(msg);
                throw new ServerException(msg);
            }
        } catch (HealControlCenterException hce) {
            log.error("Error checking account name existence: {}", hce.getMessage(), hce);
            throw new ServerException("");
        }
        return UtilityBean.<Account>builder()
                .pojoObject(utilityBean.getPojoObject())
                .requestParams(utilityBean.getRequestParams())
                .metadata(utilityBean.getMetadata())
                .build();
    }

    /**
     * Processes the creation of a new account.
     * Handles DB persistence, key generation, tag mapping, and Redis updates.
     *
     * <p>This method performs the following operations in sequence:
     * <ul>
     *     <li>Validates that no other account with the same name exists.</li>
     *     <li>Generates cryptographic key pairs (private and public keys) for the account.</li>
     *     <li>Constructs and persists the account to the database with audit metadata.</li>
     *     <li>Inserts associated anomaly configuration values (e.g., severity levels, thresholds).</li>
     *     <li>Fetches and validates the timezone tag via {@code TagsDao} and {@code MasterDataDao}.</li>
     *     <li>Persists the timezone tag mapping in the tag_mapping table.</li>
     *     <li>Builds a {@code com.heal.configuration.pojos.Account} object and stores it in Redis.</li>
     *     <li>Updates the in-memory account list within {@code AccountRepo} with the newly created account.</li>
     *     <li>Inserts default notification settings into the database for the new account.</li>
     *     <li>Fetches escalation settings from the database and saves them to Redis via {@code NotificationRepo}.</li>
     * </ul>
     *
     * <p>If any failure occurs during these operations—whether due to validation, database, or Redis interaction—
     * a {@link DataProcessingException} or {@link HealControlCenterException} is thrown as appropriate.
     *
     * @param utilityBean A validated {@link UtilityBean} containing metadata, request parameters, and the account object.
     * @return A new, empty {@link Account} object as a placeholder (the actual created account is stored in Redis and DB).
     * @throws DataProcessingException If any unrecoverable error occurs during processing.
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    @LogExecutionAnnotation
    public Account process(UtilityBean<Account> utilityBean) throws DataProcessingException {
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
        Account accountDetails = utilityBean.getPojoObject();
        try {
            log.debug("[process] Generating key pair for account");
            KeyPair keyPair = KeyGenerator.generateKeys();
            String privateKey = KeyGenerator.getPrivateKey(null, keyPair);
            String publicKey = KeyGenerator.getPublicKey(null, keyPair);

            String createdTime;
            try {
                createdTime = Objects.requireNonNull(DateTimeUtil.getCurrentTimestampInGMT()).toString();
            } catch (NullPointerException e) {
                log.error("Error while fetching currentTimestamp in GMT");
                throw new DataProcessingException("Error while fetching currentTime in GMT");
            }

            log.debug("[process] Inserting account into DB");
            AccountBean accountBean = new AccountBean();
            accountBean.setName(accountDetails.getAccountName());
            accountBean.setLastModifiedBy(userId);
            accountBean.setIdentifier(accountDetails.getIdentifier());
            accountBean.setPublicKey(publicKey);
            accountBean.setPrivateKey(privateKey);
            accountBean.setCreatedTime(createdTime);
            accountBean.setUpdatedTime(createdTime);
            accountBean.setStatus(utilityBean.getPojoObject().getStatus());

            int id = accountsDao.addAccount(accountBean);
            log.info("Account [{}] created successfully with ID [{}].", accountDetails.getAccountName(), id);

            // Tenant mapping logic
            log.debug("[process] Handling tenant mapping for account ID [{}]", id);
            handleTenantMapping(accountBean, id);

            log.debug("[process] Preparing anomaly configuration bean");
            AnomalyConfiguration accountAnomalyConfigurationBean = new AnomalyConfiguration();
            accountAnomalyConfigurationBean.setAccountId(id);
            accountAnomalyConfigurationBean.setLastModifiedBy(userId);
            accountAnomalyConfigurationBean.setCreatedTime(createdTime);
            accountAnomalyConfigurationBean.setUpdatedTime(createdTime);
            accountAnomalyConfigurationBean.setLowEnable(utilityBean.getPojoObject().getThresholdSeverity().isLow());
            accountAnomalyConfigurationBean.setMediumEnable(utilityBean.getPojoObject().getThresholdSeverity().isWarning());
            accountAnomalyConfigurationBean.setHighEnable(utilityBean.getPojoObject().getThresholdSeverity().isCritical());
            accountAnomalyConfigurationBean.setClosingWindow(utilityBean.getPojoObject().getClosingWindow());
            accountAnomalyConfigurationBean.setMaxDataBreaks(utilityBean.getPojoObject().getMaxDataBreaks());

            log.debug("[process] Inserting anomaly configurations for account ID [{}]", id);
            accountsDao.insertAccountAnomalyConfigurations(id, accountAnomalyConfigurationBean, userId);
            log.info("Account anomaly configurations inserted for account ID [{}].", id);

            TimezoneBean timezone;
            List<Tags> tagList = accountDetails.getTags();

            String timeZoneId = tagList.get(0).getIdentifier();
            try {
                timezone = masterDataDao.getTimeZoneWithId(timeZoneId);
                log.debug("Timezone fetched successfully for ID [{}].", timeZoneId);
            } catch (HealControlCenterException e) {
                log.error("Invalid timezone [{}]", timeZoneId);
                throw new DataProcessingException(e.getMessage());
            }

            //Insert tag_mapping
            try {
                log.debug("[process] Adding timezone tag mapping for account ID [{}]", id);
                TagMappingDetails timeZoneTag = TagMappingDetails.builder()
                        .tagId(tagsDao.getTagDetailsByName(Constants.TIME_ZONE_TAG).getId())
                        .tagKey(String.valueOf(timezone.getId()))
                        .tagValue(String.valueOf(timezone.getOffset()))
                        .objectId(id)
                        .objectRefTable(Constants.ACCOUNT)
                        .accountId(id)
                        .userDetailsId(userId)
                        .createdTime(createdTime)
                        .updatedTime(createdTime)
                        .build();
                int mappingId = tagsDao.addTagMappingDetails(timeZoneTag);
                if (mappingId <= 0) {
                    log.error("Timezone mapping to application [{}] failed", id);
                    throw new DataProcessingException("Timezone mapping to application failed");
                }
                log.info("Timezone mapping successfully added for account ID [{}] with mapping ID [{}].", id, mappingId);
            } catch (HealControlCenterException e) {
                log.error("Error while adding timezone tag mapping for account ID [{}]: {}", id, e.getMessage());
                throw new DataProcessingException("Error while adding timezone tag mapping: " + e.getMessage());
            }

            Optional<Tags> timeZoneTag = utilityBean.getPojoObject().getTags().stream().filter(tag -> tag.getName().equalsIgnoreCase(Constants.TIME_ZONE_TAG))
                    .findAny();

            com.heal.configuration.pojos.Tags tags = new com.heal.configuration.pojos.Tags();
            String tagName = tagList.get(0).getName();//Ex:Timezone
            tags.setKey(timezone.getTimeZoneId());
            tags.setValue(String.valueOf(timezone.getOffset()));
            tags.setType(tagName);

            //Account Repo saving to Redis
            log.debug("[process] Preparing anomaly configuration for Redis");
            ThresholdSeverity sourceSeverity = utilityBean.getPojoObject().getThresholdSeverity();
            AnomalyConfiguration anomalyConfiguration = new AnomalyConfiguration();
            anomalyConfiguration.setLowEnable(sourceSeverity.isLow());
            anomalyConfiguration.setMediumEnable(sourceSeverity.isWarning());
            anomalyConfiguration.setHighEnable(sourceSeverity.isCritical());
            anomalyConfiguration.setClosingWindow(utilityBean.getPojoObject().getClosingWindow());
            anomalyConfiguration.setMaxDataBreaks(utilityBean.getPojoObject().getMaxDataBreaks());
            anomalyConfiguration.setLastModifiedBy(userId);
            anomalyConfiguration.setCreatedTime(createdTime);
            anomalyConfiguration.setUpdatedTime(createdTime);
            anomalyConfiguration.setAccountId(id);

            log.debug("[process] Attempting to get default tenant ID from attributes.");
            int defaultTenantId = tenantsDao.getDefaultTenantIdFromAttributes();
            log.debug("[process] Retrieved defaultTenantId: {}", defaultTenantId);

            log.debug("[process] Attempting to get tenant details for defaultTenantId: {}", defaultTenantId);
            TenantDetails tenantDetails = tenantsDao.getTenantDetailsById(defaultTenantId);
            if (tenantDetails != null) {
                log.debug("[process] Retrieved tenant details for ID [{}].", defaultTenantId);
            } else {
                log.warn("[process] TenantDetails not found for defaultTenantId: {}", defaultTenantId);
            }

            log.debug("[process] Saving new account to Redis");
            com.heal.configuration.pojos.Account newAccount = com.heal.configuration.pojos.Account.builder()
                    .accountId(id)
                    .id(id)
                    .name(accountBean.getName())
                    .createdTime(String.valueOf(accountBean.getCreatedTime()))
                    .updatedTime(String.valueOf(accountBean.getUpdatedTime()))
                    .status(accountBean.getStatus())
                    .privateKey(accountBean.getPrivateKey())
                    .publicKey(accountBean.getPublicKey())
                    .identifier(accountBean.getIdentifier())
                    .tags(List.of(tags))
                    .timezone(timeZoneTag.map(Tags::getIdentifier).orElse(null))
                    .lastModifiedBy(userId)
                    .tenantDetails(tenantDetails)
                    .anomalyConfiguration(anomalyConfiguration)
                    .build();

            accountRepo.updateAccount(utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER), newAccount);
            log.info("Account details updated in Redis for identifier [{}].", utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));

            List<com.heal.configuration.pojos.Account> existingAccountList = accountRepo.getAccounts();
            if (existingAccountList.isEmpty()) {
                existingAccountList = new ArrayList<>();
                log.debug("No existing accounts found in Redis, initializing new list.");
            }
            existingAccountList.add(newAccount);
            accountRepo.updateAccounts(existingAccountList);
            log.info("Updated list of accounts in Redis with new account ID [{}].", id);

            //Insert notification_settings
            log.debug("[process] Adding default notification settings for account ID [{}]", id);
            getNotificationSettingsBL.addDefaultNotificationSettings(id, userId);
            log.info("Default notification settings added for account ID [{}].", id);

            //Notification Repo saving to Redis
            log.debug("[process] Updating notification settings in Redis for account identifier [{}]", utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
            List<EscalationSettings> escalationSetting = notificationsDao.getEscalationSettingsForAccount(id);
            notificationRepo.updateNotificationSettingsInRedis(utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER), escalationSetting);
            log.info("Notification settings updated in Redis for account identifier [{}].", utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));

            log.info("[process] Account creation completed successfully for account name [{}]", accountDetails.getAccountName());
            return new Account();

        } catch (Exception e) {
            log.error("Error while creating account: {}", accountDetails.getAccountName(), e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    /**
     * Handles tenant mapping and OpenSearch configuration setup for a newly created account.
     *
     * <p>This method manages two key scenarios:</p>
     *
     * <ul>
     *   <li><b>Scenario 1: No tenants exist in the system</b>
     *     <ul>
     *       <li>Inserts a default tenant into the <code>tenant_details</code> table.</li>
     *       <li>Persists the tenant ID into the <code>a1_installation_attributes</code> table.</li>
     *       <li>Fetches zone IDs and names (e.g., TXN, KPI, MISC) from the <code>mst_sub_type</code> table.</li>
     *       <li>Maps each zone to a corresponding OpenSearch node, username, and password from application properties.</li>
     *       <li>Inserts OpenSearch configuration into the <code>opensearch_tenant_mapping</code> table for each valid zone.</li>
     *       <li>If a zone name is unrecognized, it is skipped with a warning log.</li>
     *       <li>Finally, maps the newly created account to this default tenant in <code>account_tenant_details</code>.</li>
     *     </ul>
     *   </li>
     *
     *   <li><b>Scenario 2: Tenants already exist in the system</b>
     *     <ul>
     *       <li>Retrieves the default tenant ID from the <code>a1_installation_attributes</code> table.</li>
     *       <li>Maps the newly created account to the retrieved default tenant in <code>account_tenant_details</code>.</li>
     *       <li>If the default tenant ID is missing or invalid, a fallback attempt is made using tenant count check.</li>
     *       <li>If the fallback fails, an exception is thrown to indicate tenant mapping failure.</li>
     *     </ul>
     *   </li>
     * </ul>
     *
     * <p>This method ensures proper linkage between accounts, tenants, and OpenSearch clusters using secure, zone-based configuration.</p>
     *
     * @param account          The {@link AccountBean} containing metadata such as last modified user.
     * @param createdAccountId The ID of the newly created account to be mapped.
     * @throws HealControlCenterException If any failure occurs during tenant or OpenSearch mapping setup.
     */
    private void handleTenantMapping(AccountBean account, int createdAccountId) throws HealControlCenterException {
        try {
            int tenantCount = tenantsDao.getTenantCount();
            log.debug("Current tenant count: {}", tenantCount);

            if (tenantCount == 0) {
                log.info("Scenario 1: No tenants exist. Inserting default tenant and setting up mappings.");

                int newTenantId = tenantsDao.insertTenant(
                        Constants.DEFAULT_TENANT_NAME,
                        Constants.DEFAULT_TENANT_IDENTIFIER,
                        Constants.DEFAULT_TENANT_STATUS,
                        account.getLastModifiedBy()
                );
                log.info("Default tenant [{}] inserted with ID [{}].", Constants.DEFAULT_TENANT_NAME, newTenantId);

                tenantsDao.insertInstallationAttribute(
                        Constants.DEFAULT_TENANT_ATTRIBUTE,
                        String.valueOf(newTenantId),
                        account.getLastModifiedBy()
                );
                log.info("Installation attribute '{}' set to default tenant ID [{}].", Constants.DEFAULT_TENANT_ATTRIBUTE, newTenantId);

                Map<Integer, String> zoneMap = tenantsDao.getDefaultZonesWithNames(); // zoneId -> zoneName
                log.debug("Fetched default zone ID to name mapping for OpenSearch: {}", zoneMap);

                for (Map.Entry<Integer, String> entry : zoneMap.entrySet()) {
                    int zoneId = entry.getKey();
                    String zoneName = entry.getValue();
                    String nodeAddress;
                    String username;
                    String password;

                    switch (zoneName.toUpperCase()) {
                        case "TXN" -> {
                            nodeAddress = txnNode;
                            username = txnUsername;
                            password = txnPassword;
                        }
                        case "KPI" -> {
                            nodeAddress = kpiNode;
                            username = kpiUsername;
                            password = kpiPassword;
                        }
                        case "MISC" -> {
                            nodeAddress = miscNode;
                            username = miscUsername;
                            password = miscPassword;
                        }
                        case "EUM" -> {
                            nodeAddress = eumNode;
                            username = eumUsername;
                            password = eumPassword;
                        }
                        case "JIM" -> {
                            nodeAddress = jimNode;
                            username = jimUsername;
                            password = jimPassword;
                        }
                        case "JAEGER" -> {
                            nodeAddress = jaegerNode;
                            username = jaegerUsername;
                            password = jaegerPassword;
                        }
                        case "NO-ZONE" -> {
                            nodeAddress = nozoneNode;
                            username = nozoneUsername;
                            password = nozonePassword;
                        }
                        default -> {
                            log.warn("Unknown zone name [{}], skipping OpenSearch mapping.", zoneName);
                            continue;
                        }
                    }

                    tenantsDao.insertOpenSearchTenantMapping(
                            newTenantId,
                            Constants.CLUSTER_NAME,
                            nodeAddress,
                            Constants.PROTOCOL,
                            zoneId,
                            Constants.PER_ROUTE_CONNECTIONS,
                            Constants.MAX_CONNECTIONS,
                            Constants.CONNECTION_TIMEOUT_MS,
                            Constants.SOCKET_TIMEOUT_MS,
                            Constants.KEEP_ALIVE_SECS,
                            username,
                            password,
                            account.getLastModifiedBy()
                    );
                    log.info("OpenSearch tenant mapping inserted for tenant ID [{}] and zone ID [{}] ({}) with node [{}].",
                            newTenantId, zoneId, zoneName, nodeAddress);
                }

                tenantsDao.insertAccountTenantMapping(createdAccountId, newTenantId, account.getLastModifiedBy());
                log.info("Account ID [{}] mapped to new tenant ID [{}].", createdAccountId, newTenantId);

            } else {
                log.info("Scenario 2: Tenants already exist. Mapping account to existing default tenant.");

                int tenantId;
                try {
                    tenantId = tenantsDao.getDefaultTenantIdFromAttributes();
                    log.debug("Fetched default tenant ID from attributes: {}.", tenantId);
                } catch (Exception ex) {
                    log.warn("DefaultTenantId not found in attributes, fallback logic triggered. Error: {}", ex.getMessage());
                    tenantId = tenantsDao.getTenantCount() > 0 ? tenantsDao.getDefaultTenantIdFromAttributes() : 0;
                    if (tenantId == 0) {
                        log.error("Failed to determine default tenant ID for account ID [{}].", createdAccountId);
                        throw new HealControlCenterException("Unable to determine default tenant for mapping.");
                    }
                }

                tenantsDao.insertAccountTenantMapping(createdAccountId, tenantId, account.getLastModifiedBy());
                log.info("Account ID [{}] mapped to existing tenant ID [{}].", createdAccountId, tenantId);
            }

        } catch (Exception e) {
            log.error("Tenant mapping failed for accountId [{}] and accountName [{}]. Error: {}", createdAccountId, account.getName(), e.getMessage(), e);
            throw new HealControlCenterException("Tenant mapping failure: " + e.getMessage());
        }
    }
}