package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.IdBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> - 18-10-2021
 */
@Service
@Slf4j
public class UserRoleBL implements BusinessLogic<String, String, List<IdBean>> {

    private final UserDao userRolesAndProfilesDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public UserRoleBL(UserDao userRolesAndProfilesDao, ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.userRolesAndProfilesDao = userRolesAndProfilesDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    /**
     * Validates the client-side request parameters, such as authKey.
     * Constructs a UtilityBean containing the validated request parameters.
     * Throws ClientException if validation fails.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(Object... arguments) throws ClientException {
        try {
            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            return UtilityBean.<String>builder()
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Performs server-side validation using the provided authKey.
     * Ensures the authKey is valid in the server context.
     * Throws ServerException if validation fails.
     */
    @Override
    public String serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        return null;
    }

    @Override
    public List<IdBean> process(String bean) throws DataProcessingException {
        List<IdBean> data;
        try {
            log.debug("getting role details");
            data = userRolesAndProfilesDao.getRoles();
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        if (Objects.isNull(data) || data.isEmpty()) {
            throw new DataProcessingException("User roles information unavailable");
        }
        log.info("User roles information fetched successfully");
        return data;
    }
}