package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.ProducerKpiValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ProducersDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ProducerKpiMappingPojo;
import com.heal.controlcenter.pojo.ProducerMappedKpiDetailsPojo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Slf4j
@Component
public class GetProducerMappedKpisBL implements BusinessLogic<Void, UtilityBean<ProducerKpiValidationBean>, Page<ProducerKpiMappingPojo>> {

    private final ProducersDao producersDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetProducerMappedKpisBL(ProducersDao producersDao,
                                   ClientValidationUtils clientValidationUtils,
                                   ServerValidationUtils serverValidationUtils) {
        this.producersDao = producersDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    /**
     * Validates the client request for retrieving producer mapped KPIs.
     *
     * @param arguments An array of arguments: accountIdentifier (String), producerId (String), userDetails (BasicUserDetails).
     * @return A {@link UtilityBean} containing the validated request parameters.
     * @throws ClientException If any validation fails.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<Void> clientValidation(Object... arguments) throws ClientException {
        log.info("Client validation started for GetProducerMappedKpisBL.");
        try {
            String accountIdentifier = (String) arguments[0];
            String producerIdStr = (String) arguments[1];
            BasicUserDetails userDetails = (BasicUserDetails) arguments[2];

            clientValidationUtils.accountIdentifierValidation(accountIdentifier);
            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            requestParamsMap.put("producerId", producerIdStr);

            log.info("Client validation successful for accountIdentifier: {} and producerId: {}", accountIdentifier, producerIdStr);
            return UtilityBean.<Void>builder()
                    .requestParams(requestParamsMap)
                    .metadata(new HashMap<>())
                    .basicUserDetails(userDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Performs server-side validation for retrieving producer mapped KPIs.
     *
     * @param utilityBean A {@link UtilityBean} containing request parameters.
     * @return An enriched {@link UtilityBean} with extracted metadata (e.g., account, producerId).
     * @throws ServerException If validation fails or account/producer is not found.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<ProducerKpiValidationBean> serverValidation(UtilityBean<Void> utilityBean) throws ServerException {
        log.info("Server validation started for GetProducerMappedKpisBL.");
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        String producerIdStr = utilityBean.getRequestParams().get("producerId");
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        Integer producerId;
        try {
            producerId = Integer.parseInt(producerIdStr);
        } catch (NumberFormatException e) {
            log.error("Invalid producerId format during server validation: {}", producerIdStr, e);
            throw new ServerException("Invalid producerId");
        }
        ProducerKpiValidationBean validationBean = ProducerKpiValidationBean.builder()
                .account(account)
                .producerId(producerId)
                .build();
        log.info("Server validation successful for account: {} and producerId: {}", accountIdentifier, producerId);
        return UtilityBean.<ProducerKpiValidationBean>builder()
                .pojoObject(validationBean)
                .metadata(utilityBean.getMetadata())
                .requestParams(utilityBean.getRequestParams())
                .pageable(utilityBean.getPageable())
                .build();
    }

    /**
     * Executes the business logic to retrieve a paginated list of producer mapped KPIs.
     *
     * @param utilityBean A validated {@link UtilityBean} containing metadata and request parameters.
     * @return A {@link Page} of producer mapped KPIs.
     * @throws DataProcessingException If any error occurs during KPI retrieval or processing.
     */
    @Override
    @LogExecutionAnnotation
    public Page<ProducerKpiMappingPojo> process(UtilityBean<ProducerKpiValidationBean> utilityBean) throws DataProcessingException {
        Pageable pageable = utilityBean.getPageable();
        ProducerKpiValidationBean bean = utilityBean.getPojoObject();
        log.info("Processing request for producer mapped KPIs for account: {} and producerId: {}", bean.getAccount().getIdentifier(), bean.getProducerId());
        try {
            PaginationUtils.validatePagination(pageable);
            Page<ProducerMappedKpiDetailsPojo> mappedKpisPage = producersDao.getProducerMappedKpis(bean.getAccount().getId(), bean.getProducerId(), pageable);

            if (mappedKpisPage == null || !mappedKpisPage.hasContent()) {
                log.info("No mapped KPIs found for producerId: {}", bean.getProducerId());
                return Page.empty(pageable);
            }
            String producerName = producersDao.getProducerNameById(bean.getProducerId());
            ProducerKpiMappingPojo resultPojo = ProducerKpiMappingPojo.builder()
                    .producerId(bean.getProducerId())
                    .producerName(producerName)
                    .mappedKpis(mappedKpisPage.getContent())
                    .build();

            log.info("Successfully processed producer mapped KPIs for producerId: {}. Returning page with {} items out of total {}", bean.getProducerId(), mappedKpisPage.getNumberOfElements(), mappedKpisPage.getTotalElements());
            return new org.springframework.data.domain.PageImpl<>(java.util.Collections.singletonList(resultPojo), pageable, mappedKpisPage.getTotalElements());
        } catch (Exception e) {
            log.error("Error while fetching mapped KPIs for producerId: {}", bean.getProducerId(), e);
            throw new DataProcessingException("Error while fetching mapped KPIs for producerId: " + bean.getProducerId());
        }
    }
}
