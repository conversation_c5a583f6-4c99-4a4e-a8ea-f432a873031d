package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.AccountKPIKey;
import com.heal.controlcenter.beans.CategoryDetailBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewTypesBean;
import com.heal.controlcenter.dao.mysql.CategoryDao;
import com.heal.controlcenter.enums.CategoryType;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.CategoryDetails;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.util.PaginationUtils;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class GetCategoriesBL implements BusinessLogic<String, UtilityBean<AccountKPIKey>, Page<CategoryDetails>> {
    private final CategoryDao categoryDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetCategoriesBL(CategoryDao categoryDao, ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.categoryDao = categoryDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    /**
     * Validates client request for fetching categories.
     *
     * @param arguments variable arguments: [0] = accountIdentifier, [1] = searchTerm, [2] = type, [3] = subType, [4] = kpiType, [last] = BasicUserDetails
     * @return UtilityBean with validated request parameters
     * @throws ClientException if validation fails
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(Object... arguments) throws ClientException {
        log.debug("[clientValidation] Start with arguments: {}", (Object) arguments);
        try {
            String accountIdentifier = (String) arguments[0];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);
            log.info("[clientValidation] Validated accountIdentifier: {}", accountIdentifier);

            String searchTerm = arguments.length > 1 && arguments[1] instanceof String ? (String) arguments[1] : null;
            String type = arguments.length > 2 && arguments[2] instanceof String ? (String) arguments[2] : null;
            String subType = arguments.length > 3 && arguments[3] instanceof String ? (String) arguments[3] : null;
            String kpiType = arguments.length > 4 && arguments[4] instanceof String ? (String) arguments[4] : null;

            Map<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            if (searchTerm != null) requestParamsMap.put("searchTerm", searchTerm);
            if (type != null) requestParamsMap.put("type", type);
            if (subType != null) requestParamsMap.put("subType", subType);
            if (kpiType != null) requestParamsMap.put(Constants.KPI_TYPE, kpiType);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            log.debug("[clientValidation] Returning UtilityBean with params: {}", requestParamsMap);
            return UtilityBean.<String>builder()
                    .requestParams(requestParamsMap)
                    .metadata(new HashMap<>())
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Validates server-side data and fetches account and KPI type information.
     *
     * @param utilityBean UtilityBean containing validated client parameters
     * @return UtilityBean<AccountKPIKey> with account and KPI type details
     * @throws ServerException if validation fails or data is not found
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<AccountKPIKey> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        log.debug("[serverValidation] Start for GET categories: {}", utilityBean);
        try {
            String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
            Account account = serverValidationUtils.accountValidation(accountIdentifier);
            log.info("[serverValidation] Validated account for identifier: {} with id: {}", accountIdentifier, account.getId());

            String kpiType = utilityBean.getRequestParams().get(Constants.KPI_TYPE);
            ViewTypesBean type = null;
            if (kpiType != null && !kpiType.isEmpty()) {
                log.debug("[serverValidation] Fetching KPI type for subTypeName: {}", kpiType);
                try {
                    type = categoryDao.getKpiTypeBySubTypeName(kpiType);
                } catch (HealControlCenterException e) {
                    log.error("[serverValidation] Error fetching KPI type: {}", e.getMessage(), e);
                    throw new ServerException("Error fetching KPI type: " + e.getMessage());
                }
                if (type != null) {
                    log.info("[serverValidation] Found KPI type: {} with id: {}", type.getSubTypeName(), type.getSubTypeId());
                } else {
                    log.warn("[serverValidation] No KPI type found for subTypeName: {}", kpiType);
                }
            } else {
                log.debug("[serverValidation] No kpiType provided, will fetch all categories for account");
            }

            AccountKPIKey key = AccountKPIKey.builder()
                    .accountId(account.getId())
                    .accountIdentifier(accountIdentifier)
                    .kpiTypeId(type != null ? type.getSubTypeId() : -1)
                    .build();
            log.debug("[serverValidation] Returning AccountKPIKey: {}", key);
            return UtilityBean.<AccountKPIKey>builder()
                    .pojoObject(key)
                    .metadata(utilityBean.getMetadata())
                    .pageable(utilityBean.getPageable())
                    .requestParams(utilityBean.getRequestParams())
                    .build();
        } catch (Exception e) {
            log.error("[serverValidation] Exception: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }

    /**
     * Fetches paginated and filtered categories for the given account.
     *
     * @param utilityBean UtilityBean containing validated client parameters and pagination information
     * @return Page<CategoryDetails> for the account matching the filters
     * @throws DataProcessingException if categories cannot be fetched
     */
    @Override
    @LogExecutionAnnotation
    public Page<CategoryDetails> process(UtilityBean<AccountKPIKey> utilityBean) throws DataProcessingException {
        log.debug("[process] Start fetching categories with utilityBean: {}", utilityBean);
        Pageable pageable = utilityBean.getPageable();
        String searchTerm = utilityBean.getRequestParams().get("searchTerm");
        String type = utilityBean.getRequestParams().get("type");
        String subType = utilityBean.getRequestParams().get("subType");
        try {
            PaginationUtils.validatePagination(pageable);
            AccountKPIKey accountKPIKey = utilityBean.getPojoObject();
            log.info("[process] Fetching categories for accountId: {} with filters - searchTerm: {}, type: {}, subType: {}", accountKPIKey.getAccountId(), searchTerm, type, subType);
            List<CategoryDetailBean> categoryList = categoryDao.getCategoriesForAccountWithFilters(accountKPIKey.getAccountId(), searchTerm, type, subType, pageable);
            int totalCount = categoryDao.countCategoriesForAccountWithFilters(accountKPIKey.getAccountId(), searchTerm, type, subType);

            List<Integer> categoryIds = categoryList.stream().map(CategoryDetailBean::getId).toList();
            Map<Integer, Integer> categoriesKpiCount = new HashMap<>();
            categoryDao.getKpiCountForCategories(categoryIds).forEach(countBean ->
                    categoriesKpiCount.put(countBean.getId(), countBean.getCount())
            );
            List<CategoryDetails> result = categoryList.stream()
                    .map(c -> CategoryDetails.builder()
                            .identifier(c.getIdentifier())
                            .name(c.getName())
                            .id(c.getId())
                            .description(c.getDescription())
                            .status(c.getStatus())
                            .type((c.getIsCustom() == 1) ? Constants.CATEGORY_CUSTOM : Constants.CATEGORY_STANDARD)
                            .subType(getSubType(c).name())
                            .kpiCount(categoriesKpiCount.getOrDefault(c.getId(), 0))
                            .build())
                    .toList();
            log.info("[process] Returning {} categories for accountId: {}", result.size(), accountKPIKey.getAccountId());
            return PaginationUtils.createPage(result, pageable, totalCount);
        } catch (Exception e) {
            log.error("[process] Failed to fetch paginated categories for account. Reason: {}", e.getMessage(), e);
            throw new DataProcessingException("Failed to fetch paginated categories for account. Reason: " + e.getMessage());
        }
    }

    /**
     * Determines the sub type of a category based on its properties.
     *
     * @param category CategoryDetailBean
     * @return CategoryType enum value
     */
    private CategoryType getSubType(CategoryDetailBean category) {
        if (category.getIsWorkLoad() == 1) {
            log.debug("[getSubType] Category id: {} is of type WORKLOAD", category.getId());
            return CategoryType.WORKLOAD;
        }
        if (category.getIsInformative() == 1) {
            log.debug("[getSubType] Category id: {} is of type INFO", category.getId());
            return CategoryType.INFO;
        }
        log.debug("[getSubType] Category id: {} is of type NON_INFO", category.getId());
        return CategoryType.NON_INFO;
    }
}
