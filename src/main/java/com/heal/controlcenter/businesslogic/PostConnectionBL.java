package com.heal.controlcenter.businesslogic;

import com.heal.configuration.entities.ConnectionBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ConnectionDetailsBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ConnectionDetailsDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.redis.ConnectionRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ConnectionDetailsPojo;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class PostConnectionBL implements BusinessLogic<List<ConnectionDetailsPojo>, UtilityBean<List<ConnectionDetailsBean>>, List<IdPojo>> {

    private final AccountsDao accountsDao;
    private final ConnectionDetailsDao connectionDetailsDao;
    private final ServiceRepo serviceRepo;
    private final ClientValidationUtils clientValidationUtils;
    private final ConnectionRepo connectionRepo;
    private final ControllerDao controllerDao;

    public PostConnectionBL(AccountsDao accountsDao, ConnectionDetailsDao connectionDetailsDao, ServiceRepo serviceRepo,
                            ClientValidationUtils clientValidationUtils, ConnectionRepo connectionRepo,
                            ControllerDao controllerDao) {
        this.accountsDao = accountsDao;
        this.connectionDetailsDao = connectionDetailsDao;
        this.serviceRepo = serviceRepo;
        this.clientValidationUtils = clientValidationUtils;
        this.connectionRepo = connectionRepo;
        this.controllerDao = controllerDao;
    }

    /**
     * Validates the connection request for the given account identifier.
     * <p>
     * Checks for:
     * <ul>
     *   <li>Valid account identifier</li>
     *   <li>Non-empty request body</li>
     *   <li>Duplicate connections</li>
     *   <li>Null connection objects</li>
     *   <li>Field-level validation errors in each connection</li>
     * </ul>
     * Throws ClientException if any validation fails.
     *
     * @param arguments   List of ConnectionDetailsPojo objects to be validated, account identifier, and BasicUserDetails.
     * @return UtilityBean containing the validated connection list, request parameters, and empty metadata.
     * @throws ClientException if validation fails for the request body, account identifier, or any connection fields.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ConnectionDetailsPojo>> clientValidation(Object... arguments) throws ClientException {
        try {
            List<ConnectionDetailsPojo> requestBody = (List<ConnectionDetailsPojo>) arguments[0];
            String accountIdentifier = (String) arguments[1];

            log.info("[clientValidation] Starting validation for accountIdentifier: {}", accountIdentifier);
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);
            if (requestBody == null || requestBody.isEmpty()) {
                log.error("[clientValidation] Request body is empty for accountIdentifier: {}", accountIdentifier);
                throw new ClientException("Request body cannot be empty");
            }
            Map<String, String> errors = new HashMap<>();
            Set<ConnectionDetailsPojo> connSet = new HashSet<>(requestBody);
            if (connSet.size() < requestBody.size()) {
                log.warn("[clientValidation] Duplicate connections found in request for accountIdentifier: {}", accountIdentifier);
                errors.put("duplicateConnections", "Duplicate connections found");
            }
            for (int i = 0; i < requestBody.size(); i++) {
                ConnectionDetailsPojo conn = requestBody.get(i);
                if (conn == null) {
                    log.error("[clientValidation] Connection object at index {} is null for accountIdentifier: {}", i, accountIdentifier);
                    errors.put("connections[" + i + "]", "Connection object is null");
                    continue;
                }
                Map<String, String> connErrors = conn.validate();
                for (Map.Entry<String, String> entry : connErrors.entrySet()) {
                    errors.put("connections[" + i + "]." + entry.getKey(), entry.getValue());
                }
            }
            if (!errors.isEmpty()) {
                log.error("[clientValidation] Connection validation failed: {}", errors);
                throw new ClientException(errors.toString());
            }
            Map<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            log.info("[ClientValidation] All connections validated successfully for accountIdentifier: {}", accountIdentifier);
            return UtilityBean.<List<ConnectionDetailsPojo>>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(requestBody)
                    .metadata(new HashMap<>())
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Performs server-side validation for a list of incoming ConnectionDetailsPojo objects.
     * Validates account, uniqueness of connection, and service identifiers.
     * Throws ServerException if any validation fails.
     *
     * @param utilityBean A UtilityBean containing the list of raw ConnectionDetailsPojo objects,
     *                    request parameters (including account identifier), and metadata (including userId).
     * @return UtilityBean with validated ConnectionDetailsBean list, updated metadata, and request parameters.
     * @throws ServerException if validation fails for account, uniqueness, or service identifiers.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ConnectionDetailsBean>> serverValidation(UtilityBean<List<ConnectionDetailsPojo>> utilityBean) throws ServerException {
        log.info("[serverValidation] Validating connections for accountIdentifier: {}", utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean accountBean = accountsDao.getAccountByIdentifier(accountIdentifier);
        if (accountBean == null) {
            log.error("[ServerValidation] Account identifier is invalid");
            throw new ServerException("Account identifier is invalid");
        }
        int accountId = accountBean.getId();
        String userId = String.valueOf(utilityBean.getMetadata().get(Constants.USER_ID_KEY));
        if (userId == null || userId.trim().isEmpty()) {
            log.error("[ServerValidation] Error while extracting userIdentifier from metadata");
            throw new ServerException("Error while extracting user details from metadata");
        }
        List<ConnectionDetailsBean> beanList = new ArrayList<>();
        List<BasicEntity> serviceList = serviceRepo.getAllServicesDetails(accountIdentifier);
        for (ConnectionDetailsPojo conn : utilityBean.getPojoObject()) {
            BasicEntity srcController = serviceList.stream()
                    .filter(c -> (c.getIdentifier().equals(conn.getSourceServiceIdentifier().trim())) && c.getStatus() == 1)
                    .findAny().orElse(null);
            if (srcController == null) {
                log.error("[ServerValidation] Source Service Identifier '[{}]' does not exist.", conn.getSourceServiceIdentifier());
                throw new ServerException(String.format("Source Service Identifier '[%s]' does not exist.", conn.getSourceServiceIdentifier()));
            }
            BasicEntity destController = serviceList.stream()
                    .filter(c -> (c.getIdentifier().equals(conn.getDestinationServiceIdentifier().trim())) && c.getStatus() == 1)
                    .findAny().orElse(null);
            if (destController == null) {
                log.error("[ServerValidation] Destination Service Identifier '[{}]' does not exist.", conn.getDestinationServiceIdentifier());
                throw new ServerException(String.format("Destination Service Identifier '[%s]' does not exist.", conn.getDestinationServiceIdentifier()));
            }
            boolean exists = connectionDetailsDao.connectionExists(srcController.getId(), destController.getId(), accountId);
            if (exists) {
                log.error("[ServerValidation] Connection between Source Service Identifier '[{}]' and Destination Service Identifier '[{}]' already exists.",
                        conn.getSourceServiceIdentifier(), conn.getDestinationServiceIdentifier());
                throw new ServerException(String.format("Connection between Source Service Identifier '[%s]' and Destination Service Identifier '[%s]' already exists.",
                        conn.getSourceServiceIdentifier(), conn.getDestinationServiceIdentifier()));
            }
            beanList.add(ConnectionDetailsBean.builder()
                    .sourceId(srcController.getId())
                    .destinationId(destController.getId())
                    .sourceRefObject(Constants.CONTROLLER)
                    .destinationRefObject(Constants.CONTROLLER)
                    .createdTime(Objects.requireNonNull(DateTimeUtil.getCurrentTimestampInGMT()).toString())
                    .updatedTime(DateTimeUtil.getCurrentTimestampInGMT().toString())
                    .isDiscovery(0)
                    .accountId(accountId)
                    .userDetailsId(userId)
                    .build());
        }
        Map<String, Object> metadata = utilityBean.getMetadata();
        metadata.put(Constants.ACCOUNT, accountBean);
        return UtilityBean.<List<ConnectionDetailsBean>>builder()
                .pojoObject(beanList)
                .metadata(metadata)
                .requestParams(utilityBean.getRequestParams())
                .build();
    }

    /**
     * Processes a list of ConnectionDetailsBean objects by inserting them as connections and updating Redis cache.
     * Returns a list of created connection IDs. Throws DataProcessingException if any step fails.
     *
     * @param utilityBean A utility wrapper containing the connection list, metadata (userId, account), and request parameters.
     * @return A list of IdPojo representing the successfully created connections.
     * @throws DataProcessingException if any part of the process (insertion or Redis update) fails.
     */
    @Override
    @LogExecutionAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public List<IdPojo> process(UtilityBean<List<ConnectionDetailsBean>> utilityBean) throws DataProcessingException {
        log.info("[process] Adding new connections for accountIdentifier: {}", utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        try {
            int[] ids = connectionDetailsDao.addConnection(utilityBean.getPojoObject());
            if (ids.length != 0) {
                List<IdPojo> list = new ArrayList<>();
                for (int id : ids) {
                    list.add(IdPojo.builder().id(id).name(null).identifier(null).build());
                }
                AccountBean accountBean = (AccountBean) utilityBean.getMetadata().get(Constants.ACCOUNT);
                updateRedisCache(accountBean);
                return list;
            } else {
                String msg = String.format("Unable to add connections for accountIdentifier [%s]. No connection IDs returned from DB.", accountIdentifier);
                log.error("[process] {} | ConnectionDetailsBeanList size: {}", msg, utilityBean.getPojoObject() != null ? utilityBean.getPojoObject().size() : 0);
                throw new DataProcessingException(msg);
            }
        } catch (DataProcessingException e) {
            log.error("[process] DataProcessingException while adding connections for accountIdentifier [{}]: {}", accountIdentifier, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("[process] Unexpected error while adding connections for accountIdentifier [{}]: {}", accountIdentifier, e.getMessage(), e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    /**
     * Updates the Redis cache with the latest service and connection details for the given account.
     * <p>
     * This method fetches all services and connections for the account, then updates Redis with:
     * <ul>
     *   <li>Service-to-service connections</li>
     *   <li>Neighbours for each service</li>
     *   <li>Outbound connections</li>
     *   <li>Inbound connections</li>
     * </ul>
     * Logs the progress and any errors encountered during the update.
     *
     * @param accountBean The account entity for which Redis cache should be updated.
     */
    private void updateRedisCache(AccountBean accountBean) throws DataProcessingException {
        try {
            String accountIdentifier = accountBean.getIdentifier();
            int accountId = accountBean.getId();

            // Fetch services from DB using ControllerDao, then convert to BasicEntity
            List<ControllerBean> servicesList = controllerDao.getAllServicesByAccountId(accountId);

            if (servicesList.isEmpty()) {
                log.debug("[updateRedisCache] No service details exist for account. Account identifier: {}", accountIdentifier);
            } else {
                log.info("[updateRedisCache] Fetched {} services for accountIdentifier: {}", servicesList.size(), accountIdentifier);
                List<ConnectionBean> connectionsList = connectionDetailsDao.getServiceConnectionBeansForAccount(accountId);
                log.info("[updateRedisCache] Fetched {} connections for accountId: {}", connectionsList.size(), accountId);
                if (connectionsList.isEmpty()) {
                    log.warn("[updateRedisCache] No connections found for accountId: {}. Redis will be updated with empty data.",
                            accountId);
                }
                connectionRepo.updateServiceConnections(accountIdentifier, connectionsList, servicesList);
                connectionRepo.updateNeighbours(accountBean, connectionsList, servicesList);
                connectionRepo.updateOutbounds(accountIdentifier, connectionsList);
                connectionRepo.updateInbounds(accountIdentifier, connectionsList);
                log.info("[updateRedisCache] Redis cache update complete for accountIdentifier: {}", accountIdentifier);
            }
        } catch (Exception e) {
            log.error("[updateRedisCache] Failed to update connection details for account in redis cache. Account identifier: {}",
                    accountBean.getIdentifier(), e);
            throw new DataProcessingException("Failed to update connection details for account in redis cache.");
        }
    }
}
