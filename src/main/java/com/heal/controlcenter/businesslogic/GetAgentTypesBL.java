package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentTypePojo;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetAgentTypesBL implements BusinessLogic<Integer, UtilityBean<Integer>, List<AgentTypePojo>> {

    private final AccountsDao accountDao;
    private final ControllerDao controllerDao;
    private final MasterDataDao masterDataDao;
    private final CompInstanceDao compInstanceDao;
    private final AgentDao agentDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetAgentTypesBL(AccountsDao accountDao, ControllerDao controllerDao, MasterDataDao masterDataDao,
                           CompInstanceDao compInstanceDao, AgentDao agentDao, ClientValidationUtils clientValidationUtils,
                           ServerValidationUtils serverValidationUtils) {
        this.accountDao = accountDao;
        this.controllerDao = controllerDao;
        this.masterDataDao = masterDataDao;
        this.compInstanceDao = compInstanceDao;
        this.agentDao = agentDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<Integer> clientValidation(Object... arguments) throws ClientException {
        try {
            String accountIdentifier = (String) arguments[0];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            String serviceIdentifier = (String) arguments[1];
            clientValidationUtils.serviceIdentifierValidation(serviceIdentifier);

            int serviceId;
            try {
                serviceId = Integer.parseInt(serviceIdentifier);
            } catch (NumberFormatException e) {
                log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, serviceIdentifier));
                throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
            }

            if(serviceId <= 0) {
                log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, serviceIdentifier));
                throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
            }

            HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            return UtilityBean.<Integer>builder()
                    .pojoObject(serviceId)
                    .requestParams(requestParamsMap)
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }


    @Override
    public UtilityBean<Integer> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setMetadata(Map.of(Constants.ACCOUNT, account));

        ControllerBean service = null;
        try {
            service = controllerDao.getServiceById(utilityBean.getPojoObject(), account.getId());
        } catch (HealControlCenterException e) {
            throw new ServerException(e.getMessage());
        }
        if (null == service) {
            String message = String.format("ServiceId [%d] is unavailable for account [%s]", utilityBean.getPojoObject(),utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
            log.error(message);
            throw new ServerException(message);
        }

        return utilityBean;
    }

    @Override
    public List<AgentTypePojo> process(UtilityBean<Integer> bean) throws DataProcessingException {
        int serviceId = bean.getPojoObject();
        int accountId = ((AccountBean) bean.getMetadata().get(Constants.ACCOUNT)).getId();

        Set<Integer> agentIds = compInstanceDao.getAgentIdForService(serviceId, accountId);
        agentIds.addAll(compInstanceDao.getAgentIdForServiceFromTagMapping(serviceId, accountId));

        if(agentIds.isEmpty()) {
            log.warn("There are no agents mapped to the service [{}]", serviceId);
            return Collections.emptyList();
        }

        try {
            return agentDao.getAgentTypeListApartFromJimAndForensicAgents(agentIds)
                    .parallelStream()
                    .map(h -> AgentTypePojo.builder()
                            .id(h.getAgentTypeId())
                            .name(masterDataDao.getMstSubTypeBySubTypeId(h.getAgentTypeId()).getSubTypeName())
                            .build()).filter(Objects::nonNull)
                    .sorted(Comparator.comparing(AgentTypePojo::getId)
                            .thenComparing(o -> o.getName().toLowerCase()))
                    .distinct()
                    .collect(Collectors.toList());
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
    }
}
