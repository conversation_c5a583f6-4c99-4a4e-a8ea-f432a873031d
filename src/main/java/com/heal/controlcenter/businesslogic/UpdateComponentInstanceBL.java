package com.heal.controlcenter.businesslogic;

import com.google.common.base.Throwables;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicAgentEntity;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.dao.mysql.entity.ComponentInstanceDao;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.dao.redis.AgentRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.InstanceClusterServicePojo;
import com.heal.controlcenter.pojo.UpdateInstancePojo;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.*;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Business Logic for updating component instances.
 * Converted from appsone-controlcenter UpdateInstanceBL to Spring Boot with JDBC.
 */
@Slf4j
@Component
public class UpdateComponentInstanceBL implements BusinessLogic<List<UpdateInstancePojo>, UtilityBean<List<UpdateInstancePojo>>, List<IdPojo>> {

    private final ClientValidationUtils clientValidationUtils;

    private final ServerValidationUtils serverValidationUtils;

    private final ComponentInstanceDao componentInstanceDao;
    
    private final MasterDataDao masterDataDao;
    
    private final TagsDao tagsDao;
    
    private final ControllerDao controllerDao;
    
    private final InstanceRepo instanceRepo;
    
    private final ServiceRepo serviceRepo;
    
    private final AgentRepo agentRepo;

    public UpdateComponentInstanceBL(ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils, CompInstanceDao compInstanceDao, ComponentInstanceDao componentInstanceDao, MasterDataDao masterDataDao, TagsDao tagsDao, ControllerDao controllerDao, InstanceRepo instanceRepo, ServiceRepo serviceRepo, AgentRepo agentRepo) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.componentInstanceDao = componentInstanceDao;
        this.masterDataDao = masterDataDao;
        this.tagsDao = tagsDao;
        this.controllerDao = controllerDao;
        this.instanceRepo = instanceRepo;
        this.serviceRepo = serviceRepo;
        this.agentRepo = agentRepo;
    }

    @Override
    public UtilityBean<List<UpdateInstancePojo>> clientValidation(Object... arguments) throws ClientException {
        List<UpdateInstancePojo> requestBody = (List<UpdateInstancePojo>) arguments[0];
        String accountIdentifier = (String) arguments[1];
        BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

        // Validate account identifier
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        // Validate request body
        if (requestBody == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (requestBody.isEmpty()) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        requestBody = requestBody.parallelStream().distinct().collect(Collectors.toList());

        for (UpdateInstancePojo data : requestBody) {
            if (!data.isValid()) {
                log.error("Input Validation failure for Update Instance Details.");
                throw new ClientException("Input Validation failure for Update Instance Details.");
            }
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);
        log.debug("[clientValidation] Validation successful for accountIdentifier: {}", accountIdentifier);

        return UtilityBean.<List<UpdateInstancePojo>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .basicUserDetails(basicUserDetails)
                .build();
    }

    @Override
    public UtilityBean<List<UpdateInstancePojo>> serverValidation(UtilityBean<List<UpdateInstancePojo>> utilityBean) throws ServerException {
        try {
        log.trace("Starting server validation for update component instances");

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        List<UpdateInstancePojo> requests = utilityBean.getPojoObject();
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        if (account == null) {
            throw new ServerException("Account not found with identifier: " + accountIdentifier);
        }

        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);

        if (StringUtils.isEmpty(userId)) {
            throw new ServerException("User ID is required for update operations");
        }

        for (UpdateInstancePojo instance : requests) {
            requestBodyServerValidation(instance, account.getId());
        }

        utilityBean.getMetadata().put(Constants.ACCOUNT, account);

            return UtilityBean.<List<UpdateInstancePojo>>builder()
                    .requestParams(utilityBean.getRequestParams())
                    .pojoObject(requests)
                    .metadata(utilityBean.getMetadata())
                    .build();
        } catch (Exception e) {
            log.error("Server validation failed: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }

    private void requestBodyServerValidation(UpdateInstancePojo instance, int accId) throws ServerException, HealControlCenterException {
        /*
        Instance details check
         */
        ComponentInstanceBean instanceBean = componentInstanceDao.
                getComponentInstanceByIdAndIdentifierAndAccount(instance.getInstanceId(), instance.getInstanceIdentifier(), accId);
        if (instanceBean == null) {
            log.error("Instance with the id [{}] and identifier [{}] is unavailable for this account.", instance.getInstanceId(), instance.getInstanceIdentifier());
            throw new ServerException(String.format("Instance with id [%s] and identifier [%s] is unavailable for this account.",
                    instance.getInstanceId(), instance.getInstanceIdentifier()));
        }

        List<MasterComponentTypeBean> masterComponentTypeBeans = masterDataDao.getMasterComponentTypesData(accId);

        MasterComponentTypeBean componentTypeBean = masterComponentTypeBeans.stream().filter(masterComponentTypeBean -> masterComponentTypeBean.getName()
                .equalsIgnoreCase(Constants.COMPONENT_TYPE_HOST)).findAny().orElse(null);


        if (componentTypeBean == null) {
            String err = "Component with type '" + Constants.HOST + "' doesn't exist.";
            log.error(err);
            throw new ServerException(err);
        }

        instance.setHostInstance(componentTypeBean.getId() == instanceBean.getMstComponentTypeId());

        // add a check for host getting updated with env or ip address is already exist.
        // check only when you have a change request for environment id
        if (instance.isHostInstance() && instance.getEnvironment() != null) {
            try {
                int envId = new EnvironmentHelper().getEnvironmentId(instance.getEnvironment());

                int instanceDetailsByHostAddress = componentInstanceDao.getInstanceDetailsByHostAddress(accId, instanceBean.getHostAddress(), envId);

                if (instanceDetailsByHostAddress == -1) {
                    String errMsg = String.format("Exception while getting count of the instances with host_address %s and environment %s for account %s from Data source", instanceBean.getHostAddress(), envId, accId);
                    log.error(errMsg);
                    throw new ServerException(errMsg);
                }

                if (instanceDetailsByHostAddress >= 1) {
                    String err = "Host instance with the same environment and host address already exists.";
                    log.error(err);
                    throw new ServerException(err);
                }
                log.debug("no other host exist in env {} with host address {}",  envId, instanceBean.getHostAddress());

            } catch (DataProcessingException e) {
                String err = "Invalid environment provided";
                log.error(err);
                throw new ServerException(err);
            }
        }
        /*
        Application check
         */
        if (instance.getApplication() != null) {

            for (UpdateInstancePojo.ApplicationServiceMapping applicationData : instance.getApplication()) {

                ControllerBean appBean = controllerDao.getApplicationsById(applicationData.getId(), accId);
                if (appBean == null || !appBean.getIdentifier().equals(applicationData.getIdentifier())) {
                    log.error("Application with the id [{}] and identifier [{}] is unavailable for this account.", applicationData.getId(),
                            applicationData.getIdentifier());
                    throw new ServerException(String.format("Application with id [%s] and identifier [%s] is unavailable for this account.",
                            applicationData.getId(), applicationData.getIdentifier()));
                }

                List<ControllerBean> serviceBeanList = new ArrayList<>();
                try {
                    serviceBeanList = controllerDao.getServicesForAccount(accId);
                } catch (Exception e) {
                    log.error("Exception occurred. Details: ", e);
                }

                List<ViewApplicationServiceMappingBean> viewApplicationServiceMappingBeans =
                        controllerDao.getApplicationServiceMappingWithAppId(accId, applicationData.getId());

                /*
                Service check
                 */
                for (UpdateInstancePojo.IdAction serviceData : applicationData.getService()) {

                    ControllerBean serviceBean = serviceBeanList.stream().filter(c -> c.getId() == serviceData.getId()
                                    && c.getIdentifier().equals(serviceData.getIdentifier()))
                            .findAny()
                            .orElse(null);
                    if (serviceBean == null) {
                        log.error("Service with the id [{}] and identifier [{}] is unavailable for this account.", serviceData.getId(),
                                serviceData.getIdentifier());
                        throw new ServerException(String.format("Service with the id [%s] and identifier [%s] is unavailable for this account.",
                                serviceData.getId(), serviceData.getIdentifier()));
                    }

                    ViewApplicationServiceMappingBean appService = viewApplicationServiceMappingBeans.stream()
                            .filter(c -> c.getApplicationId() == applicationData.getId()
                                    && c.getApplicationIdentifier().equals(applicationData.getIdentifier())
                                    && c.getServiceId() == serviceData.getId()
                                    && c.getServiceIdentifier().equals(serviceData.getIdentifier()))
                            .findAny()
                            .orElse(null);

                    if (appService == null) {
                        log.error("Service with the id [{}] and identifier [{}] is not mapped to Application with id [{}] and " +
                                        "identifier [{}].", serviceData.getId(), serviceData.getIdentifier(),
                                applicationData.getId(), applicationData.getIdentifier());
                        throw new ServerException(String.format("Service with id [%s] and identifier [%s] is not mapped to" +
                                        " Application with id [%s] and identifier [%s].",
                                serviceData.getId(), serviceData.getIdentifier(), applicationData.getId(), applicationData.getIdentifier()));
                    }


                    if (serviceData.getAction().equalsIgnoreCase("Add")) {
                        List<InstanceClusterServicePojo> instanceDetails = componentInstanceDao
                                .getInstanceClusterServiceDetails(serviceData.getId(), accId);
                        /*
                        if host cluster present, then component id and common version id should be same
                         */
                        if (instance.isHostInstance()) {

                            if (instanceDetails != null && !instanceDetails.isEmpty()) {
                                for (InstanceClusterServicePojo singleHostDetails : instanceDetails) {
                                    if (singleHostDetails.getClusterComponentId() != instanceBean.getMstComponentId()
                                            && singleHostDetails.getClusterComponentTypeId() == componentTypeBean.getId()) {
                                        String err = "Host Instance's component is different from existing host " +
                                                "cluster's component for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }

                                    if (singleHostDetails.getClusterCommonVersionId() != instanceBean.getMstCommonVersionId()
                                            && singleHostDetails.getClusterComponentTypeId() == componentTypeBean.getId()) {
                                        String err = "Host Instance's component common version is different from existing host " +
                                                "cluster's component common version for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }
                                }
                            }
                        } else {
                            /*
                            host cluster should be present for comp instance map check
                             */
                            List<InstanceClusterServicePojo> hostClusterDetails = componentInstanceDao
                                    .getClusterServiceDetails(serviceData.getId(), componentTypeBean.getId());
                            if (hostClusterDetails == null || hostClusterDetails.isEmpty()) {
                                String err = "No Host Cluster found while mapping component instance to the service " + serviceData.getName();
                                log.error(err);
                                throw new ServerException(err);
                            }

                            if (instanceDetails != null && !instanceDetails.isEmpty()) {
                                for (InstanceClusterServicePojo singleCompInstanceDetails : instanceDetails) {
                                    if (singleCompInstanceDetails.getClusterComponentId() != instanceBean.getMstComponentId()
                                            && singleCompInstanceDetails.getClusterComponentTypeId() != componentTypeBean.getId()) {
                                        String err = "Component Instance's component is different from existing component " +
                                                "cluster's component for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }

                                    if (singleCompInstanceDetails.getClusterCommonVersionId() != instanceBean.getMstCommonVersionId()
                                            && singleCompInstanceDetails.getClusterComponentTypeId() != componentTypeBean.getId()) {
                                        String err = "Component Instance's component common version is different from existing Component " +
                                                "cluster's component common version for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<IdPojo> process(UtilityBean<List<UpdateInstancePojo>> utilityBean) throws DataProcessingException {
        log.trace("Starting process for update component instances");

        try {
            List<UpdateInstancePojo> updateRequests = utilityBean.getPojoObject();
            Account account = (Account) utilityBean.getMetadata().get(Constants.ACCOUNT);
            String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
            String updateTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());

            AgentRepo agentRepo = new AgentRepo();
            ServiceRepo serviceRepo = new ServiceRepo();
            InstanceRepo instanceRepo = new InstanceRepo();

            List<Integer> agentIds = new ArrayList<>();
            try {
                    for (UpdateInstancePojo instance : utilityBean.getPojoObject()) {
                        try {
                            int agentId = updateInstance(instance, updateTime, userId, account.getId());
                            if (agentId != 0) {
                                agentIds.add(agentId);
                            }
                        } catch (Exception e) {
                            log.error("Error while updating Instance information. Reason: ", e);
                            throw new DataProcessingException("Error in updating instance information for instance with id '"
                                    + instance.getInstanceId() + "' and identifier " + instance.getInstanceIdentifier());
                        }
                    }

                Map<Integer, CompInstClusterDetails> compInstDetailsMap = instanceRepo.getInstances(accountIdentifier).stream()
                        .collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));

                for (UpdateInstancePojo updateInstancePojo : instancePojoList) {
                    if (updateInstancePojo.getName() != null || updateInstancePojo.getEnvironment() != null) {
                        CompInstClusterDetails instClusterDetails = compInstDetailsMap.get(updateInstancePojo.getInstanceId());

                        if (instClusterDetails == null) {
                            log.error("Could not find instance detail for given instanceId [{}]", updateInstancePojo.getInstanceId());
                            return null;
                        }

                        if (instClusterDetails.getComponentTypeId() == 1) {
                            List<CompInstClusterDetails> instancesMappedToThisHostAddress = compInstDetailsMap.values()
                                    .parallelStream()
                                    .filter(e -> e.getHostAddress() != null)
                                    .filter(f -> f.getHostAddress().equalsIgnoreCase(instClusterDetails.getHostAddress()))
                                    .collect(Collectors.toList());

                            Map<Boolean, List<CompInstClusterDetails>> mapOfCompAndHostInstances = instancesMappedToThisHostAddress.parallelStream().collect(Collectors.partitioningBy(pt -> pt.getComponentTypeId() == 1));

                            /*Code block below adds functionality of updating host-name in service level instances redis-cache key */
                            mapOfCompAndHostInstances.get(true).forEach(mappedHostInstanceObj -> {
                                List<BasicEntity> services = instanceRepo.getServices(accountIdentifier, mappedHostInstanceObj.getIdentifier());
                                services.forEach(service -> {
                                    List<BasicInstanceBean> updatedBasicInstanceBean = serviceRepo.getServiceInstances(accountIdentifier, service.getIdentifier())
                                            .stream()
                                            .peek(p -> {
                                                if(p.getIdentifier().equalsIgnoreCase(mappedHostInstanceObj.getIdentifier())) {
                                                    p.setName(updateInstancePojo.getName() == null ? instClusterDetails.getName() : updateInstancePojo.getName());
                                                    p.setUpdatedTime(updateTime);
                                                }
                                            })
                                            .collect(Collectors.toList());

                                    serviceRepo.updateServiceInstances(accountIdentifier, service.getIdentifier(), updatedBasicInstanceBean);
                                });

                                /*Code block below adds functionality of updating host-name in agent level instances redis-cache key */
                                CompInstClusterDetails compInstClusterDetails = instanceRepo.getInstanceDetailByIdentifier(accountIdentifier,
                                        mappedHostInstanceObj.getIdentifier());
                                if(compInstClusterDetails != null && compInstClusterDetails.getAgentIds() != null) {
                                    compInstClusterDetails.getAgentIds()
                                            .forEach(agentIdentifier -> {
                                                List<BasicEntity> updatedBasicEntity = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, agentIdentifier)
                                                        .stream()
                                                        .peek(p -> {
                                                            if (p.getIdentifier().equalsIgnoreCase(mappedHostInstanceObj.getIdentifier())) {
                                                                p.setName(updateInstancePojo.getName() == null ? instClusterDetails.getName() : updateInstancePojo.getName());
                                                                p.setUpdatedTime(updateTime);
                                                            }

                                                        })
                                                        .collect(Collectors.toList());
                                                agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, agentIdentifier, updatedBasicEntity);
                                            });
                                }
                            });
                            /*Code block below updates host-name in instances and specific instance(instance-by-identifier) redis-cache keys*/
                            mapOfCompAndHostInstances.get(false).forEach(mappedCompInstanceObj -> {
                                        mappedCompInstanceObj.setHostName(updateInstancePojo.getName() == null ? instClusterDetails.getName() : updateInstancePojo.getName());
                                        mappedCompInstanceObj.setUpdatedTime(updateTime);
                                        compInstDetailsMap.put(mappedCompInstanceObj.getId(), mappedCompInstanceObj);
                                        instanceRepo.updateInstanceByIdentifier(accountIdentifier, mappedCompInstanceObj);

                                    }
                            );
                            instanceRepo.updateInstances(accountIdentifier, new ArrayList<>(compInstDetailsMap.values()));
                        }

                        updateInstanceDetailInRedis(compInstDetailsMap, updateInstancePojo, instClusterDetails, updateTime);

                        updateServiceWiseInstanceInRedis(updateInstancePojo, updateTime);

                        if (!agentIds.isEmpty()) {
                            List<BasicAgentEntity> agents = agentRepo.getAgents(accountIdentifier);
                            for (Integer agentId : agentIds) {
                                BasicAgentEntity basicAgentEntity = agents.parallelStream().filter(f -> f.getId() == agentId).findAny().orElse(null);
                                if (basicAgentEntity == null) {
                                    log.error("Could not find agent detail for agentId [{}] from agents list", agentId);
                                    return null;
                                }
                                List<BasicEntity> agentInstanceMappingDetails = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, basicAgentEntity.getIdentifier());
                                BasicEntity basicEntity = agentInstanceMappingDetails.parallelStream().filter(f -> f.getId() == instClusterDetails.getId()).findAny().orElse(null);
                                if (basicEntity != null) {
                                    basicEntity.setName(updateInstancePojo.getName() == null ? basicEntity.getName() : updateInstancePojo.getName());
                                    basicEntity.setUpdatedTime(updateTime);
                                    agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, basicEntity.getIdentifier(), agentInstanceMappingDetails);
                                }
                            }
                        }
                    }

                }

            } catch (Exception e) {
                log.error("Error while updating Instance information. Reason: ", e);
                throw (DataProcessingException) Throwables.getRootCause(e);
            }
            return null;





//            List<IdPojo> results = new ArrayList<>();
//            List<Integer> agentIds = new ArrayList<>();
//
//            // Process each update request in transaction
//            for (UpdateInstancePojo request : updateRequests) {
//                int agentId = updateInstance(request, updateTime, userId, account.getId());
//                if (agentId != 0) {
//                    agentIds.add(agentId);
//                }
//
//                results.add(IdPojo.builder()
//                        .id(request.getInstanceId())
//                        .name(request.getName())
//                        .identifier(request.getInstanceIdentifier())
//                        .build());
//            }
//
//            // Update Redis cache
//            updateRedisCache(updateRequests, account.getIdentifier(), updateTime, agentIds);

//            return results;
//
        }
        catch (Exception e) {
            log.error("Error while processing update component instances", e);
            throw new DataProcessingException("Failed to process update component instances: " + e.getMessage());
        }
        return List.of();
    }

    /**
     * Updates a single component instance following appsone-controlcenter pattern
     */
    private int updateInstance(UpdateInstancePojo instance, String updateTime, String userId, int accId) throws DataProcessingException {
        CompInstanceDataService compInstanceDataService = new CompInstanceDataService();

        ComponentInstanceBean instanceBean = componentInstanceDao.
                getComponentInstanceByIdAndIdentifierAndAccount(instance.getInstanceId(), instance.getInstanceIdentifier(), accId);

        MasterComponentTypeBean componentTypeBean = masterDataDao.getMasterComponentTypeData(Constants.COMPONENT_TYPE_HOST, accId);
        assert componentTypeBean != null;

        /*
        instance name edit
         */
        if (instance.getName() != null) {
            instanceDetailsUpdate(instance, updateTime, userId, accId);
        }

        /*
        instance environment edit
         */
        if (instance.getEnvironment() != null) {
            instanceEnvironmentUpdate(instance, updateTime, userId, accId);
        }

        /*
        get agent id for the instance
         */
        int agentId = 0;
        if (instance.isHostInstance()) {
            agentId = AgentDataService.getAgentId(instanceBean.getId(), null);
        }
        if (agentId == -1) {
            log.error("Exception while getting agent id from agent_comp_instance_mapping table.");
            throw new DataProcessingException("Exception while getting agent id from agent_comp_instance_mapping table.");
        }
        TagDetailsBean tagDetailsBean = MasterCache.getTagDetails(Constants.CONTROLLER_TAG);
        int tagId = 1;
        if (tagDetailsBean == null) {
            log.warn("Exception while fetching tag id for 'Controller' type. Setting tag id value to default i.e 1.");
        } else {
            tagId = tagDetailsBean.getId();
        }

        /*
        instance cluster edit
         */
        if (instance.getApplication() != null) {

            List<ViewClusterServicesBean> viewClusterServicesBeans = new ControllerDataService().getClusterServiceMapping(null);

            for (UpdateInstancePojo.ApplicationServiceMapping applicationData : instance.getApplication()) {

                for (UpdateInstancePojo.IdAction serviceData : applicationData.getService()) {

                    if (serviceData.getAction().equalsIgnoreCase("Add")) {

                        /*
                        In case earlier mapping was wrong and component instance had host_id 0
                         */
                        if (!instance.isHostInstance()) {
                            List<InstanceClusterServicePojo> hostDetails = new CompInstanceDataService()
                                    .getInstanceClusterServiceDetails(serviceData.getId(), accId, null);
                            hostDetails.stream().filter(c -> c.getHostAddress().equals(instanceBean.getHostAddress())
                                            && c.getClusterComponentTypeId() == componentTypeBean.getId()).findAny()
                                    .ifPresent(hostInstPojo -> instanceBean.setHostId(hostInstPojo.getInstanceId()));
                            ComponentDao componentInstanceDao = MySQLConnectionManager.getInstance().open(ComponentDao.class);
                            componentInstanceDao.updateInstanceHostIds(instanceBean.getHostId(), instanceBean.getId(), accId);
                        }

                        int clusterId = compInstanceDataService
                                .getClusterId(instanceBean.getMstComponentId(),
                                        instanceBean.getMstCommonVersionId(), instanceBean.getMstComponentTypeId(), serviceData.getId(),
                                        Objects.requireNonNull(MasterCache.getTagDetails(Constants.CONTROLLER_TAG)).getId(), accId, handle);

                         /*
                        get host id for add cluster
                         */
                        int clusterHostId = 0;

                        if (clusterId <= 0) {
                            if (!instance.isHostInstance()) {
                                try {
                                    List<Integer> hostIds = new BindInDataService()
                                            .getHostClusterId(Collections.singletonList(serviceData.getId()), componentTypeBean.getId(), null);
                                    if (hostIds.size() != 1) {
                                        log.warn("Multiple host cluster Ids available for the provided service. Resetting hostId to 0 for add cluster due to this conflict.");
                                    } else {
                                        clusterHostId = hostIds.get(0);
                                    }
                                } catch (ControlCenterException e) {
                                    log.error("Unable to fetch host cluster ID");
                                    throw new DataProcessingException(e.getMessage());
                                }
                            }
                            /*
                            add cluster
                             */
                            clusterId = addCluster(instanceBean, Collections.singletonList(serviceData), updateTime, clusterHostId, handle);
                        }
                        /*
                        add component cluster mapping
                         */
                        addComponentClusterMapping(instanceBean.getId(), updateTime, clusterId, handle);

                         /*
                        add agent-service data in tag mapping
                         */
                        if (agentId != 0) {
                            addAgentServiceTagMapping(tagId, agentId, String.valueOf(serviceData.getId()), serviceData.getIdentifier(), handle);
                        }

                    } else if (serviceData.getAction().equalsIgnoreCase("Remove")) {

                        ViewClusterServicesBean serviceCluster = viewClusterServicesBeans.stream()
                                .filter(c -> c.getServiceId() == serviceData.getId()
                                        && c.getServiceIdentifier().equals(serviceData.getIdentifier())
                                        && instance.isHostInstance() == (c.getHostClusterId() == 0))
                                .findAny().orElse(null);

                        if (serviceCluster == null) {
                            log.error("No cluster-service mapping found for instance [{}] and service [{}]",
                                    instanceBean.getName(), serviceData.getName());
                            throw new DataProcessingException("No cluster-service mapping found for instance" +
                                    " [" + instanceBean.getName() + "] and service [" + serviceData.getName() + "]");
                        }

                        int oldClusterId = serviceCluster.getClusterId();

                        //Find remaining services mapped to that serviceCluster
                        List<UpdateInstancePojo.IdAction> remainingServList = viewClusterServicesBeans.stream()
                                .filter(c -> c.getClusterId() == oldClusterId)
                                .filter(c -> c.getServiceId() != serviceData.getId())
                                .map(c -> {
                                    UpdateInstancePojo.IdAction idAction = new UpdateInstancePojo.IdAction();
                                    idAction.setId(c.getServiceId());
                                    idAction.setIdentifier(c.getServiceIdentifier());
                                    return idAction;
                                }).collect(Collectors.toList());

                        //Get the mapped instances to the old cluster
                        List<CompClusterMappingBean> compClusterMappingBeanList =
                                ComponentDataService.getClusterMapping(oldClusterId, accId);

                        if (!remainingServList.isEmpty()) {
                            //Find hostId
                            int clusterHostId = 0;
                            if (!instance.isHostInstance()) {
                                try {
                                    List<Integer> hostIds = new BindInDataService()
                                            .getHostClusterId(remainingServList.stream().map(UpdateInstancePojo.IdAction::getId).collect(Collectors.toList()),
                                                    componentTypeBean.getId(), null);
                                    if (hostIds.size() != 1) {
                                        log.warn("Multiple host cluster Ids available for the provided service. Resetting hostId to 0 for add cluster due to this conflict.");
                                    } else {
                                        clusterHostId = hostIds.get(0);
                                    }
                                } catch (ControlCenterException e) {
                                    log.error("Unable to fetch host cluster ID");
                                    throw new DataProcessingException(e.getMessage());
                                }
                            }

                            //Add new Cluster
                            int newClusterId = addCluster(instanceBean, remainingServList, updateTime, clusterHostId, handle);


                            //Map all the instances to the new cluster
                            for (CompClusterMappingBean compClusterMappingBean : compClusterMappingBeanList) {
                                addComponentClusterMapping(compClusterMappingBean.getCompInstanceId(), updateTime, newClusterId, handle);
                            }

                            //Remove the remaining service mapping to the old cluster
                            for (UpdateInstancePojo.IdAction service : remainingServList) {
                                compInstanceDataService.deleteTagMappingWithClusterIdandService(oldClusterId,
                                        tagId, Constants.COMP_INSTANCE_TABLE, service.getId(), service.getIdentifier(), handle);
                            }
                        } else {

                            // Check if any other instances are part of the cluster before removing the tag mapping.
                            // If the mapping beans size is equal to 1, we will remove the tag mapping.
                            // We check for size 1 because there should be a cluster mapping for the instance
                            // that triggered the update instance API.

                            List<CompClusterMappingBean> clusterMappingBeans = compClusterMappingBeanList
                                    .parallelStream()
                                    .filter(c -> c.getClusterId() == oldClusterId)
                                    .collect(Collectors.toList());

                            if (clusterMappingBeans.size() == 1) {
                                compInstanceDataService.deleteTagMappingWithClusterIdandService(oldClusterId,
                                        tagId, Constants.COMP_INSTANCE_TABLE, serviceData.getId(), serviceData.getIdentifier(), handle);
                                log.info("Component-cluster is successfully unmapped for clusterId : {} and serviceId : {}.", oldClusterId, serviceData.getId());
                            }
                        }


                        //delete component cluster mapping
                        try {
                            deleteComponentClusterMapping(instanceBean, oldClusterId, handle);
                        } catch (Exception e) {
                            log.error("Exception while deleting component-cluster mapping. Reason:-", e);
                            throw new DataProcessingException("Exception while deleting component-cluster mapping for instance " +
                                    instance.getInstanceIdentifier() + "and cluster " + oldClusterId);
                        }

                        /*
                        delete agent-service data in tag mapping
                         */
                        if (agentId != 0) {
                            deleteAgentServiceTagMapping(tagId, agentId, String.valueOf(serviceData.getId()), serviceData.getIdentifier(), handle);
                        }
                    }
                }
            }
        }
        return agentId;
    }

    private void instanceEnvironmentUpdate(UpdateInstancePojo instance, String updateTime, String userId, int accId) throws DataProcessingException {
        try {
            int environmentId;
            try {
                environmentId = new EnvironmentHelper().getEnvironmentId(instance.getEnvironment());
            } catch (Exception e) {
                log.error("Setting the value the environment id to NONE");
                environmentId = 383;
            }

            int result = new CompInstanceDataService().updateInstanceEnvDetails(instance.getInstanceId(), instance.getInstanceIdentifier(),
                    environmentId , updateTime, userId, accId);

            if (result == 0) {
                log.warn("No rows updated for instance Environment update request for [{}]", instance.getInstanceIdentifier());
            } else if (result == -1) {
                log.error("Error while updating instance Environment in 'comp_instance' table for [{}]", instance.getInstanceIdentifier());
                throw new DataProcessingException("Error while updating instance Environment for " + instance.getInstanceIdentifier());
            } else {
                log.trace(result + " number of rows updated successfully for instance Environment update request for [{}]",
                        instance.getInstanceIdentifier());
            }
        } catch (Exception e) {
            log.error("Exception while updating Instance Environment. Reason:-", e);
            throw new DataProcessingException("Exception while updating Instance Environment for " + instance.getInstanceIdentifier());
        }
    }

    private void instanceDetailsUpdate(UpdateInstancePojo instance, String updateTime, String userId, int accId) throws DataProcessingException {
        try {
            int result = componentInstanceDao.updateInstanceName(instance.getInstanceId(), instance.getInstanceIdentifier(),
                    instance.getName(), updateTime, userId, accId);
            if (result == 0) {
                log.warn("No rows updated for instance name update request for [{}].", instance.getInstanceIdentifier());
            } else if (result == -1) {
                log.error("Error while updating instance name in 'comp_instance' table for [{}].", instance.getInstanceIdentifier());
                throw new DataProcessingException("Error while updating instance name for " + instance.getInstanceIdentifier());
            } else {
                log.trace(result + " number of rows updated successfully for instance name update request for [{}].", instance.getInstanceIdentifier());
            }
        } catch (Exception e) {
            log.error("Exception while updating Instance name. Reason:-", e);
            throw new DataProcessingException("Exception while updating Instance name for " + instance.getInstanceIdentifier());
        }
    }

    /**
     * Updates instance name in database
     */
    private void updateInstanceName(UpdateInstancePojo request, String updateTime, String userId, int accountId) throws Exception {
        int result = compInstanceDao.updateInstanceName(
                request.getInstanceId(), request.getInstanceIdentifier(),
                request.getName(), updateTime, userId, accountId);
        
        if (result <= 0) {
            throw new HealControlCenterException("Failed to update instance name for: " + request.getInstanceIdentifier());
        }
        
        log.debug("Updated instance name for: {}", request.getInstanceIdentifier());
    }

    /**
     * Updates instance environment in database
     */
    private void updateInstanceEnvironment(UpdateInstancePojo request, String updateTime, String userId, int accountId) throws Exception {
        int environmentId = getEnvironmentId(request.getEnvironment());
        
        int result = compInstanceDao.updateInstanceEnvironment(
                request.getInstanceId(), request.getInstanceIdentifier(),
                environmentId, updateTime, userId, accountId);
        
        if (result <= 0) {
            throw new HealControlCenterException("Failed to update instance environment for: " + request.getInstanceIdentifier());
        }
        
        log.debug("Updated instance environment for: {}", request.getInstanceIdentifier());
    }

    /**
     * Updates application service mappings (add/remove services)
     */
    private void updateApplicationServiceMappings(UpdateInstancePojo request, ComponentInstanceBean instanceBean, 
                                                 String updateTime, String userId, int accountId) throws Exception {
        final int controllerId = masterDataDao.getTagDetails(Constants.CONTROLLER_TAG, 1).getId();
        
        for (UpdateInstancePojo.ApplicationServiceMapping appMapping : request.getApplication()) {
            for (UpdateInstancePojo.IdAction serviceAction : appMapping.getService()) {
                if ("Add".equalsIgnoreCase(serviceAction.getAction())) {
                    addServiceMapping(instanceBean, serviceAction, controllerId, updateTime, userId, accountId);
                } else if ("Remove".equalsIgnoreCase(serviceAction.getAction())) {
                    removeServiceMapping(instanceBean, serviceAction, controllerId, accountId);
                }
            }
        }
    }

    /**
     * Adds service mapping for component instance
     */
    private void addServiceMapping(ComponentInstanceBean instanceBean, UpdateInstancePojo.IdAction serviceAction,
                                  int controllerId, String updateTime, String userId, int accountId) throws Exception {
        // Implementation for adding service mapping
        // This would involve cluster creation/mapping similar to appsone-controlcenter
        log.debug("Adding service mapping for instance: {} to service: {}", 
                instanceBean.getIdentifier(), serviceAction.getIdentifier());
    }

    /**
     * Removes service mapping for component instance
     */
    private void removeServiceMapping(ComponentInstanceBean instanceBean, UpdateInstancePojo.IdAction serviceAction,
                                     int controllerId, int accountId) throws Exception {
        // Implementation for removing service mapping
        log.debug("Removing service mapping for instance: {} from service: {}", 
                instanceBean.getIdentifier(), serviceAction.getIdentifier());
    }

    /**
     * Updates Redis cache with updated instance details
     */
    private void updateRedisCache(List<UpdateInstancePojo> updateRequests, String accountIdentifier, 
                                 String updateTime, List<Integer> agentIds) {
        // Update instance details in Redis following appsone-controlcenter pattern
        Map<Integer, CompInstClusterDetails> instanceDetailsMap = instanceRepo.getInstances(accountIdentifier)
                .stream().collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));

        for (UpdateInstancePojo request : updateRequests) {
            updateInstanceInRedis(request, instanceDetailsMap, accountIdentifier, updateTime);
            updateServiceWiseInstanceInRedis(request, accountIdentifier, updateTime);
        }

        // Update agent mappings if needed
        updateAgentMappingsInRedis(updateRequests, agentIds, accountIdentifier, updateTime);
    }

    /**
     * Updates individual instance details in Redis
     */
    private void updateInstanceInRedis(UpdateInstancePojo request, Map<Integer, CompInstClusterDetails> instanceDetailsMap,
                                      String accountIdentifier, String updateTime) {
        CompInstClusterDetails instanceDetails = instanceDetailsMap.get(request.getInstanceId());
        if (instanceDetails != null) {
            if (request.getName() != null) {
                instanceDetails.setName(request.getName());
            }
            if (request.getEnvironment() != null) {
                int environmentId = getEnvironmentId(request.getEnvironment());
                instanceDetails.setIsDR(environmentId);
            }
            instanceDetails.setUpdatedTime(updateTime);
            
            instanceRepo.updateInstanceByIdentifier(accountIdentifier, instanceDetails);
            instanceRepo.updateInstances(accountIdentifier, new ArrayList<>(instanceDetailsMap.values()));
        }
    }

    /**
     * Updates service-wise instance mappings in Redis
     */
    private void updateServiceWiseInstanceInRedis(UpdateInstancePojo request, String accountIdentifier, String updateTime) {
        if (request.getApplication() == null) {
            return;
        }

        // Handle service additions and removals
        for (UpdateInstancePojo.ApplicationServiceMapping appMapping : request.getApplication()) {
            for (UpdateInstancePojo.IdAction serviceAction : appMapping.getService()) {
                if ("Remove".equalsIgnoreCase(serviceAction.getAction())) {
                    removeServiceInstanceFromRedis(request, serviceAction, accountIdentifier);
                } else if ("Add".equalsIgnoreCase(serviceAction.getAction())) {
                    addServiceInstanceToRedis(request, serviceAction, accountIdentifier, updateTime);
                }
            }
        }
    }

    /**
     * Removes service instance mapping from Redis
     */
    private void removeServiceInstanceFromRedis(UpdateInstancePojo request, UpdateInstancePojo.IdAction serviceAction, String accountIdentifier) {
        List<BasicInstanceBean> serviceInstances = serviceRepo.getServiceInstances(accountIdentifier, serviceAction.getIdentifier());
        serviceInstances.removeIf(instance -> instance.getId() == request.getInstanceId());
        serviceRepo.updateServiceInstances(accountIdentifier, serviceAction.getIdentifier(), serviceInstances);
        
        // Update instance-wise services
        List<BasicEntity> services = instanceRepo.getServices(accountIdentifier, request.getInstanceIdentifier());
        services.removeIf(service -> service.getIdentifier().equals(serviceAction.getIdentifier()));
        instanceRepo.updateInstanceWiseServices(accountIdentifier, request.getInstanceIdentifier(), services);
    }

    /**
     * Adds service instance mapping to Redis
     */
    private void addServiceInstanceToRedis(UpdateInstancePojo request, UpdateInstancePojo.IdAction serviceAction, 
                                          String accountIdentifier, String updateTime) {
        // Implementation for adding service instance to Redis
        log.debug("Adding service instance to Redis for instance: {} and service: {}", 
                request.getInstanceIdentifier(), serviceAction.getIdentifier());
    }

    /**
     * Updates agent mappings in Redis
     */
    private void updateAgentMappingsInRedis(List<UpdateInstancePojo> updateRequests, List<Integer> agentIds, 
                                           String accountIdentifier, String updateTime) {
        if (agentIds.isEmpty()) {
            return;
        }

        // Update agent instance mappings following appsone-controlcenter pattern
        for (UpdateInstancePojo request : updateRequests) {
            if (request.getName() != null) {
                updateAgentInstanceMapping(request, accountIdentifier, updateTime);
            }
        }
    }

    /**
     * Updates agent instance mapping details
     */
    private void updateAgentInstanceMapping(UpdateInstancePojo request, String accountIdentifier, String updateTime) {
        // Get instance details to find agent mappings
        CompInstClusterDetails instanceDetails = instanceRepo.getInstanceByIdentifier(accountIdentifier, request.getInstanceIdentifier());
        if (instanceDetails != null && instanceDetails.getAgentIds() != null) {
            for (String agentIdentifier : instanceDetails.getAgentIds()) {
                List<BasicEntity> agentInstanceMappings = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, agentIdentifier);
                BasicEntity instanceMapping = agentInstanceMappings.stream()
                        .filter(mapping -> mapping.getId() == request.getInstanceId())
                        .findFirst()
                        .orElse(null);
                
                if (instanceMapping != null) {
                    instanceMapping.setName(request.getName());
                    instanceMapping.setUpdatedTime(updateTime);
                    agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, agentIdentifier, agentInstanceMappings);
                }
            }
        }
    }

    /**
     * Gets environment ID from environment name
     */
    private int getEnvironmentId(String environmentName) {
        try {
            // Implementation to get environment ID from name
            // This would typically query the environment master data
            return masterDataDao.getEnvironmentIdByName(environmentName);
        } catch (Exception e) {
            log.warn("Failed to get environment ID for: {}, using default", environmentName);
            return 383; // Default environment ID as per appsone-controlcenter
        }
    }

    /**
     * Gets agent ID for instance
     */
    private int getAgentIdForInstance(int instanceId) {
        try {
            return compInstanceDao.getAgentIdForInstance(instanceId);
        } catch (Exception e) {
            log.warn("Failed to get agent ID for instance: {}", instanceId);
            return 0;
        }
    }
}
