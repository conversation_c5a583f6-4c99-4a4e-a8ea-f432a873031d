package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.dao.mysql.entity.ComponentInstanceDao;
import com.heal.controlcenter.dao.redis.InstanceRepo;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.dao.redis.AgentRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.InstanceClusterServicePojo;
import com.heal.controlcenter.pojo.UpdateInstancePojo;
import com.heal.controlcenter.pojo.IdPojo;
import com.heal.controlcenter.util.*;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Business Logic for updating component instances.
 * Converted from appsone-controlcenter UpdateInstanceBL to Spring Boot with JDBC.
 */
@Slf4j
@Component
public class UpdateComponentInstanceBL implements BusinessLogic<List<UpdateInstancePojo>, UtilityBean<List<UpdateInstancePojo>>, List<IdPojo>> {

    private final ClientValidationUtils clientValidationUtils;

    private final ServerValidationUtils serverValidationUtils;

    private final ComponentInstanceDao componentInstanceDao;
    
    private final MasterDataDao masterDataDao;
    
    private final TagsDao tagsDao;
    
    private final ControllerDao controllerDao;
    
    private final InstanceRepo instanceRepo;
    
    private final ServiceRepo serviceRepo;
    
    private final AgentRepo agentRepo;

    public UpdateComponentInstanceBL(ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils, CompInstanceDao compInstanceDao, ComponentInstanceDao componentInstanceDao, MasterDataDao masterDataDao, TagsDao tagsDao, ControllerDao controllerDao, InstanceRepo instanceRepo, ServiceRepo serviceRepo, AgentRepo agentRepo) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.componentInstanceDao = componentInstanceDao;
        this.masterDataDao = masterDataDao;
        this.tagsDao = tagsDao;
        this.controllerDao = controllerDao;
        this.instanceRepo = instanceRepo;
        this.serviceRepo = serviceRepo;
        this.agentRepo = agentRepo;
    }

    @Override
    public UtilityBean<List<UpdateInstancePojo>> clientValidation(Object... arguments) throws ClientException {
        List<UpdateInstancePojo> requestBody = (List<UpdateInstancePojo>) arguments[0];
        String accountIdentifier = (String) arguments[1];
        BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

        // Validate account identifier
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        // Validate request body
        if (requestBody == null) {
            log.error(UIMessages.REQUEST_NULL);
            throw new ClientException(UIMessages.REQUEST_NULL);
        }

        if (requestBody.isEmpty()) {
            log.error(UIMessages.INVALID_REQUEST_BODY);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }

        requestBody = requestBody.parallelStream().distinct().collect(Collectors.toList());

        for (UpdateInstancePojo data : requestBody) {
            if (!data.isValid()) {
                log.error("Input Validation failure for Update Instance Details.");
                throw new ClientException("Input Validation failure for Update Instance Details.");
            }
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);
        log.debug("[clientValidation] Validation successful for accountIdentifier: {}", accountIdentifier);

        return UtilityBean.<List<UpdateInstancePojo>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(requestBody)
                .metadata(new HashMap<>())
                .basicUserDetails(basicUserDetails)
                .build();
    }

    @Override
    public UtilityBean<List<UpdateInstancePojo>> serverValidation(UtilityBean<List<UpdateInstancePojo>> utilityBean) throws ServerException {
        try {
        log.trace("Starting server validation for update component instances");

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        List<UpdateInstancePojo> requests = utilityBean.getPojoObject();
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        if (account == null) {
            throw new ServerException("Account not found with identifier: " + accountIdentifier);
        }

        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);

        if (StringUtils.isEmpty(userId)) {
            throw new ServerException("User ID is required for update operations");
        }

        for (UpdateInstancePojo instance : requests) {
            requestBodyServerValidation(instance, account.getId());
        }

        utilityBean.getMetadata().put(Constants.ACCOUNT, account);

            return UtilityBean.<List<UpdateInstancePojo>>builder()
                    .requestParams(utilityBean.getRequestParams())
                    .pojoObject(requests)
                    .metadata(utilityBean.getMetadata())
                    .build();
        } catch (Exception e) {
            log.error("Server validation failed: {}", e.getMessage(), e);
            throw new ServerException("Server validation failed: " + e.getMessage());
        }
    }

    private void requestBodyServerValidation(UpdateInstancePojo instance, int accId) throws ServerException, HealControlCenterException {
        ControllerDataService controllerDataService = new ControllerDataService();

        /*
        Instance details check
         */
        ComponentInstanceBean instanceBean = componentInstanceDao.
                getComponentInstanceByIdAndIdentifierAndAccount(instance.getInstanceId(), instance.getInstanceIdentifier(), accId);
        if (instanceBean == null) {
            log.error("Instance with the id [{}] and identifier [{}] is unavailable for this account.", instance.getInstanceId(), instance.getInstanceIdentifier());
            throw new ServerException(String.format("Instance with id [%s] and identifier [%s] is unavailable for this account.",
                    instance.getInstanceId(), instance.getInstanceIdentifier()));
        }

        List<MasterComponentTypeBean> masterComponentTypeBeans = masterDataDao.getMasterComponentTypesData(accId);

        MasterComponentTypeBean componentTypeBean = masterComponentTypeBeans.stream().filter(masterComponentTypeBean -> masterComponentTypeBean.getName()
                .equalsIgnoreCase(Constants.COMPONENT_TYPE_HOST)).findAny().orElse(null);


        if (componentTypeBean == null) {
            String err = "Component with type '" + Constants.HOST + "' doesn't exist.";
            log.error(err);
            throw new ServerException(err);
        }

        instance.setHostInstance(componentTypeBean.getId() == instanceBean.getMstComponentTypeId());

        // add a check for host getting updated with env or ip address is already exist.
        // check only when you have a change request for environment id
        if (instance.isHostInstance() && instance.getEnvironment() != null) {
            try {
                int envId = new EnvironmentHelper().getEnvironmentId(instance.getEnvironment());

                int instanceDetailsByHostAddress = componentInstanceDao.getInstanceDetailsByHostAddress(accId, instanceBean.getHostAddress(), envId);

                if (instanceDetailsByHostAddress == -1) {
                    String errMsg = String.format("Exception while getting count of the instances with host_address %s and environment %s for account %s from Data source", instanceBean.getHostAddress(), envId, accId);
                    log.error(errMsg);
                    throw new ServerException(errMsg);
                }

                if (instanceDetailsByHostAddress >= 1) {
                    String err = "Host instance with the same environment and host address already exists.";
                    log.error(err);
                    throw new ServerException(err);
                }
                log.debug("no other host exist in env {} with host address {}",  envId, instanceBean.getHostAddress());

            } catch (DataProcessingException e) {
                String err = "Invalid environment provided";
                log.error(err);
                throw new ServerException(err);
            }
        }
        /*
        Application check
         */
        if (instance.getApplication() != null) {

            for (UpdateInstancePojo.ApplicationServiceMapping applicationData : instance.getApplication()) {

                ControllerBean appBean = controllerDao.getApplicationsById(applicationData.getId(), accId);
                if (appBean == null || !appBean.getIdentifier().equals(applicationData.getIdentifier())) {
                    log.error("Application with the id [{}] and identifier [{}] is unavailable for this account.", applicationData.getId(),
                            applicationData.getIdentifier());
                    throw new ServerException(String.format("Application with id [%s] and identifier [%s] is unavailable for this account.",
                            applicationData.getId(), applicationData.getIdentifier()));
                }

                List<ControllerBean> serviceBeanList = new ArrayList<>();
                try {
                    serviceBeanList = controllerDao.getServicesForAccount(accId);
                } catch (Exception e) {
                    log.error("Exception occurred. Details: ", e);
                }

                List<ViewApplicationServiceMappingBean> viewApplicationServiceMappingBeans =
                        controllerDao.getApplicationServiceMappingWithAppId(accId, applicationData.getId());

                /*
                Service check
                 */
                for (UpdateInstancePojo.IdAction serviceData : applicationData.getService()) {

                    ControllerBean serviceBean = serviceBeanList.stream().filter(c -> c.getId() == serviceData.getId()
                                    && c.getIdentifier().equals(serviceData.getIdentifier()))
                            .findAny()
                            .orElse(null);
                    if (serviceBean == null) {
                        log.error("Service with the id [{}] and identifier [{}] is unavailable for this account.", serviceData.getId(),
                                serviceData.getIdentifier());
                        throw new ServerException(String.format("Service with the id [%s] and identifier [%s] is unavailable for this account.",
                                serviceData.getId(), serviceData.getIdentifier()));
                    }

                    ViewApplicationServiceMappingBean appService = viewApplicationServiceMappingBeans.stream()
                            .filter(c -> c.getApplicationId() == applicationData.getId()
                                    && c.getApplicationIdentifier().equals(applicationData.getIdentifier())
                                    && c.getServiceId() == serviceData.getId()
                                    && c.getServiceIdentifier().equals(serviceData.getIdentifier()))
                            .findAny()
                            .orElse(null);

                    if (appService == null) {
                        log.error("Service with the id [{}] and identifier [{}] is not mapped to Application with id [{}] and " +
                                        "identifier [{}].", serviceData.getId(), serviceData.getIdentifier(),
                                applicationData.getId(), applicationData.getIdentifier());
                        throw new ServerException(String.format("Service with id [%s] and identifier [%s] is not mapped to" +
                                        " Application with id [%s] and identifier [%s].",
                                serviceData.getId(), serviceData.getIdentifier(), applicationData.getId(), applicationData.getIdentifier()));
                    }


                    if (serviceData.getAction().equalsIgnoreCase("Add")) {
                        List<InstanceClusterServicePojo> instanceDetails = componentInstanceDao
                                .getInstanceClusterServiceDetails(serviceData.getId(), accId);
                        /*
                        if host cluster present, then component id and common version id should be same
                         */
                        if (instance.isHostInstance()) {

                            if (instanceDetails != null && !instanceDetails.isEmpty()) {
                                for (InstanceClusterServicePojo singleHostDetails : instanceDetails) {
                                    if (singleHostDetails.getClusterComponentId() != instanceBean.getMstComponentId()
                                            && singleHostDetails.getClusterComponentTypeId() == componentTypeBean.getId()) {
                                        String err = "Host Instance's component is different from existing host " +
                                                "cluster's component for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }

                                    if (singleHostDetails.getClusterCommonVersionId() != instanceBean.getMstCommonVersionId()
                                            && singleHostDetails.getClusterComponentTypeId() == componentTypeBean.getId()) {
                                        String err = "Host Instance's component common version is different from existing host " +
                                                "cluster's component common version for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }
                                }
                            }
                        } else {
                            /*
                            host cluster should be present for comp instance map check
                             */
                            List<InstanceClusterServicePojo> hostClusterDetails = componentInstanceDao
                                    .getClusterServiceDetails(serviceData.getId(), componentTypeBean.getId());
                            if (hostClusterDetails == null || hostClusterDetails.isEmpty()) {
                                String err = "No Host Cluster found while mapping component instance to the service " + serviceData.getName();
                                log.error(err);
                                throw new ServerException(err);
                            }

                            if (instanceDetails != null && !instanceDetails.isEmpty()) {
                                for (InstanceClusterServicePojo singleCompInstanceDetails : instanceDetails) {
                                    if (singleCompInstanceDetails.getClusterComponentId() != instanceBean.getMstComponentId()
                                            && singleCompInstanceDetails.getClusterComponentTypeId() != componentTypeBean.getId()) {
                                        String err = "Component Instance's component is different from existing component " +
                                                "cluster's component for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }

                                    if (singleCompInstanceDetails.getClusterCommonVersionId() != instanceBean.getMstCommonVersionId()
                                            && singleCompInstanceDetails.getClusterComponentTypeId() != componentTypeBean.getId()) {
                                        String err = "Component Instance's component common version is different from existing Component " +
                                                "cluster's component common version for the service id " + serviceData.getId() + ".";
                                        log.error(err);
                                        throw new ServerException(err);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Validates individual update request following appsone-controlcenter pattern
     */
    private void validateUpdateRequest(UpdateInstancePojo request, int accountId) throws ServerException {
        // Validate instance exists
        ComponentInstanceBean instanceBean = compInstanceDao.getComponentInstanceByIdAndIdentifier(
                request.getInstanceId(), request.getInstanceIdentifier(), accountId);
        
        if (instanceBean == null) {
            throw new ServerException(String.format(
                    "Instance with id [%s] and identifier [%s] is unavailable for this account.",
                    request.getInstanceId(), request.getInstanceIdentifier()));
        }

        // Check if it's a host instance
        TagDetailsBean hostComponentType = masterDataDao.getTagDetails(Constants.COMPONENT_TYPE_HOST, 1);
        if (hostComponentType == null) {
            throw new ServerException("Component with type 'Host' doesn't exist.");
        }
        
        request.setHostInstance(hostComponentType.getId() == instanceBean.getMstComponentTypeId());

        // Validate environment update for host instances
        if (request.isHostInstance() && request.getEnvironment() != null) {
            validateHostEnvironmentUpdate(request, instanceBean, accountId);
        }

        // Validate application service mappings
        if (request.getApplication() != null) {
            validateApplicationServiceMappings(request, accountId);
        }
    }

    /**
     * Validates host environment update to prevent conflicts
     */
    private void validateHostEnvironmentUpdate(UpdateInstancePojo request, ComponentInstanceBean instanceBean, int accountId) throws ServerException {
        try {
            int envId = getEnvironmentId(request.getEnvironment());
            
            // Check if another host with same address exists in the target environment
            int existingInstanceCount = compInstanceDao.getInstanceCountByHostAddressAndEnvironment(
                    accountId, instanceBean.getHostAddress(), envId, request.getInstanceId());
            
            if (existingInstanceCount > 0) {
                throw new ServerException("Host instance with the same environment and host address already exists.");
            }
        } catch (Exception e) {
            throw new ServerException("Invalid environment provided: " + request.getEnvironment());
        }
    }

    /**
     * Validates application and service mappings
     */
    private void validateApplicationServiceMappings(UpdateInstancePojo request, int accountId) throws ServerException {
        for (UpdateInstancePojo.ApplicationServiceMapping appMapping : request.getApplication()) {
            // Validate application exists
            Controller application = controllerDao.getControllerById(appMapping.getId(), accountId);
            if (application == null || !application.getIdentifier().equals(appMapping.getIdentifier())) {
                throw new ServerException(String.format(
                        "Application with id [%s] and identifier [%s] is unavailable for this account.",
                        appMapping.getId(), appMapping.getIdentifier()));
            }

            // Validate services
            List<Controller> availableServices = controllerDao.getServicesByAccountId(accountId);
            Map<Integer, Controller> serviceMap = availableServices.stream()
                    .collect(Collectors.toMap(Controller::getId, Function.identity()));

            for (UpdateInstancePojo.IdAction serviceAction : appMapping.getService()) {
                Controller service = serviceMap.get(serviceAction.getId());
                if (service == null || !service.getIdentifier().equals(serviceAction.getIdentifier())) {
                    throw new ServerException(String.format(
                            "Service with id [%s] and identifier [%s] is unavailable for this account.",
                            serviceAction.getId(), serviceAction.getIdentifier()));
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<IdPojo> process(UtilityBean<List<UpdateInstancePojo>> utilityBean) throws DataProcessingException {
        log.trace("Starting process for update component instances");

        try {
            List<UpdateInstancePojo> updateRequests = utilityBean.getPojoObject();
            Account account = (Account) utilityBean.getMetadata().get(Constants.ACCOUNT);
            String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
            String updateTime = DateTimeUtil.getTimeInGMT(System.currentTimeMillis());

            List<IdPojo> results = new ArrayList<>();
            List<Integer> agentIds = new ArrayList<>();

            // Process each update request in transaction
            for (UpdateInstancePojo request : updateRequests) {
                int agentId = updateInstance(request, updateTime, userId, account.getId());
                if (agentId != 0) {
                    agentIds.add(agentId);
                }
                
                results.add(IdPojo.builder()
                        .id(request.getInstanceId())
                        .name(request.getName())
                        .identifier(request.getInstanceIdentifier())
                        .build());
            }

            // Update Redis cache
            updateRedisCache(updateRequests, account.getIdentifier(), updateTime, agentIds);

            return results;
            
        } catch (Exception e) {
            log.error("Error while processing update component instances", e);
            throw new DataProcessingException("Failed to process update component instances: " + e.getMessage());
        }
    }

    /**
     * Updates a single component instance following appsone-controlcenter pattern
     */
    private int updateInstance(UpdateInstancePojo request, String updateTime, String userId, int accountId) throws Exception {
        ComponentInstanceBean instanceBean = compInstanceDao.getComponentInstanceByIdAndIdentifier(
                request.getInstanceId(), request.getInstanceIdentifier(), accountId);
        
        if (instanceBean == null) {
            throw new HealControlCenterException("Instance not found: " + request.getInstanceIdentifier());
        }

        // Update instance name if provided
        if (request.getName() != null) {
            updateInstanceName(request, updateTime, userId, accountId);
        }

        // Update instance environment if provided
        if (request.getEnvironment() != null) {
            updateInstanceEnvironment(request, updateTime, userId, accountId);
        }

        // Get agent ID for host instances
        int agentId = 0;
        if (request.isHostInstance()) {
            agentId = getAgentIdForInstance(instanceBean.getId());
        }

        // Update application service mappings if provided
        if (request.getApplication() != null) {
            updateApplicationServiceMappings(request, instanceBean, updateTime, userId, accountId);
        }

        return agentId;
    }

    /**
     * Updates instance name in database
     */
    private void updateInstanceName(UpdateInstancePojo request, String updateTime, String userId, int accountId) throws Exception {
        int result = compInstanceDao.updateInstanceName(
                request.getInstanceId(), request.getInstanceIdentifier(),
                request.getName(), updateTime, userId, accountId);
        
        if (result <= 0) {
            throw new HealControlCenterException("Failed to update instance name for: " + request.getInstanceIdentifier());
        }
        
        log.debug("Updated instance name for: {}", request.getInstanceIdentifier());
    }

    /**
     * Updates instance environment in database
     */
    private void updateInstanceEnvironment(UpdateInstancePojo request, String updateTime, String userId, int accountId) throws Exception {
        int environmentId = getEnvironmentId(request.getEnvironment());
        
        int result = compInstanceDao.updateInstanceEnvironment(
                request.getInstanceId(), request.getInstanceIdentifier(),
                environmentId, updateTime, userId, accountId);
        
        if (result <= 0) {
            throw new HealControlCenterException("Failed to update instance environment for: " + request.getInstanceIdentifier());
        }
        
        log.debug("Updated instance environment for: {}", request.getInstanceIdentifier());
    }

    /**
     * Updates application service mappings (add/remove services)
     */
    private void updateApplicationServiceMappings(UpdateInstancePojo request, ComponentInstanceBean instanceBean, 
                                                 String updateTime, String userId, int accountId) throws Exception {
        final int controllerId = masterDataDao.getTagDetails(Constants.CONTROLLER_TAG, 1).getId();
        
        for (UpdateInstancePojo.ApplicationServiceMapping appMapping : request.getApplication()) {
            for (UpdateInstancePojo.IdAction serviceAction : appMapping.getService()) {
                if ("Add".equalsIgnoreCase(serviceAction.getAction())) {
                    addServiceMapping(instanceBean, serviceAction, controllerId, updateTime, userId, accountId);
                } else if ("Remove".equalsIgnoreCase(serviceAction.getAction())) {
                    removeServiceMapping(instanceBean, serviceAction, controllerId, accountId);
                }
            }
        }
    }

    /**
     * Adds service mapping for component instance
     */
    private void addServiceMapping(ComponentInstanceBean instanceBean, UpdateInstancePojo.IdAction serviceAction,
                                  int controllerId, String updateTime, String userId, int accountId) throws Exception {
        // Implementation for adding service mapping
        // This would involve cluster creation/mapping similar to appsone-controlcenter
        log.debug("Adding service mapping for instance: {} to service: {}", 
                instanceBean.getIdentifier(), serviceAction.getIdentifier());
    }

    /**
     * Removes service mapping for component instance
     */
    private void removeServiceMapping(ComponentInstanceBean instanceBean, UpdateInstancePojo.IdAction serviceAction,
                                     int controllerId, int accountId) throws Exception {
        // Implementation for removing service mapping
        log.debug("Removing service mapping for instance: {} from service: {}", 
                instanceBean.getIdentifier(), serviceAction.getIdentifier());
    }

    /**
     * Updates Redis cache with updated instance details
     */
    private void updateRedisCache(List<UpdateInstancePojo> updateRequests, String accountIdentifier, 
                                 String updateTime, List<Integer> agentIds) {
        // Update instance details in Redis following appsone-controlcenter pattern
        Map<Integer, CompInstClusterDetails> instanceDetailsMap = instanceRepo.getInstances(accountIdentifier)
                .stream().collect(Collectors.toMap(CompInstClusterDetails::getId, Function.identity()));

        for (UpdateInstancePojo request : updateRequests) {
            updateInstanceInRedis(request, instanceDetailsMap, accountIdentifier, updateTime);
            updateServiceWiseInstanceInRedis(request, accountIdentifier, updateTime);
        }

        // Update agent mappings if needed
        updateAgentMappingsInRedis(updateRequests, agentIds, accountIdentifier, updateTime);
    }

    /**
     * Updates individual instance details in Redis
     */
    private void updateInstanceInRedis(UpdateInstancePojo request, Map<Integer, CompInstClusterDetails> instanceDetailsMap,
                                      String accountIdentifier, String updateTime) {
        CompInstClusterDetails instanceDetails = instanceDetailsMap.get(request.getInstanceId());
        if (instanceDetails != null) {
            if (request.getName() != null) {
                instanceDetails.setName(request.getName());
            }
            if (request.getEnvironment() != null) {
                int environmentId = getEnvironmentId(request.getEnvironment());
                instanceDetails.setIsDR(environmentId);
            }
            instanceDetails.setUpdatedTime(updateTime);
            
            instanceRepo.updateInstanceByIdentifier(accountIdentifier, instanceDetails);
            instanceRepo.updateInstances(accountIdentifier, new ArrayList<>(instanceDetailsMap.values()));
        }
    }

    /**
     * Updates service-wise instance mappings in Redis
     */
    private void updateServiceWiseInstanceInRedis(UpdateInstancePojo request, String accountIdentifier, String updateTime) {
        if (request.getApplication() == null) {
            return;
        }

        // Handle service additions and removals
        for (UpdateInstancePojo.ApplicationServiceMapping appMapping : request.getApplication()) {
            for (UpdateInstancePojo.IdAction serviceAction : appMapping.getService()) {
                if ("Remove".equalsIgnoreCase(serviceAction.getAction())) {
                    removeServiceInstanceFromRedis(request, serviceAction, accountIdentifier);
                } else if ("Add".equalsIgnoreCase(serviceAction.getAction())) {
                    addServiceInstanceToRedis(request, serviceAction, accountIdentifier, updateTime);
                }
            }
        }
    }

    /**
     * Removes service instance mapping from Redis
     */
    private void removeServiceInstanceFromRedis(UpdateInstancePojo request, UpdateInstancePojo.IdAction serviceAction, String accountIdentifier) {
        List<BasicInstanceBean> serviceInstances = serviceRepo.getServiceInstances(accountIdentifier, serviceAction.getIdentifier());
        serviceInstances.removeIf(instance -> instance.getId() == request.getInstanceId());
        serviceRepo.updateServiceInstances(accountIdentifier, serviceAction.getIdentifier(), serviceInstances);
        
        // Update instance-wise services
        List<BasicEntity> services = instanceRepo.getServices(accountIdentifier, request.getInstanceIdentifier());
        services.removeIf(service -> service.getIdentifier().equals(serviceAction.getIdentifier()));
        instanceRepo.updateInstanceWiseServices(accountIdentifier, request.getInstanceIdentifier(), services);
    }

    /**
     * Adds service instance mapping to Redis
     */
    private void addServiceInstanceToRedis(UpdateInstancePojo request, UpdateInstancePojo.IdAction serviceAction, 
                                          String accountIdentifier, String updateTime) {
        // Implementation for adding service instance to Redis
        log.debug("Adding service instance to Redis for instance: {} and service: {}", 
                request.getInstanceIdentifier(), serviceAction.getIdentifier());
    }

    /**
     * Updates agent mappings in Redis
     */
    private void updateAgentMappingsInRedis(List<UpdateInstancePojo> updateRequests, List<Integer> agentIds, 
                                           String accountIdentifier, String updateTime) {
        if (agentIds.isEmpty()) {
            return;
        }

        // Update agent instance mappings following appsone-controlcenter pattern
        for (UpdateInstancePojo request : updateRequests) {
            if (request.getName() != null) {
                updateAgentInstanceMapping(request, accountIdentifier, updateTime);
            }
        }
    }

    /**
     * Updates agent instance mapping details
     */
    private void updateAgentInstanceMapping(UpdateInstancePojo request, String accountIdentifier, String updateTime) {
        // Get instance details to find agent mappings
        CompInstClusterDetails instanceDetails = instanceRepo.getInstanceByIdentifier(accountIdentifier, request.getInstanceIdentifier());
        if (instanceDetails != null && instanceDetails.getAgentIds() != null) {
            for (String agentIdentifier : instanceDetails.getAgentIds()) {
                List<BasicEntity> agentInstanceMappings = agentRepo.getAgentInstanceMappingDetails(accountIdentifier, agentIdentifier);
                BasicEntity instanceMapping = agentInstanceMappings.stream()
                        .filter(mapping -> mapping.getId() == request.getInstanceId())
                        .findFirst()
                        .orElse(null);
                
                if (instanceMapping != null) {
                    instanceMapping.setName(request.getName());
                    instanceMapping.setUpdatedTime(updateTime);
                    agentRepo.updateAgentInstanceMappingDetails(accountIdentifier, agentIdentifier, agentInstanceMappings);
                }
            }
        }
    }

    /**
     * Gets environment ID from environment name
     */
    private int getEnvironmentId(String environmentName) {
        try {
            // Implementation to get environment ID from name
            // This would typically query the environment master data
            return masterDataDao.getEnvironmentIdByName(environmentName);
        } catch (Exception e) {
            log.warn("Failed to get environment ID for: {}, using default", environmentName);
            return 383; // Default environment ID as per appsone-controlcenter
        }
    }

    /**
     * Gets agent ID for instance
     */
    private int getAgentIdForInstance(int instanceId) {
        try {
            return compInstanceDao.getAgentIdForInstance(instanceId);
        } catch (Exception e) {
            log.warn("Failed to get agent ID for instance: {}", instanceId);
            return 0;
        }
    }
}
