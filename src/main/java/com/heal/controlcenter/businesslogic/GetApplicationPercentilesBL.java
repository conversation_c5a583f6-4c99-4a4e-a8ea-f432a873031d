package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.util.ConfProperties;
import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.ApplicationPercentilesBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.ApplicationNotifAndPercentileDao;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ApplicationPercentilePojo;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetApplicationPercentilesBL implements BusinessLogic<String, UtilityBean<String>, List<ApplicationPercentilePojo>> {

    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final AccountsDao accountsDao;
    private final ControllerDao controllerDao;
    private final ApplicationNotifAndPercentileDao applicationNotifAndPercentileDao;

    public GetApplicationPercentilesBL(ClientValidationUtils clientValidationUtils,
                                       ServerValidationUtils serverValidationUtils,
                                       AccountsDao accountsDao,
                                       ControllerDao controllerDao,
                                       ApplicationNotifAndPercentileDao applicationNotifAndPercentileDao) {
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.accountsDao = accountsDao;
        this.controllerDao = controllerDao;
        this.applicationNotifAndPercentileDao = applicationNotifAndPercentileDao;
    }

    /**
     * Performs client-side validation for fetching application percentiles.
     * Validates the account identifier and application identifier.
     *
     * @param arguments Variable arguments: accountIdentifier, applicationId, BasicUserDetails.
     * @return A UtilityBean containing the validated parameters.
     * @throws ClientException if validation fails.
     */
    @LogExecutionAnnotation
    @Override
    public UtilityBean<String> clientValidation(Object... arguments) throws ClientException {
        try {
            if (arguments.length < 3) { // accountIdentifier, applicationId, BasicUserDetails
                log.error("[clientValidation] Insufficient parameters. Expected accountIdentifier, applicationId and BasicUserDetails.");
                throw new ClientException(UIMessages.INSUFFICIENT_PARAMETERS);
            }

            String accountIdentifier = (String) arguments[0];
            String applicationId = (String) arguments[1];

            clientValidationUtils.accountIdentifierValidation(accountIdentifier);
            clientValidationUtils.applicationIdValidation(applicationId);

            Map<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            requestParamsMap.put(Constants.APPLICATION_ID, applicationId);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            log.info("[clientValidation] Client validation successful for accountIdentifier: {} and applicationId: {}", accountIdentifier, applicationId);
            return UtilityBean.<String>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(null)
                    .metadata(new HashMap<>())
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Performs server-side validation for fetching application percentiles.
     * Validates the account and application existence.
     *
     * @param utilityBean UtilityBean containing accountIdentifier and applicationId.
     * @return UtilityBean with validated account and application IDs in metadata.
     * @throws ServerException if validation fails.
     */
    @LogExecutionAnnotation
    @Override
    public UtilityBean<String> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        log.debug("[serverValidation] Start - utilityBean: {}", utilityBean);
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        String applicationId = utilityBean.getRequestParams().get(Constants.APPLICATION_ID);
        String userId = String.valueOf(utilityBean.getMetadata().get(Constants.USER_ID_KEY));

        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();

        try {
            if (!controllerDao.isApplicationExists(Integer.parseInt(applicationId), accountId)) {
                log.error("[serverValidation] Application with id [{}] does not exist for account [{}]", applicationId, accountIdentifier);
                throw new ServerException(UIMessages.APPLICATION_NOT_FOUND);
            }
        } catch (HealControlCenterException e) {
            log.error("[serverValidation] Error checking application existence: {}", e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }

        utilityBean.getMetadata().put(Constants.ACCOUNT_ID, accountId);
        utilityBean.getMetadata().put(Constants.APPLICATION_ID, Integer.parseInt(applicationId));
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userId);

        log.info("[serverValidation] Server validation successful for accountId: {} and applicationId: {}", accountId, applicationId);
        return utilityBean;
    }

    /**
     * Processes the request to fetch application percentiles.
     *
     * @param utilityBean UtilityBean containing validated accountId and applicationId.
     * @return List of ApplicationPercentilePojo.
     * @throws DataProcessingException if data retrieval fails.
     */
    @LogExecutionAnnotation
    @Override
    public List<ApplicationPercentilePojo> process(UtilityBean<String> utilityBean) throws DataProcessingException {
        log.debug("[process] Start - utilityBean: {}", utilityBean);
        int accountId = (int) utilityBean.getMetadata().get(Constants.ACCOUNT_ID);
        int applicationId = (int) utilityBean.getMetadata().get(Constants.APPLICATION_ID);

        try {
            List<ApplicationPercentilesBean> percentiles = applicationNotifAndPercentileDao.getApplicationPercentiles(applicationId, accountId);
            log.info("[process] Fetched {} percentiles for applicationId: {} and accountId: {}", percentiles.size(), applicationId, accountId);

            Map<Integer, Integer> percentileMapping = parsePercentileMapping(ConfProperties.getString(Constants.APPLICATION_PERCENTILES_MAPPING));

            return percentiles.stream()
                    .map(p -> ApplicationPercentilePojo.builder()
                            .id(p.getId())
                            .applicationId(p.getApplicationId())
                            .accountId(p.getAccountId())
                            .displayName(p.getDisplayName())
                            .percentileValue(percentileMapping.getOrDefault(p.getMstKpiDetailsId(), 0)) // Map mstKpiDetailsId back to percentileValue
                            .createdTime(p.getCreatedTime())
                            .updatedTime(p.getUpdatedTime())
                            .userDetailsId(p.getUserDetailsId())
                            .build())
                    .collect(Collectors.toList());
        } catch (HealControlCenterException e) {
            log.error("[process] Error fetching application percentiles for applicationId [{}] and accountId [{}]: {}", applicationId, accountId, e.getMessage(), e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    /**
     * Parses the percentile mapping string from conf.properties into a Map.
     * Expected format: "[mstKpiId-percentileValue],[mstKpiId-percentileValue],..."
     *
     * @param mappingString The string to parse.
     * @return A map where key is mstKpiDetailsId and value is percentileValue.
     */
    private Map<Integer, Integer> parsePercentileMapping(String mappingString) {
        Map<Integer, Integer> mapping = new HashMap<>();
        if (mappingString == null || mappingString.trim().isEmpty()) {
            log.warn("[parsePercentileMapping] Percentile mapping string is empty or null.");
            return mapping;
        }

        String[] entries = mappingString.split("],\\[");
        for (String entry : entries) {
            entry = entry.replace("[", "").replace("]", "").trim();
            String[] parts = entry.split("-");
            if (parts.length == 2) {
                try {
                    int mstKpiId = Integer.parseInt(parts[0].trim());
                    int percentileValue = Integer.parseInt(parts[1].trim());
                    mapping.put(mstKpiId, percentileValue);
                } catch (NumberFormatException e) {
                    log.error("[parsePercentileMapping] Invalid number format in mapping entry: {}. Skipping.", entry, e);
                }
            } else {
                log.warn("[parsePercentileMapping] Malformed mapping entry: {}. Skipping.", entry);
            }
        }
        return mapping;
    }
}