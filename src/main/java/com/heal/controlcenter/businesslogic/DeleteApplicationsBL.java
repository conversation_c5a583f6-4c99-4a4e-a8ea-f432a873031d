package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Application;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.redis.ApplicationRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.DeleteApplicationsPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DeleteApplicationsBL implements BusinessLogic<DeleteApplicationsPojo, UtilityBean<List<ControllerBean>>, Object> {

    private final ControllerDao controllerDao;
    private final ApplicationRepo applicationRepo;
    private final UserValidationUtil userValidationUtil;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public DeleteApplicationsBL(ControllerDao controllerDao, ApplicationRepo applicationRepo, UserValidationUtil userValidationUtil,
                              ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.controllerDao = controllerDao;
        this.applicationRepo = applicationRepo;
        this.userValidationUtil = userValidationUtil;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    /**
     * Validates the client request for deleting applications. Checks account identifier and prepares request params and metadata.
     *
     * @param arguments The request body containing application identifiers and delete type.
     * @return UtilityBean containing the validated request and metadata.
     * @throws ClientException if the account identifier is invalid.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<DeleteApplicationsPojo> clientValidation(Object... arguments) throws ClientException {
        try {
            DeleteApplicationsPojo requestBody = (DeleteApplicationsPojo) arguments[0];
            String accountIdentifier = (String) arguments[1];

            log.debug("[clientValidation] Start - accountIdentifier: {}, requestBody: {}", accountIdentifier, requestBody);
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            log.debug("[clientValidation] Request params map created: {}", requestParamsMap);

            Map<String, Object> metadata = new HashMap<>();
            metadata.put(Constants.HARD_DELETE, requestBody.isHardDelete());
            log.debug("[clientValidation] Metadata set: {}", metadata);

            UtilityBean<DeleteApplicationsPojo> result = UtilityBean.<DeleteApplicationsPojo>builder()
                    .requestParams(requestParamsMap)
                    .pojoObject(requestBody)
                    .basicUserDetails(basicUserDetails)
                    .metadata(metadata)
                    .build();
            log.debug("[clientValidation] Returning UtilityBean: {}", result);
            return result;
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Performs server-side validation for deleting applications. Checks user access, application existence, and service mappings.
     *
     * @param utilityBean UtilityBean containing the request and metadata.
     * @return UtilityBean with validated ControllerBeans for deletion.
     * @throws ServerException if validation fails (e.g., application not found, still mapped to services).
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<List<ControllerBean>> serverValidation(UtilityBean<DeleteApplicationsPojo> utilityBean) throws ServerException {
        log.debug("[serverValidation] Start - utilityBean: {}", utilityBean);
        String userId = utilityBean.getMetadata().get(Constants.USER_ID_KEY).toString();
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        log.debug("[serverValidation] userId: {}, accountIdentifier: {}", userId, accountIdentifier);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        int accountId = account.getId();

        List<String> applicationIdentifiers = utilityBean.getPojoObject().getApplicationIdentifiers();
        boolean hardDelete = (boolean) utilityBean.getMetadata().get(Constants.HARD_DELETE);

        log.debug("[serverValidation] hardDelete: {}", hardDelete);
        log.debug("[serverValidation] Application identifiers: {}", applicationIdentifiers);

        List<ControllerBean> controllerBeanList = new ArrayList<>();

        for (String app : applicationIdentifiers) {
            log.debug("[serverValidation] Validating application: {}", app);

            List<ControllerBean> accessibleApplications;
            try {
                accessibleApplications = userValidationUtil.getAccessibleApplicationsForUser(userId, accountIdentifier);
                log.debug("[serverValidation] Accessible applications fetched for userId {}: {}", userId, accessibleApplications);
            } catch (HealControlCenterException e) {
                log.error("[serverValidation] Error fetching accessible applications for userId {}: {}", userId, e.getMessage(), e);
                throw new ServerException(e.getMessage());
            }
            log.debug("[serverValidation] Accessible applications identifiers: {}", accessibleApplications.stream().map(ControllerBean::getIdentifier).collect(Collectors.toList()));

            ControllerBean controller = accessibleApplications.parallelStream()
                    .filter(c -> c.getIdentifier().equals(app.trim()))
                    .findAny().orElse(null);

            if (controller == null || controller.getStatus() != 1) {
                log.error("Application with Identifier '[{}]' does not exist or is not active.", app);
                throw new ServerException(String.format("Application with Identifier '[%s]' does not exist or is not active.", app));
            }

            List<ViewApplicationServiceMappingBean> applicationServiceList;
            try {
                applicationServiceList = controllerDao.getServicesMappedToApplications(accountId, applicationIdentifiers);
            } catch (HealControlCenterException e) {
                throw new ServerException(e.getMessage());
            }
            log.debug("[serverValidation] applicationServiceList for accountId {}: {}", accountId, applicationServiceList);

            List<ViewApplicationServiceMappingBean> mappedServices = applicationServiceList.parallelStream()
                    .filter(c -> (c.getApplicationIdentifier().equals(app.trim())))
                    .toList();

            log.debug("[serverValidation] Mapped services for '{}': {}", app, mappedServices.size());

            if (!mappedServices.isEmpty()) {
                log.error("Some services are still mapped to Application with Identifier '[{}]'. Please remove the mapped services first.", app);
                throw new ServerException(String.format("Some services are still mapped to Application with Identifier '[%s]'. Please remove the mapped services first.", app));
            }
            controllerBeanList.add(controller);
            log.debug("[serverValidation] Controller added: {}", controller.getIdentifier());
        }

        Map<String, Object> metadata = utilityBean.getMetadata();
        metadata.put(Constants.HARD_DELETE, hardDelete);

        UtilityBean<List<ControllerBean>> result = UtilityBean.<List<ControllerBean>>builder()
                .requestParams(utilityBean.getRequestParams())
                .pojoObject(controllerBeanList)
                .metadata(metadata)
                .build();
        log.debug("[serverValidation] Returning UtilityBean: {}", result);
        return result;
    }

    /**
     * Processes the deletion of applications (hard or soft delete) and updates Redis cache.
     *
     * @param utilityBean UtilityBean containing ControllerBeans to delete and metadata.
     * @return Success message if deletion is successful.
     * @throws DataProcessingException if any error occurs during deletion or Redis update.
     */
    @Override
    @LogExecutionAnnotation
    @Transactional(rollbackFor = {Exception.class})
    public String process(UtilityBean<List<ControllerBean>> utilityBean) throws DataProcessingException {
        log.debug("[process] Start - utilityBean: {}", utilityBean);
        List<ControllerBean> controllerBeanList = utilityBean.getPojoObject();
        boolean hardDelete = (boolean) utilityBean.getMetadata().get(Constants.HARD_DELETE);
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);

        log.info("[process] isHardDelete before processing: {}", hardDelete);
        log.debug("[process] Applications to delete: {}", controllerBeanList.stream().map(ControllerBean::getIdentifier).collect(Collectors.toList()));

        try {
            for (ControllerBean bean : controllerBeanList) {
                log.debug("[process] Deleting application: {} (hardDelete: {})", bean.getIdentifier(), hardDelete);
                if (hardDelete) {
                    log.info("Performing hard delete for application [id: {}, identifier: {}]", bean.getId(), bean.getIdentifier());
                    hardDeleteApplication(bean);
                } else {
                    log.info("Performing soft delete for application [id: {}, identifier: {}]", bean.getId(), bean.getIdentifier());
                    softDeleteApplication(bean);
                }
            }
            log.debug("[process] All applications deleted from DB. Proceeding to delete from Redis.");
            deleteApplicationInRedis(controllerBeanList, accountIdentifier);
            log.info("[process] Redis cleanup successful for accountIdentifier: {}", accountIdentifier);
            return "Application(s) deleted successfully.";
        } catch (HealControlCenterException e) {
            log.error("Error during application deletion process. Details: ", e);
            throw new DataProcessingException("Failed to delete application: " + e.getMessage());
        }
    }

    /**
     * Performs hard delete of an application and all its related mappings in the database.
     *
     * @param bean ControllerBean representing the application to delete.
     * @throws HealControlCenterException if any delete operation fails.
     */
    private void hardDeleteApplication(ControllerBean bean) throws HealControlCenterException {
        log.debug("[hardDeleteApplication] Deleting mappings for application: {}", bean.getIdentifier());

        checkExecutionStatus(controllerDao.deleteApplicationNotificationMappingWithAppId(bean.getId()), bean.getIdentifier(), "application_notification_mapping");
        checkExecutionStatus(controllerDao.deleteApplicationPercentilesWithAppId(bean.getId()), bean.getIdentifier(), "application_percentiles");
        checkExecutionStatus(controllerDao.deleteTagMappingByEntityId(bean.getId(), Constants.CONTROLLER), bean.getIdentifier(), "tag_mapping");
        checkExecutionStatus(controllerDao.deleteApplicationAliasByAppId(bean.getIdentifier()), bean.getIdentifier(), "application_aliases");
        checkExecutionStatus(controllerDao.deleteApplicationAnomalyConfigByAppId(bean.getId()), bean.getIdentifier(), "application_anomaly_configurations");
        checkExecutionStatus(controllerDao.deleteControllerWithId(bean.getId()), bean.getIdentifier(), "controller");
        log.debug("[hardDeleteApplication] Completed hard delete for application: {}", bean.getIdentifier());
    }

    /**
     * Performs soft delete (status update) of an application in the database.
     *
     * @param bean ControllerBean representing the application to soft delete.
     * @throws HealControlCenterException if the update fails.
     */
    private void softDeleteApplication(ControllerBean bean) throws HealControlCenterException {
        log.debug("[softDeleteApplication] Soft deleting application: {}", bean.getIdentifier());

        checkExecutionStatus(controllerDao.softDeleteApplicationById(bean.getId()), bean.getIdentifier(), "controller");
        log.info("Soft deleted application [id: {}, identifier: {}]", bean.getId(), bean.getIdentifier());
        log.debug("[softDeleteApplication] Completed soft delete for application: {}", bean.getIdentifier());
    }

    /**
     * Checks the result of a delete/update operation and throws exception if it failed.
     *
     * @param res Result of the operation (number of rows affected).
     * @param appIdentifier Application identifier.
     * @param tableName Table name where the operation was performed.
     * @throws HealControlCenterException if the operation fails.
     */
    private void checkExecutionStatus(int res, String appIdentifier, String tableName) throws HealControlCenterException {
        log.debug("[checkExecutionStatus] Table: {}, App: {}, Result: {}", tableName, appIdentifier, res);

        if (res == -1) {
            log.error("Error occurred while deleting from '[{}]' table for application with identifier '[{}]'.", tableName, appIdentifier);
            throw new HealControlCenterException(String.format("Error occurred while deleting from '[%s]' table for application with " +
                    "identifier '[%s]'.", tableName, appIdentifier));
        } else if (res == 0) {
            log.warn("No rows affected in [{}] for identifier [{}]", tableName, appIdentifier);
        } else {
            log.debug("[checkExecutionStatus] Successful operation in [{}] for identifier [{}]", tableName, appIdentifier);
        }
    }

    /**
     * Removes deleted applications from Redis and updates the Redis cache for the account.
     *
     * @param applications List of ControllerBeans representing deleted applications.
     * @param accountIdentifier The account identifier.
     * @throws HealControlCenterException if Redis update fails.
     */
    private void deleteApplicationInRedis(List<ControllerBean> applications, String accountIdentifier) throws HealControlCenterException {
        log.debug("[deleteApplicationInRedis] Starting Redis cleanup for accountIdentifier: {}", accountIdentifier);
        List<Application> applicationList = applicationRepo.getApplicationsForAccount(accountIdentifier);
        log.debug("[deleteApplicationInRedis] Current Redis application list: {}", applicationList);
        for (ControllerBean applicationDetail : applications) {
            log.debug("[deleteApplicationInRedis] Deleting application from Redis: {}", applicationDetail.getIdentifier());
            applicationList.removeIf(app -> app.getIdentifier().equalsIgnoreCase(applicationDetail.getIdentifier()));
            applicationRepo.deleteApplication(accountIdentifier, applicationDetail.getIdentifier());
            log.debug("[deleteApplicationInRedis] Deleted application {} from Redis.", applicationDetail.getIdentifier());
        }
        applicationRepo.updateApplicationDetailsForAccount(accountIdentifier, applicationList);
        log.debug("[deleteApplicationInRedis] Updated Redis application list for accountIdentifier: {}", accountIdentifier);
    }
}