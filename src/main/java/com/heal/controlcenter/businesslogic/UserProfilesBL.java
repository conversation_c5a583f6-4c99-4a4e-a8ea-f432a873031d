package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.UserProfileBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.util.ServerValidationUtils;
import com.heal.controlcenter.util.UIMessages;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
public class UserProfilesBL implements BusinessLogic<String, String, List<UserProfileBean>>{

    private final UserDao userRolesAndProfilesDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public UserProfilesBL(UserDao userRolesAndProfilesDao, ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.userRolesAndProfilesDao = userRolesAndProfilesDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }


    @Override
    @LogExecutionAnnotation
    public UtilityBean<String> clientValidation(Object... arguments) throws ClientException {
        try {
            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            return UtilityBean.<String>builder()
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    @Override
    public String serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        return null;
    }

    @Override
    public List<UserProfileBean> process(String bean) throws DataProcessingException {
        try {
            List<UserProfileBean> listofUserProfiles = userRolesAndProfilesDao.getUserProfiles();
            for (UserProfileBean userProfile: listofUserProfiles) {
                Set<String> setOfUserProfileMapping = new HashSet<>(userRolesAndProfilesDao.getAccessProfileMapping(userProfile.getUserProfileId()));
                userProfile.setAccessibleFeatures(setOfUserProfileMapping);
            }
            return listofUserProfiles;
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
    }
}