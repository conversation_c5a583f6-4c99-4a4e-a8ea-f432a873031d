package com.heal.controlcenter.businesslogic;

import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.heal.configuration.entities.KpiCategoryDetailsBean;
import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.*;
import com.heal.controlcenter.beans.CompInstClusterDetailsBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.KPIDao;
import com.heal.controlcenter.dao.mysql.ServiceDao;
import com.heal.controlcenter.dao.mysql.entity.KpiDetailsBean;
import com.heal.controlcenter.dao.mysql.entity.ServiceKpiThreshold;
import com.heal.controlcenter.dao.opensearch.ServiceOpensearchRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.StaticThresholdRules;
import com.heal.controlcenter.service.CommonDataService;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetServiceStaticThresholdBL implements BusinessLogic<Object, UtilityBean<Map<String, Object>>, List<StaticThresholdRules>> {

    private final CommonDataService commonDataService;
    private final KPIDao kpiDao;
    private final ServiceDao serviceDao;
    private final CacheWrapper cacheWrapper;
    private final ServiceOpensearchRepo serviceOpensearchRepo;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetServiceStaticThresholdBL(CommonDataService commonDataService, KPIDao kpiDao, ServiceDao serviceDao,
                                       CacheWrapper cacheWrapper, ServiceOpensearchRepo serviceOpensearchRepo,
                                       ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils) {
        this.commonDataService = commonDataService;
        this.kpiDao = kpiDao;
        this.serviceDao = serviceDao;
        this.cacheWrapper = cacheWrapper;
        this.serviceOpensearchRepo = serviceOpensearchRepo;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<Object> clientValidation(Object... arguments) throws ClientException {
        try {
            String authKey = (String) arguments[0];
            clientValidationUtils.nullOrEmptyCheck(authKey, UIMessages.AUTH_KEY_INVALID);

            String accountIdentifier = (String) arguments[1];
            clientValidationUtils.nullOrEmptyCheck(accountIdentifier, UIMessages.ACCOUNT_IDENTIFIER_INVALID);

            String serviceIdentifier = (String) arguments[2];
            clientValidationUtils.nullOrEmptyCheck(serviceIdentifier, UIMessages.SERVICE_IDENTIFIER_INVALID);

            String kpiType = ((String) arguments[3]).trim();
            clientValidationUtils.nullOrEmptyCheck(kpiType, UIMessages.KPI_TYPE_INVALID);

            String thresholdType = (String) arguments[4];
            if (Objects.isNull(thresholdType) || thresholdType.trim().isEmpty() || !thresholdType.equalsIgnoreCase("static")) {
                log.error("Invalid thresholdType. Reason: thresholdType is undefined in the request.");
                throw new ClientException(UIMessages.THRESHOLD_TYPE_INVALID);
            }

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            requestParamsMap.put(Constants.SERVICE_IDENTIFIER, serviceIdentifier);
            requestParamsMap.put(Constants.KPI_TYPE, kpiType);
            requestParamsMap.put(Constants.THRESHOLD_TYPE, thresholdType);
            requestParamsMap.put(Constants.AUTH_KEY, authKey);

            return UtilityBean.builder()
                    .requestParams(requestParamsMap)
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    @Override
    public UtilityBean<Map<String, Object>> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        //Entry-Exit logging is handled in LoggingAspect

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        String serviceIdentifier = utilityBean.getRequestParams().get(Constants.SERVICE_IDENTIFIER);
        String kpiType = utilityBean.getRequestParams().get(Constants.KPI_TYPE).trim();
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);

        Map<String, Object> metadata = new HashMap<>();

        String userId = serverValidationUtils.authKeyValidation(authKey);

        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        metadata.put(Constants.ACCOUNT, account);

        UserAccessDetails userAccessDetails = serverValidationUtils.userAccessDetailsValidation(userId, accountIdentifier);

        Service service = serverValidationUtils.serviceValidation(userId, accountIdentifier, serviceIdentifier, userAccessDetails);
        metadata.put(Constants.SERVICE, service);

        Map<String, List<ViewTypes>> viewTypes = cacheWrapper.getAllViewTypesIdMap();
        ViewTypes kpi = CommonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.KPI_TYPE, kpiType);
        if (kpi == null || (!kpi.getSubTypeName().equalsIgnoreCase("Availability") && !kpi.getSubTypeName().equalsIgnoreCase("Core"))) {
            log.error("Invalid KPI type [{}]. Reason: KPI type should be one of Availability or Core.", kpiType);
            throw new ServerException(UIMessages.INVALID_KPI_TYPE);
        }
        metadata.put(kpiType, kpi);

        ViewTypes lowThresholdSeverityType = CommonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        if (lowThresholdSeverityType == null) {
            log.error("Invalid thresholds view types. Subtype: {} unavailable", Constants.THRESHOLD_SEVERITY_TYPE_LOW);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_LOW, lowThresholdSeverityType);

        ViewTypes mediumThresholdSeverityType = CommonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        if (mediumThresholdSeverityType == null) {
            log.error("Invalid thresholds view types. Subtype: {} unavailable", Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM, mediumThresholdSeverityType);

        ViewTypes highThresholdSeverityType = CommonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.THRESHOLD_SEVERITY_TYPE, Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
        if (highThresholdSeverityType == null) {
            log.error("Invalid thresholds view types. Subtype: {} unavailable", Constants.THRESHOLD_SEVERITY_TYPE_HIGH);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.THRESHOLD_SEVERITY_TYPE_HIGH, highThresholdSeverityType);

        ViewTypes lessThanOperationType = CommonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_LESSER_THAN);
        if (lessThanOperationType == null) {
            log.error("Invalid operation view types. Subtype: {} unavailable", Constants.OPERATIONS_TYPE_LESSER_THAN);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.OPERATIONS_TYPE_LESSER_THAN, lessThanOperationType);

        ViewTypes greaterThanOperationType = CommonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_GREATER_THAN);
        if (greaterThanOperationType == null) {
            log.error("Invalid operation view types. Subtype: {} unavailable", Constants.OPERATIONS_TYPE_GREATER_THAN);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.OPERATIONS_TYPE_GREATER_THAN, greaterThanOperationType);

        ViewTypes notBetweenOperationType = CommonUtils.getViewTypeByNameAndSubType(viewTypes, Constants.OPERATIONS_TYPE, Constants.OPERATIONS_TYPE_NOT_BETWEEN);
        if (notBetweenOperationType == null) {
            log.error("Invalid operation view types. Subtype: {} unavailable", Constants.OPERATIONS_TYPE_NOT_BETWEEN);
            throw new ServerException("Failure in data validation");
        }
        metadata.put(Constants.OPERATIONS_TYPE_NOT_BETWEEN, notBetweenOperationType);

        return UtilityBean.<Map<String, Object>>builder()
                .requestParams(utilityBean.getRequestParams())
                .pojoObject(metadata)
                .build();
    }

    @Override
    public List<StaticThresholdRules> process(UtilityBean<Map<String, Object>> bean) throws DataProcessingException {
        Map<String, Object> metadata = bean.getPojoObject();
        Service service = (Service) metadata.get(Constants.SERVICE);
        Account account = (Account) metadata.get(Constants.ACCOUNT);
        String kpiType = bean.getRequestParams().get(Constants.KPI_TYPE).trim();
        ViewTypes kpiViewType = (ViewTypes) metadata.get(kpiType);

        try {
            // 1. Fetch and filter data from various sources
            List<KpiDetailsBean> kpiDetailsList = getRelevantKpiDetails(service, account, kpiViewType);
            if(kpiDetailsList.isEmpty()) {
                log.warn("Kpi details unavailable for kpiType:{}, service:{}, account:{}", kpiType, service.getIdentifier(), account.getIdentifier());
                return Collections.emptyList();
            }

            List<ServiceKpiThreshold> kpiThresholdList = getFilteredKpiThresholds(service.getServiceConfiguration(), account.getId(), service.getId(), metadata);
            Map<Integer, List<String>> configuredThresholdsFromOS = getConfiguredThresholdsFromOS(account.getIdentifier(), service.getIdentifier(), kpiThresholdList);

            // 2. Process the data to generate all threshold rules
            Map<String, Set<StaticThresholdRules>> allRules = getStaticThresholdByKpiId(kpiViewType, kpiDetailsList, kpiThresholdList, metadata, configuredThresholdsFromOS);
            if(allRules.isEmpty()) {
                log.error("No rules available for kpiType:{}, service:{}, account:{}", kpiType, service.getIdentifier(), account.getIdentifier());
                return Collections.emptyList();
            }
            // 3. Sync missing rules to OpenSearch
            syncMissingRulesToOpenSearch(account.getIdentifier(), service.getIdentifier(), allRules.getOrDefault(Constants.MISSING_RULES, Collections.emptySet()));

            // 4. Prepare and return the final sorted list
            return prepareFinalResponse(allRules.getOrDefault(Constants.AVAILABLE_RULES, Collections.emptySet()), service.getIdentifier(), account.getIdentifier());

        } catch (HealControlCenterException e) {
            log.error("Error occurred while getting static threshold by kpiId for accountId: {}, serviceId: {}. Details: ", account.getIdentifier(), service.getIdentifier(), e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    /**
     * Fetches all KPI thresholds for a service and filters them based on the service's severity configuration.
     */
    private List<ServiceKpiThreshold> getFilteredKpiThresholds(ServiceConfiguration serviceConfig, int accountId, int serviceId, Map<String, Object> metadata) throws HealControlCenterException {
        ViewTypes lowThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        ViewTypes mediumThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        ViewTypes highThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);

        return serviceDao.getAllKpiThresholds(accountId, serviceId).stream()
                .filter(k -> k.getOperationTypeId() > 0)
                .filter(k -> (serviceConfig.isLowEnable() && k.getThresholdSeverityId() == lowThreshold.getSubTypeId())
                        || (serviceConfig.isMediumEnable() && k.getThresholdSeverityId() == mediumThreshold.getSubTypeId())
                        || (serviceConfig.isHighEnable() && k.getThresholdSeverityId() == highThreshold.getSubTypeId()))
                .toList();
    }

    /**
     * Fetches the currently configured thresholds from OpenSearch for a given set of KPIs.
     */
    private Map<Integer, List<String>> getConfiguredThresholdsFromOS(String accountIdentifier, String serviceIdentifier, List<ServiceKpiThreshold> kpiThresholdList) throws HealControlCenterException {
        Set<String> thresholdKpiStrIds = kpiThresholdList.stream()
                .map(ServiceKpiThreshold::getKpiId)
                .map(String::valueOf)
                .collect(Collectors.toSet());

        TabularResults osResults = serviceOpensearchRepo.getAllConfigureThresholdsKpiListsFromOS(accountIdentifier, serviceIdentifier, thresholdKpiStrIds);
        return extractResult(osResults);
    }

    /**
     * Gathers all KPI definitions that are relevant to the components of a given service.
     */
    private List<KpiDetailsBean> getRelevantKpiDetails(Service service, Account account, ViewTypes kpiViewType) throws HealControlCenterException {
        List<CompInstClusterDetailsBean> serviceCompInstances;
        // Fetch all KPI details once and group them for efficient lookup
        Map<String, List<KpiDetailsBean>> kpiDetailsMap;
        try {
            serviceCompInstances = commonDataService.getComponentClusterList(service.getId(), account.getId());
            if (serviceCompInstances.isEmpty()) {
                log.warn("Component instances unavailable for the serviceId [{}] provided.", service.getId());
                return Collections.emptyList();
            }
            kpiDetailsMap = kpiDao.getAllKpiDetailsKpiList().stream()
                    .collect(Collectors.groupingBy(kpi -> kpi.getCommonVersionId() + "#" + kpi.getComponentId()));
        } catch (HealControlCenterException e) {
            throw new HealControlCenterException(e.getMessage());
        }

        // Map component instances to their relevant KPIs and flatten the list
        return serviceCompInstances.stream()
                .map(compInstance -> {
                    String key = compInstance.getCommonVersionId() + "#" + compInstance.getCompId();
                    List<KpiDetailsBean> kpiBeansForComp = kpiDetailsMap.get(key);
                    if (kpiBeansForComp == null || kpiBeansForComp.isEmpty()) {
                        log.warn("KPI bean list unavailable for commonVersionId: {}, compId: {}", compInstance.getCommonVersionId(), compInstance.getCompId());
                        return Collections.<KpiDetailsBean>emptyList();
                    }

                    return kpiBeansForComp.stream()
                            .filter(kpi -> kpi.getTypeId() == kpiViewType.getSubTypeId())
                            .toList();
                })
                .flatMap(Collection::stream)
                .toList();
    }

    /**
     * Handles the side-effect of inserting any missing threshold rules into OpenSearch.
     */
    private void syncMissingRulesToOpenSearch(String accountIdentifier, String serviceIdentifier, Set<StaticThresholdRules> missingRules) throws HealControlCenterException {
        if (missingRules.isEmpty()) {
            return;
        }

        Timestamp echoMilli = DateTimeUtil.getCurrentTimestampInGMT();
        long startTime = System.currentTimeMillis();
        serviceOpensearchRepo.insertBulkServiceKpiThresholdsIntoOS(accountIdentifier, serviceIdentifier, List.copyOf(missingRules), echoMilli, "");
        log.debug("Total time taken to insert missing thresholds into OS: {} ms", (System.currentTimeMillis() - startTime));
    }

    /**
     * Sorts the final list of rules and logs the count before returning.
     */
    private List<StaticThresholdRules> prepareFinalResponse(Set<StaticThresholdRules> availableRules, String serviceIdentifier, String accountIdentifier) {
        if (availableRules.isEmpty()) {
            return Collections.emptyList();
        }

        List<StaticThresholdRules> sortedRules = availableRules.stream()
                .sorted(Comparator.comparing(StaticThresholdRules::getCategoryName)
                        .thenComparing(StaticThresholdRules::getKpiName)
                        .thenComparing(StaticThresholdRules::getKpiLevel))
                .toList();

        log.info("Static threshold rules count: {}, service: {}, account: {}", sortedRules.size(), serviceIdentifier, accountIdentifier);
        return sortedRules;
    }

    private Map<String, Set<StaticThresholdRules>> getStaticThresholdByKpiId(ViewTypes kpiType, List<KpiDetailsBean> kpiDetailsBeanList,
                                                                             List<ServiceKpiThreshold> kpiThresholdList, Map<String, Object> metadata,
                                                                             Map<Integer, List<String>> configuredThresholdsFromOS) throws HealControlCenterException {

        Map<String, Set<StaticThresholdRules>> output = new HashMap<>();
        output.put(Constants.AVAILABLE_RULES, new HashSet<>());
        output.put(Constants.MISSING_RULES, new HashSet<>());

        // Pre-computation: Group thresholds and categories by KPI ID for efficient lookups.
        Map<Integer, List<ServiceKpiThreshold>> kpiIdToThresholdsMap = kpiThresholdList.stream()
                .collect(Collectors.groupingBy(ServiceKpiThreshold::getKpiId));

        // Using a merge function is a robust way to handle potential duplicate keys from the database.
        Map<Integer, KpiCategoryDetailsBean> kpiCategoryDetailsMap = kpiDao.getAllKpiCategoryDetails().stream()
                .collect(Collectors.toMap(KpiCategoryDetailsBean::getKpiId, cat -> cat, (existing, replacement) -> existing));

        // Fetch severity types once to avoid repeated lookups in the loop.
        ViewTypes lowThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_LOW);
        ViewTypes mediumThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_MEDIUM);
        ViewTypes highThreshold = (ViewTypes) metadata.get(Constants.THRESHOLD_SEVERITY_TYPE_HIGH);

        // Process each KPI detail, delegating the complex logic to helper methods.
        for (KpiDetailsBean kpiDetailsBean : kpiDetailsBeanList) {
            KpiCategoryDetailsBean kpiCategoryDetailBean = kpiCategoryDetailsMap.get(kpiDetailsBean.getId());
            if (kpiCategoryDetailBean == null) {
                log.error("Category details unavailable for kpi: {}", kpiDetailsBean);
                continue;
            }

            List<ServiceKpiThreshold> associatedThresholds = kpiIdToThresholdsMap.getOrDefault(kpiDetailsBean.getId(), Collections.emptyList());

            Map<String, Set<StaticThresholdRules>> deltaOutput = processKpiDetail(kpiDetailsBean, kpiCategoryDetailBean, associatedThresholds, kpiType.getSubTypeName(),
                    configuredThresholdsFromOS, lowThreshold, mediumThreshold, highThreshold);

            // This adds all new rules from the deltaOutput into the main 'output' collections.
            output.get(Constants.AVAILABLE_RULES).addAll(deltaOutput.getOrDefault(Constants.AVAILABLE_RULES, Collections.emptySet()));
            output.get(Constants.MISSING_RULES).addAll(deltaOutput.getOrDefault(Constants.MISSING_RULES, Collections.emptySet()));
        }

        return output;
    }

    /**
     * Processes a single KPI, generating its configured and placeholder threshold rules.
     */
    private Map<String, Set<StaticThresholdRules>> processKpiDetail(KpiDetailsBean kpiDetailsBean, KpiCategoryDetailsBean kpiCategoryDetailBean,
                                  List<ServiceKpiThreshold> kpiThresholds, String kpiTypeName,
                                  Map<Integer, List<String>> configuredThresholdsFromOS,
                                  ViewTypes lowThreshold, ViewTypes mediumThreshold, ViewTypes highThreshold) throws HealControlCenterException {

        if (kpiThresholds.isEmpty()) {
            // If no thresholds are configured for this KPI, create default placeholder rules.
            Set<StaticThresholdRules> placeholderRules = createAndAddPlaceholderRules(kpiDetailsBean, kpiCategoryDetailBean, kpiTypeName);
            return Map.of(Constants.AVAILABLE_RULES, placeholderRules);
        }
        // If thresholds are configured, process them.
        return processConfiguredThresholds(kpiDetailsBean, kpiCategoryDetailBean, kpiThresholds, kpiTypeName,
                configuredThresholdsFromOS, lowThreshold, mediumThreshold, highThreshold);
    }

    /**
     * Creates default placeholder rules for a KPI that has no configured thresholds.
     */
    private Set<StaticThresholdRules> createAndAddPlaceholderRules(KpiDetailsBean kpiDetailsBean, KpiCategoryDetailsBean kpiCategoryDetailBean, String kpiTypeName) {
        log.debug("KpiId:{} is not present in the service kpi threshold. Placeholder entry will be created.", kpiDetailsBean.getId());

        Set<StaticThresholdRules> availableRules = new HashSet<>();

        if (Constants.CORE_KPI_TYPE.equalsIgnoreCase(kpiTypeName)) {
            // Core KPIs need placeholders for both instance and cluster levels.
            availableRules.add(populateStaticThresholdRulesByKpiDetail(true, Constants.CORE_KPI_TYPE, kpiDetailsBean, kpiCategoryDetailBean));
            availableRules.add(populateStaticThresholdRulesByKpiDetail(false, Constants.CORE_KPI_TYPE, kpiDetailsBean, kpiCategoryDetailBean));
        } else {
            // Availability KPIs only need an instance-level placeholder.
            availableRules.add(populateStaticThresholdRulesByKpiDetail(false, Constants.AVAIL_KPI_TYPE, kpiDetailsBean, kpiCategoryDetailBean));
        }

        return availableRules;
    }

    /**
     * Processes a list of configured thresholds for a single KPI.
     */
    private Map<String, Set<StaticThresholdRules>> processConfiguredThresholds(KpiDetailsBean kpiDetailsBean, KpiCategoryDetailsBean kpiCategoryDetailBean,
                                             List<ServiceKpiThreshold> kpiThresholds, String kpiTypeName,
                                             Map<Integer, List<String>> configuredThresholdsFromOS,
                                             ViewTypes lowThreshold, ViewTypes mediumThreshold, ViewTypes highThreshold) throws HealControlCenterException {

        Map<String, Set<StaticThresholdRules>> rules = new HashMap<>();
        Set<StaticThresholdRules> availableRules = new HashSet<>();
        Set<StaticThresholdRules> missingRulesForOS = new HashSet<>();
        // For Core KPIs, if a threshold is defined for only one level (e.g., 'clusters'),
        // we must create a placeholder for the other level (e.g., 'instances').
        if (Constants.CORE_KPI_TYPE.equalsIgnoreCase(kpiTypeName)) {
            Map<Integer, List<ServiceKpiThreshold>> severityWiseThresholds = kpiThresholds.stream()
                    .collect(Collectors.groupingBy(ServiceKpiThreshold::getThresholdSeverityId));

            severityWiseThresholds.values().stream()
                    .filter(thresholds -> thresholds.size() == 1) // Find severities where only one level is configured.
                    .forEach(matchingThresholds -> {
                        boolean isClusterLevelDefined = matchingThresholds.get(0).getApplicableTo().equalsIgnoreCase("Clusters");
                        // Add a placeholder for the opposite, undefined level.
                        availableRules.add(populateStaticThresholdRulesByKpiDetail(!isClusterLevelDefined, Constants.CORE_KPI_TYPE, kpiDetailsBean, kpiCategoryDetailBean));
                    });
        }

        // Process each configured threshold rule.
        for (ServiceKpiThreshold threshold : kpiThresholds) {
            StaticThresholdRules rule = populateStaticThresholdRules(kpiCategoryDetailBean, threshold, kpiDetailsBean, lowThreshold, mediumThreshold, highThreshold);
            availableRules.add(rule);

            // Identify if this rule needs to be synced to OpenSearch.
            List<String> applicableToInOS = configuredThresholdsFromOS.getOrDefault(kpiDetailsBean.getId(), Collections.emptyList());
            if (rule.isGenerateAnomaly() && !applicableToInOS.contains(threshold.getApplicableTo())) {
                log.trace("Threshold missing in OS, queueing for insert. kpiId:{}, applicableTo:{}", kpiDetailsBean.getId(), threshold.getApplicableTo());
                missingRulesForOS.add(rule);
            }
        }

        rules.put(Constants.AVAILABLE_RULES, availableRules);
        rules.put(Constants.MISSING_RULES, missingRulesForOS);

        return rules;
    }

    private Map<Integer, List<String>> extractResult(TabularResults results) {
        Map<Integer, List<String>> kpiIdVSApplicableToMap = new HashMap<>();

        if (results != null && results.getRowResults() != null && !results.getRowResults().isEmpty()) {
            for (TabularResults.ResultRow resultRow : results.getRowResults()) {
                int kpiId = 0;
                String applicableTo = "";
                for (TabularResults.ResultRow.ResultRowColumn resultRowColumn : resultRow.getListOfRows()) {
                    if (resultRowColumn.getColumnName().equalsIgnoreCase("kpiId")) {
                        kpiId = Integer.parseInt(resultRowColumn.getColumnValue());

                    }
                    if (resultRowColumn.getColumnName().equalsIgnoreCase("applicableTo")) {
                        applicableTo = resultRowColumn.getColumnValue();
                    }
                }

                List<String> exists = kpiIdVSApplicableToMap.getOrDefault(kpiId, new ArrayList<>());
                exists.add(applicableTo);
                kpiIdVSApplicableToMap.put(kpiId, exists);
            }
        }
        return kpiIdVSApplicableToMap;
    }

    protected StaticThresholdRules populateStaticThresholdRulesByKpiDetail(boolean isCluster, String kpiType, KpiDetailsBean kpiDetailsBean,
                                                                           KpiCategoryDetailsBean kpiCategoryDetailBean) {
        int temp = kpiDetailsBean.getId();
        StaticThresholdRules staticThresholdRules = new StaticThresholdRules();
        staticThresholdRules.setKpiName(kpiDetailsBean.getName());
        staticThresholdRules.setKpiId(Integer.toString(temp));
        staticThresholdRules.setDataId(0);
        staticThresholdRules.setKpiLevel(isCluster ? "clusters" : "instances");
        staticThresholdRules.setCategoryId(kpiCategoryDetailBean.getId());
        staticThresholdRules.setCategoryName(kpiCategoryDetailBean.getName());
        staticThresholdRules.setKpiAttribute(Constants.ALL);
        staticThresholdRules.setKpiDataType(kpiDetailsBean.getDataType());
        staticThresholdRules.setKpiUnit(kpiDetailsBean.getMeasureUnits());
        staticThresholdRules.setComputedDetails(StaticThresholdRules.ComputedDetails.builder()
                .formula(kpiDetailsBean.getComputedFormula())
                .build());

        if (Constants.AVAIL_KPI_TYPE.equalsIgnoreCase(kpiType)) {
            staticThresholdRules.setGenerateAnomaly(false);
            staticThresholdRules.setUserDefinedSOR(true);
        } else {
            staticThresholdRules.setGenerateAnomaly(true);
            staticThresholdRules.setUserDefinedSOR(false);
        }

        return staticThresholdRules;
    }

    private StaticThresholdRules populateStaticThresholdRules(KpiCategoryDetailsBean kpiCategoryDetailBean, ServiceKpiThreshold kpiThreshold, KpiDetailsBean kpiDetailsBean,
                                                              ViewTypes lowThreshold, ViewTypes mediumThreshold, ViewTypes highThreshold) throws HealControlCenterException {
        StaticThresholdRules staticThresholdRules = new StaticThresholdRules();
        staticThresholdRules.setKpiName(kpiDetailsBean.getName());
        staticThresholdRules.setKpiId(String.valueOf(kpiThreshold.getKpiId()));
        staticThresholdRules.setDataId(kpiThreshold.getId());
        staticThresholdRules.setKpiLevel(kpiThreshold.getApplicableTo());
        staticThresholdRules.setCategoryId(kpiCategoryDetailBean.getId());
        staticThresholdRules.setCategoryName(kpiCategoryDetailBean.getName());
        staticThresholdRules.setKpiAttribute(kpiThreshold.getKpiAttribute());
        staticThresholdRules.setKpiDataType(kpiDetailsBean.getDataType());
        staticThresholdRules.setCoverageWindow(kpiThreshold.getCoverageWindow());
        staticThresholdRules.setComputedDetails(StaticThresholdRules.ComputedDetails.builder()
                .formula(kpiDetailsBean.getComputedFormula())
                .build());

        staticThresholdRules.setKpiUnit(kpiDetailsBean.getMeasureUnits());
        staticThresholdRules.setStartTime(kpiThreshold.getStartTime());
        if (kpiThreshold.getStatus() == 1) {
            staticThresholdRules.setGenerateAnomaly(true);
        }

        setThresholdValues(kpiThreshold, lowThreshold, mediumThreshold, highThreshold, staticThresholdRules);

        return staticThresholdRules;
    }

    private void setThresholdValues(ServiceKpiThreshold kpiThreshold, ViewTypes lowThreshold, ViewTypes mediumThreshold, ViewTypes highThreshold,
                                    StaticThresholdRules staticThresholdRules) throws HealControlCenterException {
        ViewTypes customOperationType = cacheWrapper.getAllViewTypesIdMap().getOrDefault(Constants.OPERATIONS_TYPE, new ArrayList<>())
                .stream().filter(s -> s.getSubTypeId() == kpiThreshold.getOperationTypeId()).findFirst().orElse(null);
        if (customOperationType == null) {
            log.error("Invalid Operation Type for {} id.", kpiThreshold.getOperationTypeId());
            throw new HealControlCenterException("Invalid operation type");
        }

        if (kpiThreshold.getThresholdSeverityId() == lowThreshold.getSubTypeId()) {
            staticThresholdRules.setLowThreshold(ThresholdConfig.builder()
                    .operationType(customOperationType.getSubTypeName())
                    .max(kpiThreshold.getMaxThreshold())
                    .min(kpiThreshold.getMinThreshold())
                    .build());
        }
        if (kpiThreshold.getThresholdSeverityId() == mediumThreshold.getSubTypeId()) {
            staticThresholdRules.setWarningThreshold(ThresholdConfig.builder()
                    .operationType(customOperationType.getSubTypeName())
                    .max(kpiThreshold.getMaxThreshold())
                    .min(kpiThreshold.getMinThreshold())
                    .build());
        }
        if (kpiThreshold.getThresholdSeverityId() == highThreshold.getSubTypeId()) {
            staticThresholdRules.setErrorThreshold(ThresholdConfig.builder()
                    .operationType(customOperationType.getSubTypeName())
                    .max(kpiThreshold.getMaxThreshold())
                    .min(kpiThreshold.getMinThreshold())
                    .build());
        }
    }
}