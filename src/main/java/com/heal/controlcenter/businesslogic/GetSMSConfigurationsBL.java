package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.SMSDetailsBean;
import com.heal.controlcenter.beans.SMSParameterBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.SMSDetailsPojo;
import com.heal.controlcenter.pojo.SMSParameterPojo;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetSMSConfigurationsBL implements BusinessLogic<Object, Integer, SMSDetailsPojo> {

    private final AccountsDao accountDao;
    private final MasterDataDao masterDataDao;
    private final NotificationsDao notificationsDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;

    public GetSMSConfigurationsBL(AccountsDao accountDao, MasterDataDao masterDataDao,
                                  NotificationsDao notificationsDao, ClientValidationUtils clientValidationUtils,
                                  ServerValidationUtils serverValidationUtils) {
        this.accountDao = accountDao;
        this.masterDataDao = masterDataDao;
        this.notificationsDao = notificationsDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
    }

    @Override
    @LogExecutionAnnotation
    public UtilityBean<Object> clientValidation(Object... arguments) throws ClientException {
        try {
            String accountIdentifier = (String) arguments[0];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);

            return UtilityBean.builder()
                    .requestParams(requestParamsMap)
                    .basicUserDetails(basicUserDetails)
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    @Override
    public Integer serverValidation(UtilityBean<Object> utilityBean) throws ServerException {
        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);
        return account.getId();
    }

    @Override
    public SMSDetailsPojo process(Integer accountId) throws DataProcessingException {
        SMSDetailsBean smsDetails;
        List<SMSParameterBean> smsParametersList;
        try {
            smsDetails = notificationsDao.getSMSDetails(accountId);
            if (smsDetails == null) {
                log.info("SMS Configurations not found for accountId [{}].", accountId);
                return null;
            }
            smsParametersList = notificationsDao.getSMSParameters(smsDetails.getId());
        } catch (HealControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }
        if (smsParametersList.isEmpty()) {
            log.info("SMS parameters not found for SMS details id [{}].", smsDetails.getId());
            return null;
        }

        List<SMSParameterPojo> smsParameters = smsParametersList.stream().map(parameter -> SMSParameterPojo.builder()
                        .parameterId(parameter.getId())
                        .parameterName(parameter.getParameterName())
                        .parameterValue(parameter.getParameterValue())
                        .parameterType(masterDataDao.getMstSubTypeBySubTypeId(parameter.getParameterTypeId()).getSubTypeName())
                        .isPlaceholder(parameter.getIsPlaceholder() == 1)
                        .build())
                .collect(Collectors.toList());

        return SMSDetailsPojo.builder()
                .address(smsDetails.getAddress())
                .id(smsDetails.getId())
                .countryCode(smsDetails.getCountryCode())
                .port(smsDetails.getPort())
                .protocolName(masterDataDao.getMstSubTypeBySubTypeId(smsDetails.getProtocolId()).getSubTypeName())
                .httpMethod(smsDetails.getHttpMethod())
                .httpRelativeUrl(smsDetails.getHttpRelativeUrl())
                .isMultiRequest(smsDetails.getIsMultiRequest())
                .postData(smsDetails.getPostData())
                .parameters(smsParameters)
                .build();
    }
}