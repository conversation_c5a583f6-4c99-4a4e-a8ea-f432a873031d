package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetAccountServicesBL implements BusinessLogic<Object, UtilityBean<AccountServiceValidationBean>, Page<ServiceListPage>> {

    private final ControllerDao controllerDao;
    private final ConnectionDetailsDao connectionDetailsDao;
    private final UserDao userDao;
    private final ClientValidationUtils clientValidationUtils;
    private final ServerValidationUtils serverValidationUtils;
    private final MasterDataDao masterDataDao;
    private final KeyCloakAuthService keyCloakAuthService;
    private final AccountServiceDao accountServiceDao;

    private final UserValidationUtil userValidationUtil;

    public GetAccountServicesBL(ControllerDao controllerDao, ConnectionDetailsDao connectionDetailsDao, UserDao userDao,
                                ClientValidationUtils clientValidationUtils, ServerValidationUtils serverValidationUtils,
                                MasterDataDao masterDataDao, KeyCloakAuthService keyCloakAuthService, AccountServiceDao accountServiceDao,
                                UserValidationUtil userValidationUtil) {
        this.controllerDao = controllerDao;
        this.connectionDetailsDao = connectionDetailsDao;
        this.userDao = userDao;
        this.clientValidationUtils = clientValidationUtils;
        this.serverValidationUtils = serverValidationUtils;
        this.masterDataDao = masterDataDao;
        this.keyCloakAuthService = keyCloakAuthService;
        this.accountServiceDao = accountServiceDao;
        this.userValidationUtil = userValidationUtil;
    }

    /**
     * Validates the client request for retrieving account services.
     *
     * @param arguments The request parameters, including authorization, account identifier, and search term.
     * @return A {@link UtilityBean} containing the validated request parameters.
     * @throws ClientException If any validation fails.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<Object> clientValidation(Object... arguments) throws ClientException {
        try {
            String accountIdentifier = (String) arguments[0];
            clientValidationUtils.accountIdentifierValidation(accountIdentifier);

            String searchTerm = arguments.length > 1 ? (String) arguments[1] : null;
            if (searchTerm != null) {
                searchTerm = searchTerm.trim();
                if (searchTerm.isEmpty()) {
                    log.warn("Search term is empty after trimming. Defaulting to empty string.");
                    searchTerm = "";
                }
            } else {
                log.warn("Search term is null in requestParams. Defaulting to empty string.");
                searchTerm = "";
            }

            BasicUserDetails basicUserDetails = (BasicUserDetails) arguments[arguments.length - 1];

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, accountIdentifier);
            requestParamsMap.put(Constants.SEARCH_TERM_KEY, searchTerm);

            return UtilityBean.builder()
                    .requestParams(requestParamsMap)
                    .basicUserDetails(basicUserDetails)
                    .metadata(new HashMap<>())
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_CLIENT_VALIDATION, e);
            throw new ClientException(UIMessages.INVALID_REQUEST_BODY);
        }
    }

    /**
     * Performs server-side validation for the get account services request.
     *
     * @param utilityBean A {@link UtilityBean} containing request parameters.
     * @return An enriched {@link UtilityBean} with extracted metadata (e.g., userId, account).
     * @throws ServerException If validation fails or account is not found.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<AccountServiceValidationBean> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        AccountServiceValidationBean accountServiceValidationBean = AccountServiceValidationBean.builder()
                .account(account)
                .build();

        return UtilityBean.<AccountServiceValidationBean>builder()
                .pojoObject(accountServiceValidationBean)
                .metadata(utilityBean.getMetadata())
                .pageable(utilityBean.getPageable())
                .requestParams(utilityBean.getRequestParams())
                .build();
    }

    /**
     * Executes the business logic to retrieve a paginated list of services for an account.
     *
     * @param utilityBean A validated {@link UtilityBean} containing metadata and request parameters.
     * @return A {@link Page} of services for the account.
     * @throws DataProcessingException If any error occurs during service retrieval from the database.
     */
    @Override
    @LogExecutionAnnotation
    public Page<ServiceListPage> process(UtilityBean<AccountServiceValidationBean> utilityBean) throws DataProcessingException {
        AccountServiceValidationBean accountServiceValidationBean = utilityBean.getPojoObject();
        Account account = accountServiceValidationBean.getAccount();
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);

        Pageable pageable = utilityBean.getPageable();
        String searchTerm = utilityBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);

        PaginationUtils.validatePagination(pageable);
        if (searchTerm == null) {
            log.warn("Search term is null in UtilityBean requestParams. Defaulting to empty string.");
            searchTerm = "";
        }
        log.info("Processing services for account {} with Pageable: {} and SearchTerm: '{}'", account.getIdentifier(), pageable, searchTerm);

        try {
            List<String> accessibleAppIdentifiers = userValidationUtil.getAccessibleApplicationIdentifiers(userId, account.getIdentifier());
            if (accessibleAppIdentifiers.isEmpty()) {
                log.warn("User [{}] does not have access to any applications in account [{}]", userId, account.getIdentifier());
                return new PageImpl<>(Collections.emptyList(), pageable, 0);
            }
            List<ControllerBean> services;
            int totalServices;

            if (accessibleAppIdentifiers.contains("*")) {
                services = controllerDao.getServicesList(account.getId(), searchTerm, pageable);
                totalServices = controllerDao.getServicesListCount(account.getId(), searchTerm);
            } else {
                services = controllerDao.getServicesList(account.getId(), searchTerm, accessibleAppIdentifiers, pageable);
                totalServices = controllerDao.getServicesListCount(account.getId(), searchTerm, accessibleAppIdentifiers);
            }

            if (services.isEmpty()) {
                log.info(LogMessages.NO_SERVICES_FOUND, account.getIdentifier());
                return new PageImpl<>(Collections.emptyList(), pageable, totalServices);
            }

            List<Integer> serviceIds = services.stream().map(ControllerBean::getId).collect(Collectors.toList());
            List<String> userIdentifiers = services.stream().map(ControllerBean::getLastModifiedBy).filter(Objects::nonNull).distinct().toList();

            Map<Integer, Map<String, String>> tagsByServiceId = accountServiceDao.getTagsForServices(serviceIds);
            List<Integer> entryPointServiceIds = accountServiceDao.getEntryPointServiceIds(serviceIds);
            Map<Integer, ServiceGroupBean> serviceGroupsByServiceId = accountServiceDao.getServiceGroupsForServices(serviceIds);
            Map<String, KeyCloakUserDetails> userDetailsMap = userIdentifiers.stream()
                    .map(keyCloakAuthService::getKeycloakUserDataFromId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(KeyCloakUserDetails::getId, Function.identity(), (u1, u2) -> u1));


            List<ConnectionDetailsBean> connections = connectionDetailsDao.getConnectionsByAccountId(account.getId());
            List<CompInstClusterDetailsBean> compInstClusterDetails = masterDataDao.getCompInstanceDetails(account.getId());

            //TODO: we are converting instance id vs component type name.
            Map<Integer, String> componentTypeMap = compInstClusterDetails.stream()
                    .collect(Collectors.toMap(CompInstClusterDetailsBean::getInstanceId, CompInstClusterDetailsBean::getComponentTypeName, (e, r) -> e));

            Map<Integer, String> serviceIdToNameMap = services.stream()
                    .collect(Collectors.toMap(ControllerBean::getId, ControllerBean::getName));

            List<ServiceListPage> serviceListPages = services.stream()
                    .map(service -> {
                        ServiceListPage bean = new ServiceListPage();
                        bean.setId(service.getId());
                        bean.setName(service.getName());
                        bean.setIdentifier(service.getIdentifier());
                        bean.setComponentType(componentTypeMap.getOrDefault(service.getId(), "NA")); // TODO: This is wrong, We should dervice from service instances.
                        bean.setStatus(String.valueOf(service.getStatus()));

                        Map<String, String> serviceTags = tagsByServiceId.getOrDefault(service.getId(), Collections.emptyMap());
                        bean.setLayer(serviceTags.get(Constants.LAYER_TAG));
                        bean.setType(serviceTags.get(Constants.SERVICE_TYPE_TAG));
                        bean.setEntryPointService(entryPointServiceIds.contains(service.getId()));
                        bean.setServiceGroup(serviceGroupsByServiceId.get(service.getId()));

                        if (service.getCreatedTime() != null) {
                            bean.setCreatedOn(DateTimeUtil.getGMTToEpochTime(service.getCreatedTime()));
                        }
                        if (service.getUpdatedTime() != null) {
                            bean.setLastModifiedOn(DateTimeUtil.getGMTToEpochTime(service.getUpdatedTime()));
                        }

                        if (service.getLastModifiedBy() != null) {
                            //TODO: As mentioned in account PR, we should make one call per user. This can be stored in local cache as well.
                            KeyCloakUserDetails userDetails = userDetailsMap.get(service.getLastModifiedBy());
                            String username = userDetails != null ? userDetails.getUsername() : service.getLastModifiedBy();
                            bean.setLastModifiedBy(username);
                            // Set createdBy to the same value since database doesn't have separate created_by field
                            bean.setCreatedBy(username);
                        }

                        updateInboundOutBoundServices(connections, service.getId(), bean, serviceIdToNameMap);
                        return bean;
                    })
                    .collect(Collectors.toList());

            return PaginationUtils.createPage(serviceListPages, pageable, totalServices);

        } catch (HealControlCenterException e) {
            log.error("Error while fetching services for account: {}. Details: ", account.getIdentifier(), e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    private void updateInboundOutBoundServices(List<ConnectionDetailsBean> connectionDetailsList, Integer serviceId, ServiceListPage bean, Map<Integer, String> serviceIdToNameMap) {
        Map<String, Object> inboundMap = new HashMap<>();
        Map<String, Object> outboundMap = new HashMap<>();

        if (Objects.nonNull(connectionDetailsList)) {
            List<Integer> inboundIds = connectionDetailsList.stream()
                    .filter(c -> serviceId.equals(c.getDestinationId()))
                    .map(ConnectionDetailsBean::getSourceId)
                    .toList();
            List<String> inboundNames = inboundIds.stream()
                    .map(serviceIdToNameMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            inboundMap.put("count", inboundNames.size());
            inboundMap.put("name", inboundNames);

            List<Integer> outboundIds = connectionDetailsList.stream()
                    .filter(c -> serviceId.equals(c.getSourceId()))
                    .map(ConnectionDetailsBean::getDestinationId)
                    .toList();
            List<String> outboundNames = outboundIds.stream()
                    .map(serviceIdToNameMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            outboundMap.put("count", outboundNames.size());
            outboundMap.put("name", outboundNames);
        } else {
            inboundMap.put("count", 0);
            inboundMap.put("name", Collections.emptyList());
            outboundMap.put("count", 0);
            outboundMap.put("name", Collections.emptyList());
        }

        bean.setInbound(inboundMap);
        bean.setOutbound(outboundMap);
    }
}
