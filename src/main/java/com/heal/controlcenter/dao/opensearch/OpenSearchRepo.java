package com.heal.controlcenter.dao.opensearch;

import com.heal.controlcenter.dao.redis.MasterDataRepo;
import com.heal.controlcenter.enums.OpenSearchConnectionManager;
import com.heal.controlcenter.exception.HealControlCenterException;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.FieldValue;
import org.opensearch.client.opensearch._types.SortOrder;
import org.opensearch.client.opensearch._types.aggregations.Aggregation;
import org.opensearch.client.opensearch._types.query_dsl.Query;
import org.opensearch.client.opensearch.core.DeleteByQueryRequest;
import org.opensearch.client.opensearch.core.DeleteByQueryResponse;
import org.opensearch.client.opensearch.core.SearchRequest;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Repository
public class OpenSearchRepo<T> {
    @Autowired
    private MasterDataRepo masterDataRepo;

    @PostConstruct
    public void init() {
        OpenSearchConnectionManager.INSTANCE.setMasterDataRepo(masterDataRepo);
    }

    public static SearchResponse<Object> getDistinctFieldsAndCount(Query qb, Map<String, Aggregation> termAggs, String indexName, String accountIdentifier, String index) throws IOException, HealControlCenterException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            throw new HealControlCenterException("OpenSearch client is null.");
        }

        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .query(qb)
                .aggregations(termAggs)
                .build();

        return openSearchClient.search(searchRequest, Object.class);
    }

    public static SearchResponse<Object> getLimitedSortedDocument(Query qb, String indexName, String fieldName, boolean isDesc, int limit, String accountIdentifier, String index) throws IOException, HealControlCenterException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            throw new HealControlCenterException("OpenSearch client is null.");
        }

        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .query(qb)
                .size(limit)
                .sort(c -> c.field(f -> f.field(fieldName).order(isDesc ? SortOrder.Desc : SortOrder.Asc)))
                .build();

        return openSearchClient.search(searchRequest, Object.class);
    }

    public DeleteByQueryResponse deleteDocByInstanceIdentifier(String indexName, String instanceIdentifier, String osIdentifier, String accountIdentifier, String index) throws IOException {
        OpenSearchClient openSearchClient = OpenSearchConnectionManager.INSTANCE.getOpenSearchClient(accountIdentifier, index);
        if (openSearchClient == null) {
            log.error("Could not delete the data from OpenSearch for indexName:{}, instanceIdentifier:{}, osIdentifier:{}", indexName, instanceIdentifier, osIdentifier);
            return null;
        }

        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest.Builder()
                .index(indexName)
                .query(Query.of(c -> c.term(d -> d.field(osIdentifier).value(FieldValue.of(instanceIdentifier)))))
                .build();

        return openSearchClient.deleteByQuery(deleteByQueryRequest);
    }
}
