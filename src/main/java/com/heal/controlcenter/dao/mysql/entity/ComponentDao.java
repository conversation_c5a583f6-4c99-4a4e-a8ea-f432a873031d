package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.beans.CompClusterMappingBean;
import com.heal.controlcenter.beans.CompInstanceKpiGroupDetailsBean;
import com.heal.controlcenter.exception.HealControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class ComponentDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<CompClusterMappingBean> getInstanceClusterMapping(int accountId) {
        String query = "SELECT comp_instance_id AS compInstanceId, cluster_id AS clusterId " +
                "FROM component_cluster_mapping " +
                "WHERE account_id = ?";

        try {
            log.debug("Fetching instance-cluster mappings for accountId: {}", accountId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompClusterMappingBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching instance-cluster mappings for accountId: {}. Details:", accountId, e);
            return Collections.emptyList();
        }
    }

    /**
     * Gets config watch files/KPIs by component ID and version ID.
     * This method retrieves KPI group details for file/config watch monitoring.
     *
     * @param componentId The master component ID
     * @param componentVersionId The master component version ID
     * @return List of CompInstanceKpiGroupDetailsBean for config watch KPIs
     */
    public List<CompInstanceKpiGroupDetailsBean> getConfigWatchFilesByComponentId(int componentId, int componentVersionId) throws HealControlCenterException {
        String query = "SELECT CONCAT(cf.relative_path, cf.file_name) AS attributeValue, " +
        "pk.id AS mstProducerKpiMappingId, " +
                "pk.producer_id AS mstProducerId, " +
                "pk.mst_kpi_details_id AS mstKpiDetailsId, " +
                "k.kpi_group_id AS mstKpiGroupId, " +
                "g.name AS kpiGroupName, " +
                "cm.default_collection_interval AS collectionInterval " +
                "FROM mst_producer_kpi_mapping pk, " +
                "mst_component_files cf, " +
                "mst_kpi_details k, " +
                "mst_kpi_group g, " +
                "mst_component_version_kpi_mapping cm, " +
                "mst_component_version cv " +
                "WHERE pk.mst_component_id = ? " +
                "AND cf.mst_component_version_id = ? " +
                "AND cf.mst_component_id = pk.mst_component_id " +
                "AND pk.mst_component_version_id = cf.mst_component_version_id " +
                "AND cf.mst_kpi_details_id = pk.mst_kpi_details_id " +
                "AND pk.is_default = 1 " +
                "AND cf.mst_kpi_details_id = k.id " +
                "AND k.kpi_group_id = g.id " +
                "AND cv.mst_component_id = cm.mst_common_version_id " +
                "AND cv.mst_common_version_id = cm.mst_common_version_id " +
                "AND cm.mst_component_id = pk.mst_component_id " +
                "AND cv.id = cf.mst_component_version_id " +
                "AND cm.mst_kpi_details_id = cf.mst_kpi_details_id " +
                "AND pk.mst_kpi_details_id = cm.mst_kpi_details_id " +
                "AND cf.is_discovery = 0";

        try {
            log.debug("Fetching config watch files for componentId: {} and componentVersionId: {}", componentId, componentVersionId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompInstanceKpiGroupDetailsBean.class), componentId, componentVersionId);
        } catch (Exception e) {
            log.error("Error occurred while fetching config watch files for componentId: {} and componentVersionId: {}. Details:",
                    componentId, componentVersionId, e);
            throw new HealControlCenterException(e.getMessage());
        }
    }
}
