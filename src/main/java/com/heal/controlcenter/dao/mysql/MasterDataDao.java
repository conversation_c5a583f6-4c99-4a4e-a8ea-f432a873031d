package com.heal.controlcenter.dao.mysql;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.AllKpiList;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.pojo.TimezoneDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;

@Slf4j
@Repository
public class MasterDataDao {

    private final JdbcTemplate jdbcTemplate;

    public MasterDataDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public TimezoneBean getTimeZoneWithId(String timeZoneId) throws HealControlCenterException {
        String query = "select id, time_zone_id timeZoneId, timeoffset offset, user_details_id userDetailsId, account_id accountId, " +
                "status status from mst_timezone where status=1 and time_zone_id=?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(TimezoneBean.class), timeZoneId);
        } catch (Exception ex) {
            log.error("Error while fetching timezone details for id [{}]. Details: ", timeZoneId, ex);
            throw new HealControlCenterException("Error while fetching timezone details");
        }
    }

    public ViewTypesBean getMstSubTypeBySubTypeId(int subTypeId)  {
        String query = "select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types " +
                "where subtypeid = ?";
        try {
            log.debug("getting type details.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ViewTypesBean.class), subTypeId);
        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type info from 'view_types' table for subTypeId [{}]. Details: ", subTypeId, e);
            //throw new HealControlCenterException("Error occurred while fetching master type and sub type info.");
        }
        return null;
    }

    public List<ViewTypesBean> getMstSubTypeByTypeId(int typeId) throws HealControlCenterException {
        String query = "select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types " +
                "where typeid = " + typeId;
        try {
            log.debug("getting type details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewTypesBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type info from 'view_types' table for typeId [{}]. Details: ", typeId, e);
            throw new HealControlCenterException("Error occurred while fetching master type and sub type info.");
        }
    }

    public ViewTypesBean getViewTypesFromMstTypeAndSubTypeName(String typeName, String subTypeName) throws HealControlCenterException {
        String query = "select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types where type=? and name=?";
        try {
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(ViewTypesBean.class), typeName, subTypeName);
        } catch (Exception e) {
            log.error("Error while fetching view type using typeName [{}] and subTypeName [{}]. Details: ", typeName, subTypeName, e);
            throw new HealControlCenterException("Error while fetching view type");
        }
    }

    public List<ViewTypesBean> getMstTypeByTypeName(String typeName) throws HealControlCenterException {
        String query = "select type typeName, typeid typeId, name subTypeName, subtypeid subTypeId from view_types where type=?";
        try {
            log.debug("getting type details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ViewTypesBean.class), typeName);
        } catch (Exception e) {
            log.error("Error occurred while fetching master type and sub type info from 'view_types' table for typeName [{}]. Details: ", typeName, e);
            throw new HealControlCenterException("Error occurred while fetching master type and sub type info.");
        }
    }

    public List<CompInstClusterDetailsBean> getCompInstanceDetails(int accountId) throws HealControlCenterException {
        String query = "select id instanceId,common_version_id commonVersionId, common_version_name commonVersionName,mst_component_id compId,component_name componentName, " +
                "mst_component_type_id mstComponentTypeId,component_type_name componentTypeName, " +
                "mst_component_version_id compVersionId,component_version_name componentVersionName,name instanceName,host_id hostId,status, " +
                "host_name hostName,is_cluster isCluster,identifier, " +
                "host_address hostAddress, supervisor_id supervisorId from view_component_instance where account_id = ? and status = 1";
        try {
            log.debug("getting component instance details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompInstClusterDetailsBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching component instance list from 'view_component_instance' table for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while fetching component instance details.");
        }
    }

    public List<TagMappingDetails> getTagMappingDetails(int accountId) throws HealControlCenterException {
        String query = "select id,tag_id tagId,object_id objectId,object_ref_table objectRefTable," +
                "tag_key tagKey,tag_value tagValue,created_time createdTime,updated_time updatedTime," +
                "account_id accountId,user_details_id userDetailsId from tag_mapping " +
                "where object_ref_table != 'transaction' and account_id = ?";
        try {
            log.debug("getting tag mapping details.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TagMappingDetails.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching tag mapping details list from 'tag_mapping' table for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while fetching tag mapping details.");
        }
    }

    public List<MasterComponentTypeBean> getMasterComponentTypesData(int accountId) throws HealControlCenterException {
        String query = "SELECT id, name, description, is_custom isCustom, status, created_time createdTime, " +
                "updated_time updatedTime, user_details_id userDetailsId, account_id accountId " +
                "FROM mst_component_type WHERE account_id IN (1, ?)";

        try {
            log.debug("Getting master component types data for accountId: {}", accountId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(MasterComponentTypeBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching master component type data for accountId [{}]:", accountId, e);
            throw new HealControlCenterException("Error occurred while fetching master component type data.");
        }
    }

    public TagDetailsBean findTagDetailsByName(String tagName, String accountId) throws HealControlCenterException {
        String query = "SELECT id, name, tag_type_id AS tagTypeId, is_predefined AS isPredefined, " +
                "ref_table AS refTable, created_time AS createdTime, updated_time AS updatedTime, " +
                "account_id AS accountId, user_details_id AS userDetailsId " +
                "FROM tag_details WHERE name = ? AND account_id IN (1, ?)";

        try {
            log.debug("Querying for tag details with name: {}", tagName);
            return jdbcTemplate.queryForObject(query,
                    new BeanPropertyRowMapper<>(TagDetailsBean.class),
                    tagName, accountId);
        } catch (EmptyResultDataAccessException e) {
            log.warn("No active tag found with name [{}].", tagName);
            return null; // or throw an exception based on your design
        } catch (Exception e) {
            log.error("Error occurred while fetching tag details by name [{}]. Details: ", tagName, e);
            throw new HealControlCenterException("Error occurred while fetching tag details by name.");
        }
    }

    public List<TimezoneDetail> getAllActiveTimezones() throws HealControlCenterException {
        // This is the same query used by the old system to get all active timezones.
        String query = "SELECT id, time_zone_id AS timeZoneId, timeoffset AS offset, user_details_id AS userDetailsId, " +
                "account_id AS accountId, status FROM mst_timezone WHERE status=1";
        try {
            log.debug("Querying for all active timezones.");
            // Use jdbcTemplate.query for multiple results, mapping them to the TimezoneDetail bean.
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(TimezoneDetail.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching all active timezones from 'mst_timezone' table. Details: ", e);
            // Return an empty list to prevent NullPointerExceptions in the calling code.
            throw new HealControlCenterException("Error occurred while fetching all active timezones.");
        }
    }

    /**
     * Retrieves tag details with related information using joins for enhanced data.
     * This method joins tag_details with related tables to provide comprehensive tag information.
     *
     * @param tagName the name of the tag to search for
     * @param accountId the account ID to filter by
     * @return TagDetailsBean containing tag details with related information, or null if not found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public TagDetailsBean getTagDetails(String tagName, int accountId) throws HealControlCenterException {
        // Using LEFT JOINs to get related information from tag_type, account, and user_details tables
        String query = "SELECT td.id, td.name, td.tag_type_id AS tagTypeId, td.is_predefined AS isPredefined, " +
                "td.ref_table AS refTable, td.created_time AS createdTime, td.updated_time AS updatedTime, " +
                "td.account_id AS accountId, td.user_details_id AS userDetailsId, " +
                "td.ref_where_column_name AS refWhereColumnName, td.ref_select_column_name AS refSelectColumnName " +
                "FROM tag_details td " +
                "WHERE td.name = ? AND td.account_id IN (1, ?)";

        try {
            log.debug("Querying tag_details for name: {} and accountId: {}", tagName, accountId);
            return jdbcTemplate.queryForObject(query, (rs, rowNum) -> {
                TagDetailsBean tagDetails = new TagDetailsBean();
                tagDetails.setId(rs.getInt("id"));
                tagDetails.setName(rs.getString("name"));
                tagDetails.setTagTypeId(rs.getInt("tagTypeId"));
                tagDetails.setIsPredefined(rs.getInt("isPredefined"));
                tagDetails.setRefTable(rs.getString("refTable"));
                tagDetails.setCreatedTime(rs.getString("createdTime"));
                tagDetails.setUpdatedTime(rs.getString("updatedTime"));
                tagDetails.setAccountId(rs.getInt("accountId"));
                tagDetails.setRefWhereColumnName(rs.getString("refWhereColumnName"));
                tagDetails.setRefSelectColumnName(rs.getString("refSelectColumnName"));
                tagDetails.setLastModifiedBy(rs.getString("userDetailsId"));
                return tagDetails;
            }, tagName, accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching tag details by name [{}] and accountId [{}]. Details: ",
                    tagName, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching tag details by name.");
        }
    }

    /**
     * Retrieves common version group KPIs data from view_common_version_kpis table.
     *
     * @param mstCommonVersionId the master common version ID
     * @param accountId the account ID
     * @return List of ViewCommonVersionKPIsBean containing group KPIs data
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public List<ViewCommonVersionKPIsBean> getViewCommonVersionGroupKPIsData(int mstCommonVersionId, int accountId) throws HealControlCenterException {
        String query = "SELECT DISTINCT mst_component_id AS mstComponentId, kpi_id AS kpiId, kpi_name AS kpiName, " +
                "kpi_identifier AS kpiIdentifier, mst_common_version_id AS mstCommonVersionId, " +
                "kpi_group_id AS kpiGroupId, kpi_type_id AS kpiTypeId, default_collection_interval AS defaultCollectionInterval, " +
                "status, default_operation_id AS defaultOperationId, default_threshold AS defaultThreshold, " +
                "account_id AS accountId " +
                "FROM view_common_version_kpis " +
                "WHERE mst_common_version_id = ? AND kpi_group_id != 0 AND account_id IN (1, ?)";

        try {
            log.debug("Querying view_common_version_kpis for group KPIs with mstCommonVersionId: {}, accountId: {}",
                    mstCommonVersionId, accountId);

            return jdbcTemplate.query(query, (rs, rowNum) -> {
                ViewCommonVersionKPIsBean kpiBean = new ViewCommonVersionKPIsBean();
                kpiBean.setMstComponentId(rs.getInt("mstComponentId"));
                kpiBean.setKpiId(rs.getInt("kpiId"));
                kpiBean.setKpiName(rs.getString("kpiName"));
                kpiBean.setKpiIdentifier(rs.getString("kpiIdentifier"));
                kpiBean.setMstCommonVersionId(rs.getInt("mstCommonVersionId"));
                kpiBean.setKpiGroupId(rs.getInt("kpiGroupId"));
                kpiBean.setKpiTypeId(rs.getInt("kpiTypeId"));
                kpiBean.setDefaultCollectionInterval(rs.getInt("defaultCollectionInterval"));
                kpiBean.setStatus(rs.getInt("status"));
                kpiBean.setDefaultOperationId(rs.getInt("defaultOperationId"));
                kpiBean.setDefaultThreshold(rs.getInt("defaultThreshold"));
                kpiBean.setAccountId(rs.getInt("accountId"));
                return kpiBean;
            }, mstCommonVersionId, accountId);

        } catch (Exception e) {
            log.error("Error occurred while fetching view common version group KPIs data for mstCommonVersionId [{}], accountId [{}]. Details: ",
                    mstCommonVersionId, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching view common version group KPIs data.");
        }
    }

    /**
     * Retrieves all active KPIs from view_all_kpis table.
     *
     * @return List of AllKpiList containing all active KPI data
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public List<AllKpiList> getAllKpis() throws HealControlCenterException {
        String query = "SELECT kpiid AS kpiId, kpi_name AS kpiName, measure_units AS kpiUnits, " +
                "cluster_operation AS clusterOperation, kpi_type AS kpiType, IFNULL(group_id, 0) AS groupId, " +
                "group_name AS groupName, IFNULL(discovery, 0) AS isDiscovery, IFNULL(group_status, 0) AS groupStatus, " +
                "rollup_operation AS rollupOperation, cluster_aggregation_type AS clusterAggType, " +
                "instance_aggregation_type AS instanceAggType, is_informative AS isInfo, identifier AS identifier, " +
                "is_custom AS isCustom, value_type AS valueType, data_type AS dataType, " +
                "cron_expression AS cronExpression, delta_per_sec AS deltaPerSec, reset_delta_value AS resetDeltaValue " +
                "FROM view_all_kpis WHERE kpi_status = 1";

        try {
            log.debug("Querying view_all_kpis for all active KPIs");

            return jdbcTemplate.query(query, (rs, rowNum) -> {
                AllKpiList kpi = new AllKpiList();
                kpi.setKpiId(rs.getInt("kpiId"));
                kpi.setKpiName(rs.getString("kpiName"));
                kpi.setKpiUnits(rs.getString("kpiUnits"));
                kpi.setClusterOperation(rs.getString("clusterOperation"));
                kpi.setKpiType(rs.getString("kpiType"));
                kpi.setGroupId(rs.getInt("groupId"));
                kpi.setGroupName(rs.getString("groupName"));
                kpi.setIsDiscovery(rs.getInt("isDiscovery"));
                kpi.setGroupStatus(rs.getInt("groupStatus"));
                kpi.setRollupOperation(rs.getString("rollupOperation"));
                kpi.setClusterAggType(rs.getInt("clusterAggType"));
                kpi.setInstanceAggType(rs.getInt("instanceAggType"));
                kpi.setIsInfo(rs.getInt("isInfo"));
                kpi.setIdentifier(rs.getString("identifier"));
                kpi.setIsCustom(rs.getInt("isCustom"));
                kpi.setValueType(rs.getString("valueType"));
                kpi.setDataType(rs.getString("dataType"));
                kpi.setCronExpression(rs.getString("cronExpression"));
                kpi.setDeltaPerSec(rs.getInt("deltaPerSec"));
                kpi.setResetDeltaValue(rs.getInt("resetDeltaValue"));
                return kpi;
            });

        } catch (Exception e) {
            log.error("Error occurred while fetching all KPIs. Details: ", e);
            throw new HealControlCenterException("Error occurred while fetching all KPIs.");
        }
    }

    /**
     * Retrieves master KPI group list from mst_kpi_group table.
     *
     * @param accountId the account ID
     * @return List of MasterKpiGroupBean containing KPI group data
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public List<MasterKpiGroupBean> getMasterKPIGroupList(int accountId, int kpiGroupId) throws HealControlCenterException {
        String query = "SELECT id, name, description, created_time AS createdTime, updated_time AS updatedTime, " +
                "account_id AS accountId, user_details_id AS userDetailsId, kpi_type_id AS kpiTypeId, " +
                "discovery, regex, status, is_custom AS isCustom, identifier " +
                "FROM mst_kpi_group WHERE account_id IN (1, ?) AND id = ?";

        try {
            log.debug("Querying mst_kpi_group for accountId: {}", accountId);

            return jdbcTemplate.query(query, (rs, rowNum) -> {
                MasterKpiGroupBean kpiGroup = new MasterKpiGroupBean();
                kpiGroup.setId(rs.getInt("id"));
                kpiGroup.setName(rs.getString("name"));
                kpiGroup.setDescription(rs.getString("description"));
                kpiGroup.setCreatedTime(rs.getTimestamp("createdTime"));
                kpiGroup.setUpdatedTime(rs.getTimestamp("updatedTime"));
                kpiGroup.setAccountId(rs.getInt("accountId"));
                kpiGroup.setUserDetailsId(rs.getString("userDetailsId"));
                kpiGroup.setKpiTypeId(rs.getInt("kpiTypeId"));
                kpiGroup.setDiscovery(rs.getInt("discovery"));
                kpiGroup.setRegex(rs.getString("regex"));
                kpiGroup.setStatus(rs.getInt("status"));
                kpiGroup.setIsCustom(rs.getInt("isCustom"));
                kpiGroup.setIdentifier(rs.getString("identifier"));

                return kpiGroup;
            }, accountId, kpiGroupId);

        } catch (Exception e) {
            log.error("Error occurred while fetching master KPI group list for accountId [{}]. Details: ", accountId, e);
            throw new HealControlCenterException("Error occurred while fetching master KPI group list.");
        }
    }

    /**
     * Retrieves view producer group KPIs data from view_producer_kpis table.
     *
     * @param mstKpiDetailId the master KPI detail ID
     * @param mstCompVersionId the master component version ID
     * @param mstCompId the master component ID
     * @param mstCompTypeId the master component type ID
     * @param accountId the account ID
     * @return ViewProducerKPIsBean containing producer group KPIs data, or null if not found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public ViewProducerKPIsBean getViewProducerGroupKPIsData(int mstKpiDetailId, int mstCompVersionId, int mstCompId, int mstCompTypeId, int accountId) throws HealControlCenterException {
        String query = "SELECT producer_id AS producerId, producer_name AS producerName, is_custom AS isCustom, " +
                "status, account_id AS accountId, is_kpi_group AS isKpiGroup, kpi_type AS kpiType, " +
                "producer_type AS producerType, classname, is_default AS isDefault, " +
                "mst_kpi_details_id AS mstKpiDetailsId, mst_component_version_id AS mstComponentVersionId, " +
                "kpi_name AS kpiName, mst_component_id AS mstComponentId, " +
                "mst_component_type_id AS mstComponentTypeId, mst_producer_kpi_mapping_id AS mstProducerKpiMappingId " +
                "FROM view_producer_kpis " +
                "WHERE mst_kpi_details_id = ? AND mst_component_version_id = ? AND mst_component_id = ? " +
                "AND mst_component_type_id = ? AND is_kpi_group != 0 AND is_default = 1 AND account_id IN (1, ?)";

        try {
            log.debug("Querying view_producer_kpis for group KPIs with mstKpiDetailId: {}, mstCompVersionId: {}, mstCompId: {}, mstCompTypeId: {}, accountId: {}",
                    mstKpiDetailId, mstCompVersionId, mstCompId, mstCompTypeId, accountId);

            return jdbcTemplate.queryForObject(query, (rs, rowNum) -> {
                ViewProducerKPIsBean producerKpi = new ViewProducerKPIsBean();
                producerKpi.setProducerId(rs.getInt("producerId"));
                producerKpi.setProducerName(rs.getString("producerName"));
                producerKpi.setIsCustom(rs.getInt("isCustom"));
                producerKpi.setStatus(rs.getInt("status"));
                producerKpi.setAccountId(rs.getInt("accountId"));
                producerKpi.setIsKpiGroup(rs.getInt("isKpiGroup"));
                producerKpi.setKpiType(rs.getString("kpiType"));
                producerKpi.setProducerType(rs.getString("producerType"));
                producerKpi.setClassname(rs.getString("classname"));
                producerKpi.setIsDefault(rs.getInt("isDefault"));
                producerKpi.setMstKpiDetailsId(rs.getInt("mstKpiDetailsId"));
                producerKpi.setMstComponentVersionId(rs.getInt("mstComponentVersionId"));
                producerKpi.setKpiName(rs.getString("kpiName"));
                producerKpi.setMstComponentId(rs.getInt("mstComponentId"));
                producerKpi.setMstComponentTypeId(rs.getInt("mstComponentTypeId"));
                producerKpi.setMstProducerKpiMappingId(rs.getInt("mstProducerKpiMappingId"));

                return producerKpi;
            }, mstKpiDetailId, mstCompVersionId, mstCompId, mstCompTypeId, accountId);

        } catch (Exception e) {
            log.error("Error occurred while fetching view producer group KPIs data for mstKpiDetailId [{}], mstCompVersionId [{}], mstCompId [{}], mstCompTypeId [{}], accountId [{}]. Details: ",
                    mstKpiDetailId, mstCompVersionId, mstCompId, mstCompTypeId, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching view producer group KPIs data.");
        }
    }

    /**
     * Retrieves master KPI details data from mst_kpi_details table.
     *
     * @param kpiId the KPI ID
     * @param accountId the account ID
     * @return MasterKPIDetailsBean containing KPI details data, or null if not found
     * @throws HealControlCenterException if an error occurs during the database operation
     */
    public MasterKPIDetailsBean getMasterKPIDetailsData(int kpiId, int accountId) throws HealControlCenterException {
        String query = "SELECT id, name, description, data_type AS dataType, is_custom AS isCustom, " +
                "status, kpi_type_id AS kpiTypeId, measure_units AS measureUnits, cluster_operation AS clusterOperation, " +
                "created_time AS createdTime, updated_time AS updatedTime, user_details_id AS userDetailsId, " +
                "account_id AS accountId, kpi_group_id AS kpiGroupId, identifier, value_type AS valueType " +
                "FROM mst_kpi_details WHERE id = ? AND account_id IN (1, ?)";

        try {
            log.debug("Querying mst_kpi_details for kpiId: {}, accountId: {}", kpiId, accountId);

            return jdbcTemplate.queryForObject(query, (rs, rowNum) -> {
                MasterKPIDetailsBean kpiDetails = new MasterKPIDetailsBean();
                kpiDetails.setId(rs.getInt("id"));
                kpiDetails.setName(rs.getString("name"));
                kpiDetails.setDescription(rs.getString("description"));
                kpiDetails.setDataType(rs.getString("dataType"));
                kpiDetails.setIsCustom(rs.getInt("isCustom"));
                kpiDetails.setStatus(rs.getInt("status"));
                kpiDetails.setKpiTypeId(rs.getInt("kpiTypeId"));
                kpiDetails.setMeasureUnits(rs.getString("measureUnits"));
                kpiDetails.setClusterOperation(rs.getString("clusterOperation"));
                kpiDetails.setCreatedTime(rs.getString("createdTime"));
                kpiDetails.setUpdatedTime(rs.getString("updatedTime"));
                kpiDetails.setUserDetailsId(rs.getString("userDetailsId"));
                kpiDetails.setAccountId(rs.getInt("accountId"));
                kpiDetails.setKpiGroupId(rs.getInt("kpiGroupId"));
                kpiDetails.setIdentifier(rs.getString("identifier"));
                kpiDetails.setValueType(rs.getString("valueType"));

                return kpiDetails;
            }, kpiId, accountId);

        } catch (Exception e) {
            log.error("Error occurred while fetching master KPI details data for kpiId [{}], accountId [{}]. Details: ",
                    kpiId, accountId, e);
            throw new HealControlCenterException("Error occurred while fetching master KPI details data.");
        }
    }

    /**
     * Gets environment ID by environment name.
     * @param environmentName Environment name
     * @return Environment ID
     * @throws HealControlCenterException if environment not found
     */
    public int getEnvironmentIdByName(String environmentName) throws HealControlCenterException {
        String query = "SELECT id FROM mst_environment WHERE name = ? AND status = 1";

        try {
            Integer environmentId = jdbcTemplate.queryForObject(query, Integer.class, environmentName);
            return environmentId != null ? environmentId : 383; // Default environment ID
        } catch (EmptyResultDataAccessException e) {
            log.warn("Environment not found: {}, using default ID", environmentName);
            return 383; // Default environment ID as per appsone-controlcenter
        } catch (Exception e) {
            log.error("Error getting environment ID for: {}", environmentName, e);
            throw new HealControlCenterException(e, "Failed to get environment ID");
        }
    }

    /**
     * Retrieves a specific master component type by name for an account.
     * This replaces the stream filter logic that was used with the JDBI version.
     * @param mstCompTypeName Component type name (e.g., Constants.COMPONENT_TYPE_HOST)
     * @param accountId Account ID
     * @return MasterComponentTypeBean or null if not found
     */
    public MasterComponentTypeBean getMasterComponentTypeData(String mstCompTypeName, int accountId) {
        String sql = "SELECT id, name, description, is_custom AS isCustom, status, " +
                "created_time AS createdTime, updated_time AS updatedTime, " +
                "user_details_id AS userDetailsId, account_id AS accountId " +
                "FROM mst_component_type WHERE account_id IN (1, ?) AND LOWER(name) = LOWER(?)";

        try {
            List<MasterComponentTypeBean> results = jdbcTemplate.query(sql,
                new BeanPropertyRowMapper<>(MasterComponentTypeBean.class),
                accountId, mstCompTypeName);

            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error retrieving master component type by name: {} for account: {}", mstCompTypeName, accountId, e);
            return null;
        }
    }
}

