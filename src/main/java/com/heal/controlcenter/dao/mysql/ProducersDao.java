package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.pojos.KpiDetails;
import com.heal.controlcenter.beans.ProducerKpiValidationBean;
import com.heal.controlcenter.pojo.ProducerDetailsPojo;
import com.heal.controlcenter.pojo.ProducerKPIMappingDetailsPojo;
import com.heal.controlcenter.pojo.ProducerKpiMappingPojo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@Repository
public class ProducersDao {

    private final JdbcTemplate jdbcTemplate;

    public ProducersDao(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Retrieves a paginated list of producer details for a given account.
     *
     * @param accountId The ID of the account.
     * @param producerId The ID of the producer (can be null).
     * @param name The name of the producer (can be null).
     * @param status The status of the producer (can be null).
     * @param producerType The type of the producer (can be null).
     * @param pageable Pagination and sorting information.
     * @return A list of ProducerDetailsPojo objects.
     */
    public List<ProducerDetailsPojo> getProducerDetailsWithAccId(int accountId, Integer producerId, String name, String status, String producerType, int isCustom, Pageable pageable) {
        List<Object> params = new ArrayList<>();
        String query = "SELECT p.id, p.name, p.description, pt.type as producer_type_name, p.is_custom, p.status, p.is_kpi_group, p.created_time, p.updated_time, ua.username as user_name " +
                "FROM mst_producer p " +
                "JOIN mst_producer_type pt ON p.mst_producer_type_id = pt.id " +
                "JOIN user_attributes ua ON p.user_details_id = ua.user_identifier " +
                "WHERE p.account_id IN (1, ?) AND p.is_custom = ?";
        params.add(accountId);
        params.add(isCustom);

        if (producerId != null) {
            query += " AND p.id = ?";
            params.add(producerId);
        }
        if (name != null && !name.isEmpty()) {
            query += " AND p.name LIKE ?";
            params.add("%" + name + "%");
        }
        if (status != null && !status.isEmpty()) {
            query += " AND p.status = ?";
            params.add(status);
        }
        if (producerType != null && !producerType.isEmpty()) {
            query += " AND pt.type = ?";
            params.add(producerType);
        }

        query += " LIMIT " + pageable.getOffset() + ", " + pageable.getPageSize();

        return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ProducerDetailsPojo.class), params.toArray());
    }

    /**
     * Retrieves the total count of producers for a given account.
     *
     * @param accountId The ID of the account.
     * @param name The name of the producer (can be null).
     * @param status The status of the producer (can be null).
     * @param producerType The type of the producer (can be null).
     * @return The total number of producers.
     */
    public int getProducersCount(int accountId, String name, String status, String producerType, int isCustom) {
        List<Object> params = new ArrayList<>();
        String query = "SELECT count(*) FROM mst_producer p " +
                "JOIN mst_producer_type pt ON p.mst_producer_type_id = pt.id " +
                "WHERE p.account_id IN (1, ?) AND p.is_custom = ?";
        params.add(accountId);
        params.add(isCustom);

        if (name != null && !name.isEmpty()) {
            query += " AND p.name LIKE ?";
            params.add("%" + name + "%");
        }
        if (status != null && !status.isEmpty()) {
            query += " AND p.status = ?";
            params.add(status);
        }
        if (producerType != null && !producerType.isEmpty()) {
            query += " AND pt.type = ?";
            params.add(producerType);
        }

        return jdbcTemplate.queryForObject(query, Integer.class, params.toArray());
    }

    /**
     * Retrieves a list of producer KPI mapping details for a given account.
     *
     * @param accountId The ID of the account.
     * @param producerId The ID of the producer (can be null).
     * @return A list of ProducerKPIMappingDetailsPojo objects.
     */
    public List<ProducerKPIMappingDetailsPojo> getProducerKPIMappingDetails(int accountId, Integer producerId) {
        String query = "select mpkm.producer_id producerId, mkd.name kpiName, mct.name componentTypeName, mc.name componentName," +
            " mcv.name componentVersionName from (((mst_producer_kpi_mapping mpkm join mst_component mc on mpkm.mst_component_id = mc.id)" +
            " join mst_component_version mcv on mpkm.mst_component_version_id = mcv.id) join mst_component_type mct on" +
            " mpkm.mst_component_type_id = mct.id) join mst_kpi_details mkd on mpkm.mst_kpi_details_id = mkd.id where" +
            " mpkm.account_id in (1, ?)";

        if (producerId != null) {
            query += " and producer_id = " + producerId;
        }

        return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(ProducerKPIMappingDetailsPojo.class), accountId);
    }

    /**
     * Retrieves the mapped KPIs for a producer using a single query.
     *
     * @param accountId  The ID of the account.
     * @param producerId The ID of the producer.
     * @return A ProducerKpiMappingPojo object containing the mapped KPIs.
     */
    public Page<com.heal.controlcenter.pojo.ProducerMappedKpiDetailsPojo> getProducerMappedKpis(int accountId, int producerId, Pageable pageable) {
        String kpiQuery =
                "SELECT mpkm.producer_id AS producerId, mkd.name AS kpiName, mct.name AS componentTypeName, " +
                        "mc.name AS componentName, mpkm.is_default AS isDefault, mcv.name AS componentVersionName " +
                        "FROM (((mst_producer_kpi_mapping mpkm " +
                        "JOIN mst_component mc ON mpkm.mst_component_id = mc.id) " +
                        "JOIN mst_component_version mcv ON mpkm.mst_component_version_id = mcv.id) " +
                        "JOIN mst_component_type mct ON mpkm.mst_component_type_id = mct.id) " +
                        "JOIN mst_kpi_details mkd ON mpkm.mst_kpi_details_id = mkd.id " +
                        "WHERE mpkm.account_id IN (1, ?) AND mpkm.producer_id = ? " +
                        "LIMIT ? OFFSET ?";

        List<com.heal.controlcenter.pojo.ProducerMappedKpiDetailsPojo> mappedKpis = jdbcTemplate.query(
                kpiQuery,
                (rs, rowNum) -> {
                    com.heal.controlcenter.pojo.ProducerMappedKpiDetailsPojo pojo = new com.heal.controlcenter.pojo.ProducerMappedKpiDetailsPojo();
                    pojo.setProducerId(rs.getInt("producerId"));
                    pojo.setKpiName(rs.getString("kpiName"));
                    pojo.setComponentTypeName(rs.getString("componentTypeName"));
                    pojo.setComponentName(rs.getString("componentName"));
                    pojo.setIsDefault(rs.getInt("isDefault"));
                    pojo.setComponentVersionName(rs.getString("componentVersionName"));
                    return pojo;
                },
                accountId, producerId, pageable.getPageSize(), pageable.getOffset()
        );

        int totalCount = getProducerMappedKpisCount(accountId, producerId);

        return new PageImpl<>(mappedKpis, pageable, totalCount);
    }

    /**
     * Retrieves the total count of mapped KPIs for a given producer.
     *
     * @param accountId  The ID of the account.
     * @param producerId The ID of the producer.
     * @return The total number of mapped KPIs.
     */
    public int getProducerMappedKpisCount(int accountId, int producerId) {
        String countQuery = "SELECT COUNT(*) FROM mst_producer_kpi_mapping mpkm " +
                            "WHERE mpkm.account_id IN (1, ?) AND mpkm.producer_id = ?";
        return jdbcTemplate.queryForObject(countQuery, Integer.class, accountId, producerId);
    }

    /**
     * Retrieves the name of a producer by its ID.
     *
     * @param producerId The ID of the producer.
     * @return The name of the producer.
     */
    public String getProducerNameById(int producerId) {
        String query = "SELECT name FROM mst_producer WHERE id = ?";
        return jdbcTemplate.queryForObject(query, String.class, producerId);
    }
}