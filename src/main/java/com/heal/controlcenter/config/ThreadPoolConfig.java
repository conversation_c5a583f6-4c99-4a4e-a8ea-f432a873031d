package com.heal.controlcenter.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ThreadPoolConfig {

    @Bean("ThreadPoolTaskExecutorCacheInitialization")
    public ThreadPoolTaskExecutor threadPoolTaskExecutorCacheInitialization() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("CacheInit-");
        executor.initialize();
        return executor;
    }

    @Bean("ThreadPoolTaskExecutorCacheRefreshScheduler")
    public ThreadPoolTaskExecutor threadPoolTaskExecutorCacheRefreshScheduler() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(25);
        executor.setThreadNamePrefix("CacheRefresh-");
        executor.initialize();
        return executor;
    }

    @Bean("ThreadPoolTaskExecutorAPIs")
    public ThreadPoolTaskExecutor threadPoolTaskExecutorAPIs() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(40);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("API-");
        executor.initialize();
        return executor;
    }
}
