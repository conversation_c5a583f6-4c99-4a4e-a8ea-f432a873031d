package com.heal.controlcenter.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import io.swagger.v3.oas.models.examples.Example;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class OpenApiComponentsConfig {

    @Bean
    public io.swagger.v3.oas.models.OpenAPI baseOpenAPI() {
        io.swagger.v3.oas.models.Components components = new io.swagger.v3.oas.models.Components()
                .addResponses("BadRequest", new io.swagger.v3.oas.models.responses.ApiResponse()
                        .description("Bad Request - Invalid input parameters, malformed request data, or validation failures")
                        .content(new io.swagger.v3.oas.models.media.Content()
                                .addMediaType("application/json", createBadRequestMediaType())))
                .addResponses("Unauthorized", new io.swagger.v3.oas.models.responses.ApiResponse()
                        .description("Unauthorized - Invalid, missing, or expired authentication token")
                        .content(new io.swagger.v3.oas.models.media.Content()
                                .addMediaType("application/json", createUnauthorizedMediaType())))
                .addResponses("ServerError", new io.swagger.v3.oas.models.responses.ApiResponse()
                        .description("Internal Server Error - Unexpected server-side error occurred during request processing")
                        .content(new io.swagger.v3.oas.models.media.Content()
                                .addMediaType("application/json", createServerErrorMediaType())));
        return new io.swagger.v3.oas.models.OpenAPI().components(components);
    }

    private io.swagger.v3.oas.models.media.MediaType createBadRequestMediaType() {
        io.swagger.v3.oas.models.media.MediaType mediaType = new io.swagger.v3.oas.models.media.MediaType();
        mediaType.setSchema(new io.swagger.v3.oas.models.media.Schema<>()
                .$ref("#/components/schemas/ErrorResponse"));
        Map<String, Example> examples = new HashMap<>();
        examples.put("default", new Example().value("{\"timestamp\": \"2025-08-11T12:34:56.789+00:00\", \"status\": 400, \"error\": \"Bad Request\", \"message\": \"Invalid input for field 'paramName': must be a number.\", \"path\": \"/api/v1/resource\"}"));
        mediaType.setExamples(examples);
        return mediaType;
    }

    private io.swagger.v3.oas.models.media.MediaType createUnauthorizedMediaType() {
        io.swagger.v3.oas.models.media.MediaType mediaType = new io.swagger.v3.oas.models.media.MediaType();
        mediaType.setSchema(new io.swagger.v3.oas.models.media.Schema<>()
                .$ref("#/components/schemas/ErrorResponse"));
        Map<String, Example> examples = new HashMap<>();
        examples.put("default", new Example().value("{\"timestamp\": \"2025-08-11T12:34:56.789+00:00\", \"status\": 401, \"error\": \"Unauthorized\", \"message\": \"Full authentication is required to access this resource.\", \"path\": \"/api/v1/resource\"}"));
        mediaType.setExamples(examples);
        return mediaType;
    }

    private io.swagger.v3.oas.models.media.MediaType createServerErrorMediaType() {
        io.swagger.v3.oas.models.media.MediaType mediaType = new io.swagger.v3.oas.models.media.MediaType();
        mediaType.setSchema(new io.swagger.v3.oas.models.media.Schema<>()
                .$ref("#/components/schemas/ErrorResponse"));
        Map<String, Example> examples = new HashMap<>();
        examples.put("default", new Example().value("{\"timestamp\": \"2025-08-11T12:34:56.789+00:00\", \"status\": 500, \"error\": \"Internal Server Error\", \"message\": \"An unexpected error occurred. Please try again later.\", \"path\": \"/api/v1/resource\"}"));
        mediaType.setExamples(examples);
        return mediaType;
    }

}



