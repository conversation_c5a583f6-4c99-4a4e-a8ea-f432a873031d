package com.heal.controlcenter.config;

import com.heal.controlcenter.swagger.ErrorResponse;
import com.heal.controlcenter.swagger.SuccessResponse;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotatedElementUtils;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.responses.ApiResponse;
import io.swagger.v3.oas.models.responses.ApiResponses;

@Configuration
public class OpenApiCustomization {

    @Bean
    OperationCustomizer applyCustomAnnotations() {
        return (operation, handlerMethod) -> {
            // Parameterized success response
            SuccessResponse success = AnnotatedElementUtils.findMergedAnnotation(
                    handlerMethod.getMethod(), SuccessResponse.class);
            if (success != null) {
                ApiResponses responses = operation.getResponses();
                ApiResponse ok = new ApiResponse().description(success.description());
                io.swagger.v3.oas.models.media.Content content = new io.swagger.v3.oas.models.media.Content();
                MediaType mt = new MediaType();

                // Let springdoc compute schema from class:
                Schema<?> schema = new Schema<>().$ref("#/components/schemas/" + success.schema().getSimpleName());
                mt.setSchema(schema);
                content.addMediaType(success.mediaType(), mt);
                ok.setContent(content);

                responses.addApiResponse(success.code(), ok);
            }

            // Common errors (parameterized which ones to include)
            ErrorResponse common = AnnotatedElementUtils.findMergedAnnotation(
                    handlerMethod.getMethod(), ErrorResponse.class);
            if (common == null) {
                // fall back to class-level if present
                common = AnnotatedElementUtils.findMergedAnnotation(
                        handlerMethod.getBeanType(), ErrorResponse.class);
            }
            if (common != null) {
                ApiResponses responses = operation.getResponses();
                if (common.badRequest())   responses.addApiResponse("400", new ApiResponse().$ref("#/components/responses/BadRequest")
                        .description("Bad Request - Invalid input parameters, malformed request data, or validation failures"));
                if (common.unauthorized()) responses.addApiResponse("401", new ApiResponse().$ref("#/components/responses/Unauthorized")
                        .description("Unauthorized - Invalid, missing, or expired authentication token"));
                if (common.serverError())  responses.addApiResponse("500", new ApiResponse().$ref("#/components/responses/ServerError")
                        .description("Internal Server Error - Unexpected server-side error occurred during request processing"));
            }

            return operation;
        };
    }
}
