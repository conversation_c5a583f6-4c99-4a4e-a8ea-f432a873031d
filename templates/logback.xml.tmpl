<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-controlcenter.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <FileNamePattern>/tmp/logs/heal-controlcenter.%i.log</FileNamePattern>
            <MinIndex>1</MinIndex>
            <MaxIndex>{{ key "service/controlcenter/log/maxfiles"}}</MaxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>{{ key "service/controlcenter/log/maxfilesize"}}</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-controlcenter-core.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <FileNamePattern>/tmp/logs/heal-controlcenter-core.%i.log</FileNamePattern>
            <MinIndex>1</MinIndex>
            <MaxIndex>{{ key "service/controlcenter/log/root/maxfiles"}}</MaxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>{{ key "service/controlcenter/log/root/maxfilesize"}}</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="STATS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-controlcenter-stats.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <FileNamePattern>/tmp/logs/heal-controlcenter-stats.%i.log</FileNamePattern>
            <MinIndex>1</MinIndex>
            <MaxIndex>{{ key "service/controlcenter/log/stats/maxfiles"}}</MaxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>{{ key "service/controlcenter/log/stats/maxfilesize"}}</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <logger name="com.appnomic.appsone.controlcenter" level="{{ key "service/controlcenter/log/mode"}}" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.appnomic.appsone.controlcenter.cache.CCCache" level="{{ key "service/controlcenter/log/stats/mode"}}" additivity="false">
        <appender-ref ref="STATS_FILE"/>
    </logger>

    <root level="{{ key "service/controlcenter/log/root/mode"}}">
        <appender-ref ref="CORE_FILE"/>
    </root>
</configuration>
