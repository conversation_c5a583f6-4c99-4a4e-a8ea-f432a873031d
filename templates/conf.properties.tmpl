#=====================================
# KEYCLOAK CONFIG
#KeyCloak parameters, these are used for session management
#=====================================
keycloak.ip={{ key "service/keycloak/hostname" }}
keycloak.port={{ key "service/keycloak/port/https" }}
keycloak.user={{ key "service/keycloak/username" }}
keycloak.pwd={{ key "service/keycloak/password" }}

#=====================================
# OPENSEARCH CONFIG
#=====================================
opensearch.connection.io.reactor.size={{ key "service/controlcenter/opensearch/connection/io/reactor/size" }}

#======================================
# Mysql Database Configuration
#=======================================
mysql.server.connection.url=jdbc:mysql://{{ key "service/perconadb/node1/ip" }}:{{ key "service/perconadb/node1/port" }}/appsone?{{ key "service/keycloak/jdbcparams" }}
mysql.database.username={{ key "service/perconadb/username" }}
mysql.database.password={{ key "service/perconadb/password_ui" }}
mysql.database.pool.size={{ key "service/controlcenter/perconadb/poolsize" }}
mysql.database.pool.size.max={{ key "service/controlcenter/perconadb/maxpoolsize" }}

# The below configuration is used for determining the delay window in pipeline to be assumed
# pipeline.problem.delay.minutes=10

#=======================================
# Message Queue Server Configurations
# By default, transaction-broker will use 127.0.0.1 as the message queue server ip
# As per property "mq.server.ssl.enabled", pipeline will use either 5672 or 5671 as the message queue server port.
# By default, "mq.server.ssl.enabled" is true and port is 5672, to disable ssl connection set "mq.server.ssl.enabled" to false.
# If no value is set for "mq.server.username" and "mq.server.password" then connection will be established as a guest user.
# By default, "mq.server.virtual.host" will be "/"
# Un-comment and edit the below properties to override.
#=======================================
mq.server.addresses={{ key "service/rabbitmq/addresses" }}
mq.server.virtual.host=/
mq.server.username={{ key "service/rabbitmq/username" }}
mq.server.password={{ key "service/rabbitmq/password/encrypted" }}
mq.server.ssl.enabled={{ key "service/rabbitmq/sslenable" }}
mq.server.ssl.protocol={{ key "service/rabbitmq/sslprotocol" }}

#=======================================
# Default Agent Mode Values
#=======================================
#commandMode=Auto
#autoSnapshotCollectionLevel=Medium
JVMCpuUtil={{ key "service/controlcenter/jvmcpuutil"}}
JVMMemUtil={{ key "service/controlcenter/jvmmemutil"}}

#Service_Breakage_Details time bracket
instance.health.bracket.min={{ key "service/controlcenter/healthinmin"}}

#Account wise keys will be generated
keys.generation.folder={{ key "service/controlcenter/keysgenerationfolder"}}

#=======================================
# Cache time out in minutes
#=======================================
cache.timeout={{ key "service/controlcenter/cacheTimeout"}}
config.cache.timeout={{ key "service/controlcenter/configCacheTimeout"}}

#============================
##Dormant User Configuration
user.dormant.creation.time.days = {{ key "service/controlcenter/userDormantCreationDays"}}
user.dormant.login.time.days = {{ key "service/controlcenter/userDormantLastLoginDays"}}
#=============================

instance.forensic.trigger.suppression.interval={{ key "service/pipeline/forensicbehaviourwindowsizeminute" }}
user.notification.forensic.suppression.interval={{ key "service/controlcenter/userForensicNotifAlertSuppressionIntervalMin" }}
http.handler.max.threads = {{ key "service/controlcenter/http/handler/threads" }}


#=======================================
# A1Health configurations
#=======================================
health.account.identifier={{ key "service/controlcenter/healthAccountIdentifier" }}

user.access.roles = {{ key "service/controlcenter/user/access/roles" }}
keystore.file.path={{key "service/controlcenter/keystore/path"}}
truststore.file.path={{key "service/controlcenter/truststore/path"}}
static.files.path={{key "service/controlcenter/frontend/path"}}
keystore.password={{key "service/controlcenter/keystore/password"}}
truststore.password={{key "service/controlcenter/truststore/password"}}

#=======================================
# Redis configuration
#=======================================
redis.hosts={{key "service/redis/nodes"}}
redis.ssl.enabled={{key "service/redis/sslenable"}}
redis.username={{key "service/redis/username"}}
redis.password={{key "service/redis/password/encrypted"}}
redis.cluster.mode= {{ key "service/redis/cluster/mode" }}
comp.instance.range={{key "service/controlcenter/instance/range"}}

#=======================================
# Data Receiver configuration
#=======================================
data.receiver.kpi.endpoint={{key "service/data/receiver/kpi/endpoint"}}
http.client.max.connections={{key "service/controlcenter/http/client/max/connections"}}
http.client.max.connections.per.route={{key "service/controlcenter/http/client/max/connections/per/route"}}
http.client.connection.timeout={{key "service/controlcenter/http/client/connections/timeout"}}
http.client.socket.timeout={{key "service/controlcenter/http/client/socket/timeout"}}

#=======================================
# Data communication details for component
# ======================================
haproxy.port = {{key "service/haproxy/grpc/port"}}
haproxy.ip = {{key "service/haproxy/ip"}}

#=======================================
#Connector Details
#=======================================
heal-ssl-enabled={{key "service/connector/ssl/enabled"}}
grpc-address={{key "service/grpc/address"}}
grpc-port={{key "service/grpc/port"}}
connector.cc.port={{key "service/controlcenter/port/https"}}
connector-percona-url=jdbc:mysql://{{ key "service/perconadb/node1/ip" }}:{{ key "service/perconadb/node1/port" }}/

health.metrics.scheduler.milliseconds={{key "service/controlcenter/health/metrics/loginterval/milliseconds"}}

command.out.scheduler.enable={{key "service/controlcenter/commandout/scheduler/enabled"}}
user.mapping.scheduler.enable={{key "service/controlcenter/usermapping/scheduler/enabled"}}
user.dormant.scheduler.enable={{key "service/controlcenter/userdormant/scheduler/enabled"}}
maintenance.window.scheduler.enable={{key "service/controlcenter/maintenancewindow/scheduler/enabled"}}
scheduler.bulk.operation.limit={{key "service/controlcenter/scheduler/bulk/operation/limit"}}

https.port={{key "service/controlcenter/server/port"}}

data.timezone={{ key "service/data/timezone" }}

#=======================================
# Txn Auto Acceptance Config Details
#=======================================
default.min.request.count={{key "service/controlcenter/default/min/request/count"}}
allowed.min.request.count={{key "service/controlcenter/allowed/min/request/count"}}
default.max.auto_accepted.requests={{key "service/controlcenter/default/max/autoaccepted/requests"}}
allowed.min.auto_accepted.requests={{key "service/controlcenter/allowed/min/autoaccepted/requests"}}
allowed.max.auto_accepted.requests={{key "service/controlcenter/allowed/max/autoaccepted/requests"}}
min.hold.duration={{key "service/controlcenter/min/hold/duration"}}
max.hold.duration={{key "service/controlcenter/max/hold/duration"}}
default.hold.duration={{key "service/controlcenter/default/hold/duration"}}
instance.kpi.availability={{key "service/host/availability/kpi/identifier"}}
fetch.os.data.accept.discard.txn.from.cc.duration.day={{key "service/controlcenter/fetchOSData/acceptDiscardTxn/duration/day"}}


#=======================================
# Heal Instance Delete Keys
#=======================================

instance.removal.status.percona={{key "service/controlcenter/instance/removal/status/percona"}}
instance.removal.status.opensearch={{key "service/controlcenter/instance/removal/status/opensearch"}}
instance.removal.opensearch.indexes={{key "service/controlcenter/instance/removal/opensearch/indexes"}}
delete.txn.by.query.os.batch.size={{key "service/controlcenter/delete/txn/os/batch/size"}}
delete.txn.by.query.os.time.out.minute={{key "service/controlcenter/delete/txn/query/os/timeout/minute"}}
redis.txn.keys.expiry.time.seconds={{key "service/controlcenter/delete/txn/redis/expiry/time/seconds"}}

kpi.aggrs.bucket.size={{key "service/controlcenter/kpi/aggs/bucket/size"}}
realtime.threshold.past.days={{key "service/controlcenter/kpi/lookback/days"}}

#=======================================
# Forensic Commands Configuration
#=======================================

forensic.command.retry.number={{key "service/controlcenter/command/retry/number"}}
forensic.command.supervisor.controller.ttl={{ key "service/controlcenter/supervior/controller/ttl"}}
instance.metadata.csv.file.headers={{key "service/controlcenter/instance/metadata/csv/file/headers"}}

host.availability.redis.key.expiry.minute={{key "service/host/availability/redis/key/expiry/minute"}}