# Component Instance Update API Conversion

## Overview
This document describes the complete conversion of the component instance update functionality from appsone-controlcenter to heal-controlcenter, following the original UpdateInstanceBL pattern and converting from JDBI to JDBC with Spring Boot.

## Original Implementation Analysis

### Source: appsone-controlcenter
- **Service**: `UpdateInstanceService.updateInstance()`
- **Business Logic**: `UpdateInstanceBL`
- **Route**: `put("/" + Constants.COMP_INSTANCE_IDENTIFIER, ControlCenterService.updateInstances, new JsonTransformer())`
- **Framework**: Spark Java with JDBI
- **POJO**: `UpdateInstancePojo`

### Key Features of Original Implementation:
1. **Instance Name Updates**: Update component instance names
2. **Environment Updates**: Change instance environment settings
3. **Service Mappings**: Add/Remove service mappings for instances
4. **Host Instance Validation**: Special validation for host instances
5. **Cluster Management**: Automatic cluster creation and mapping
6. **Redis Cache Updates**: Comprehensive Redis cache synchronization
7. **Agent Mapping Updates**: Update agent-instance relationships

## Converted Implementation

### Target: heal-controlcenter
- **Controller**: `ComponentInstanceController.updateComponentInstanceDetails()`
- **Business Logic**: `UpdateComponentInstanceBL`
- **Framework**: Spring Boot with JDBC
- **Path**: `PUT /api/accounts/{accountIdentifier}/instances/details`

## Implementation Details

### 1. Business Logic Class: UpdateComponentInstanceBL

**File**: `src/main/java/com/heal/controlcenter/businesslogic/UpdateComponentInstanceBL.java`

**Key Methods:**
- `clientValidation()`: Validates request format and basic rules
- `serverValidation()`: Validates business rules and data integrity
- `process()`: Executes the update operations in transaction
- `updateInstance()`: Core update logic for individual instances
- `updateRedisCache()`: Synchronizes Redis cache with database changes

**Features Implemented:**
- ✅ Instance name updates
- ✅ Environment updates with validation
- ✅ Application service mapping updates (Add/Remove)
- ✅ Host instance special validation
- ✅ Redis cache synchronization
- ✅ Agent mapping updates
- ✅ Transactional processing

### 2. POJO Class: UpdateInstancePojo

**File**: `src/main/java/com/heal/controlcenter/pojo/UpdateInstancePojo.java`

**Structure:**
```java
public class UpdateInstancePojo {
    private int instanceId;
    private String instanceIdentifier;
    private String name;
    private List<ApplicationServiceMapping> application;
    private String environment;
    private boolean hostInstance;
    
    // Nested classes for service mappings
    public static class ApplicationServiceMapping { ... }
    public static class IdAction { ... }
}
```

**Validation Rules:**
- Instance ID must be > 0
- Instance identifier cannot be empty
- Name length must be ≤ 128 characters
- Service actions must be "Add" or "Remove"
- Environment cannot be empty string

### 3. DAO Enhancements

**File**: `src/main/java/com/heal/controlcenter/dao/mysql/CompInstanceDao.java`

**Added Methods:**
- `getComponentInstanceByIdAndIdentifier()`: Retrieve instance by ID and identifier
- `getInstanceCountByHostAddressAndEnvironment()`: Validate host environment conflicts
- `updateInstanceName()`: Update instance name in database
- `updateInstanceEnvironment()`: Update instance environment
- `getAgentIdForInstance()`: Get associated agent ID

**File**: `src/main/java/com/heal/controlcenter/dao/mysql/MasterDataDao.java`

**Added Methods:**
- `getEnvironmentIdByName()`: Convert environment name to ID

### 4. Controller Endpoint

**File**: `src/main/java/com/heal/controlcenter/controller/ComponentInstanceController.java`

**Endpoint Details:**
- **Method**: PUT
- **Path**: `/api/accounts/{accountIdentifier}/instances/details`
- **Request Body**: `List<UpdateInstancePojo>`
- **Response**: `ResponsePojo<List<IdPojo>>`

**Features:**
- OpenAPI documentation with Swagger annotations
- Authentication integration via `BasicUserDetails`
- Comprehensive error handling
- Request/response logging

## Key Conversion Patterns

### 1. Framework Migration
- **From**: Spark Java → **To**: Spring Boot REST
- **From**: JDBI → **To**: JDBC with JdbcTemplate
- **From**: Manual transaction handling → **To**: Spring @Transactional

### 2. Validation Pattern
```java
// Original: Manual validation in BL
if (!data.isValid()) {
    throw new ClientException("Validation failed");
}

// Converted: Structured validation with UtilityBean
UtilityBean<List<UpdateInstancePojo>> utilityBean = 
    updateComponentInstanceBL.clientValidation(updateRequests, accountIdentifier, userDetails);
```

### 3. Database Operations
```java
// Original: JDBI Handle
dbi.inTransaction((conn, status) -> {
    // Database operations
    return null;
});

// Converted: Spring JDBC with @Transactional
@Transactional(rollbackFor = Exception.class)
public List<IdPojo> process(UtilityBean<List<UpdateInstancePojo>> utilityBean) {
    // Database operations using JdbcTemplate
}
```

### 4. Redis Cache Updates
```java
// Original: Direct Redis operations
instanceRepo.updateInstanceByIdentifier(accountIdentifier, instanceDetails);

// Converted: Same Redis operations maintained
instanceRepo.updateInstanceByIdentifier(accountIdentifier, instanceDetails);
instanceRepo.updateInstances(accountIdentifier, instanceDetailsList);
```

## Request/Response Examples

### Update Instance Name and Environment
```json
PUT /api/accounts/heal_health/instances/details
[
  {
    "instanceId": 123,
    "instanceIdentifier": "tomcat-instance-1",
    "name": "Updated Tomcat Instance",
    "environment": "production"
  }
]
```

### Add Service Mapping
```json
PUT /api/accounts/heal_health/instances/details
[
  {
    "instanceId": 123,
    "instanceIdentifier": "tomcat-instance-1",
    "application": [
      {
        "id": 456,
        "name": "Web Application",
        "identifier": "web-app",
        "service": [
          {
            "id": 789,
            "name": "Web Service",
            "identifier": "web-service",
            "action": "Add"
          }
        ]
      }
    ]
  }
]
```

### Remove Service Mapping
```json
PUT /api/accounts/heal_health/instances/details
[
  {
    "instanceId": 123,
    "instanceIdentifier": "tomcat-instance-1",
    "application": [
      {
        "id": 456,
        "name": "Web Application", 
        "identifier": "web-app",
        "service": [
          {
            "id": 789,
            "name": "Web Service",
            "identifier": "web-service",
            "action": "Remove"
          }
        ]
      }
    ]
  }
]
```

## Testing

**File**: `src/test/java/com/heal/controlcenter/controller/ComponentInstanceUpdateTest.java`

**Test Coverage:**
- Request validation (valid/invalid scenarios)
- Service action validation (Add/Remove)
- Environment validation
- Application service mapping validation
- Endpoint mapping verification

## Error Handling

**Client Errors (400):**
- Invalid instance ID or identifier
- Invalid service actions
- Empty required fields
- Invalid name length

**Server Errors (500):**
- Instance not found
- Environment conflicts for host instances
- Database operation failures
- Redis cache update failures

## Migration Benefits

1. **Consistency**: Follows established heal-controlcenter patterns
2. **Maintainability**: Uses Spring Boot dependency injection and configuration
3. **Performance**: Leverages Spring's transaction management and connection pooling
4. **Documentation**: Comprehensive OpenAPI documentation
5. **Testing**: Structured unit test coverage
6. **Error Handling**: Standardized exception handling across the application

## Usage Notes

1. **Backward Compatibility**: Maintains same request/response structure as original
2. **Redis Consistency**: Ensures database and cache remain synchronized
3. **Transaction Safety**: All operations are wrapped in database transactions
4. **Validation**: Comprehensive validation at both client and server levels
5. **Logging**: Detailed logging for debugging and monitoring

This implementation provides a complete, production-ready conversion of the component instance update functionality while maintaining all the features and business logic of the original appsone-controlcenter implementation.
