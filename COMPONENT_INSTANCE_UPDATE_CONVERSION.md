# Component Instance Update API Conversion

## Overview
This document describes the conversion of the Spark Java route `put("", ControlCenterService.updateInstanceDetails, new JsonTransformer());` to a Spring Boot REST endpoint.

## Original Implementation
- **Framework**: Spark Java
- **Route**: `put("", ControlCenterService.updateInstanceDetails, new JsonTransformer())`
- **Path**: `/api/accounts/{accountIdentifier}/instances`
- **Method**: PUT
- **Handler**: ControlCenterService.updateInstanceDetails (method not found in current codebase)

## Converted Implementation

### 1. Spring Boot Controller Method
**File**: `src/main/java/com/heal/controlcenter/controller/ComponentInstanceController.java`

```java
@PutMapping("accounts/{accountIdentifier}/instances")
public ResponseEntity<ResponsePojo<List<IdPojo>>> updateComponentInstances(
    @PathVariable("accountIdentifier") String accountIdentifier,
    @RequestBody List<ComponentInstancePojo> updateRequests,
    BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException
```

### 2. Key Features
- **Path Mapping**: `@PutMapping("accounts/{accountIdentifier}/instances")`
- **Request Body**: `List<ComponentInstancePojo>` - allows bulk updates
- **Response**: `ResponseEntity<ResponsePojo<List<IdPojo>>>` - returns updated instance IDs
- **Validation**: Uses existing `ComponentInstancePojo.updateValidate()` method
- **Authentication**: Integrated with existing `BasicUserDetails` injection

### 3. Business Logic Integration
The implementation reuses the existing `PostComponentInstanceBL` with modifications:

#### Modified Methods:
1. **serverValidation()**: Added `forceUpdate` flag detection from metadata
2. **addServerValidations()**: Added `forceUpdate` parameter
3. **validateAndGetComponentInstance()**: Added `forceUpdate` parameter  
4. **moreServerValidations()**: Modified to handle force updates
5. **addComponentInstance()**: Added `forceUpdate` parameter
6. **addComponentInstanceToDatabase()**: Added `forceUpdate` parameter
7. **processComponentInstances()**: Added `forceUpdate` parameter

#### Update Logic:
- When `forceUpdate=true`, existing active instances can be updated
- Sets `isUpdate=1` flag for existing instances
- Preserves existing instance ID for database updates
- Bypasses normal "already exists" validation errors

### 4. Validation Rules
Following the existing `ComponentInstancePojo.updateValidate()` method:

**Allowed Updates:**
- `attributes` - Component instance attributes
- `serviceIdentifiers` - Service mappings

**Restricted Fields:**
- `name`, `componentName`, `componentVersion`, `commonVersion`
- `parentIdentifier`, `parentName`, `agentIdentifiers`

**Validation Errors:**
- Both attributes and serviceIdentifiers cannot be empty
- Cannot update restricted fields
- Empty arrays are not allowed

### 5. Database Operations
- **Create**: Uses `componentInstanceDao.addComponentInstance()`
- **Update**: Uses `componentInstanceDao.updateComponentInstance()`
- **Redis**: Updates both database and Redis cache via `InstanceRepo`

### 6. Error Handling
- **Client Validation**: Validates request format and field restrictions
- **Server Validation**: Validates business rules and data integrity
- **Exception Handling**: Uses existing exception hierarchy
  - `ClientException` for validation errors
  - `ServerException` for business logic errors  
  - `DataProcessingException` for processing errors

### 7. Response Format
```json
{
  "data": [
    {
      "id": 123,
      "name": "instance-name",
      "identifier": "instance-identifier"
    }
  ],
  "message": "Component Instance(s) updated successfully.",
  "status": "OK"
}
```

## Testing
Created test file: `src/test/java/com/heal/controlcenter/controller/ComponentInstanceControllerUpdateTest.java`

**Test Coverage:**
- Validation for allowed update fields
- Validation errors for restricted fields
- Empty request validation
- Endpoint mapping verification

## Migration Benefits
1. **Consistency**: Uses same patterns as other Spring Boot endpoints
2. **Reusability**: Leverages existing business logic and validation
3. **Maintainability**: Follows established controller-BL-DAO pattern
4. **Security**: Integrates with existing authentication/authorization
5. **Documentation**: Includes OpenAPI annotations for API documentation

## Usage Example
```bash
curl -X PUT "http://localhost:8080/api/accounts/heal_health/instances" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '[
    {
      "identifier": "tomcat-instance-1",
      "attributes": [
        {
          "name": "port",
          "value": "8080"
        }
      ],
      "serviceIdentifiers": ["web-service"]
    }
  ]'
```

## Notes
- The original `ControlCenterService.updateInstanceDetails` method was not found in the current codebase
- Implementation assumes similar functionality to the existing POST endpoint but with update semantics
- Maintains backward compatibility with existing data structures and validation rules
- Uses transactional processing to ensure data consistency
